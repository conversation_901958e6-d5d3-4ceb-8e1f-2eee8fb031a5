This is make.info, produced by makeinfo version 4.8 from make.texi.

   This file documents the GNU `make' utility, which determines
automatically which pieces of a large program need to be recompiled,
and issues the commands to recompile them.

   This is Edition 0.70, last updated 1 April 2006, of `The GNU Make
Manual', for GNU `make' version 3.81.

   Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996,
1997, 1998, 1999, 2000, 2002, 2003, 2004, 2005, 2006 Free Software
Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have freedom to copy and
     modify this GNU Manual, like GNU software.  Copies published by
     the Free Software Foundation raise funds for GNU development."

INFO-DIR-SECTION GNU Packages
START-INFO-DIR-ENTRY
* Make: (make).            Remake files automatically.
END-INFO-DIR-ENTRY


Indirect:
make.info-1: 1297
make.info-2: 301265

Tag Table:
(Indirect)
Node: Top1297
Node: Overview14702
Node: Preparing15712
Node: Reading16684
Node: Bugs17611
Node: Introduction19441
Node: Rule Introduction21033
Node: Simple Makefile22777
Node: How Make Works26406
Node: Variables Simplify29061
Node: make Deduces31267
Node: Combine By Prerequisite33007
Node: Cleanup34036
Node: Makefiles35455
Node: Makefile Contents36421
Node: Makefile Names39376
Node: Include40987
Ref: Include-Footnote-144619
Node: MAKEFILES Variable44753
Node: MAKEFILE_LIST Variable46263
Node: Special Variables47531
Node: Remaking Makefiles51038
Node: Overriding Makefiles55287
Node: Reading Makefiles57340
Node: Secondary Expansion60244
Node: Rules67678
Node: Rule Example70350
Node: Rule Syntax71207
Node: Prerequisite Types73710
Node: Wildcards75486
Node: Wildcard Examples77204
Node: Wildcard Pitfall78460
Node: Wildcard Function80249
Node: Directory Search82033
Node: General Search83175
Node: Selective Search84890
Node: Search Algorithm87878
Node: Commands/Search90397
Node: Implicit/Search91743
Node: Libraries/Search92687
Node: Phony Targets94779
Node: Force Targets99865
Node: Empty Targets100910
Node: Special Targets102208
Node: Multiple Targets109382
Node: Multiple Rules111257
Node: Static Pattern113493
Node: Static Usage114145
Node: Static versus Implicit117866
Node: Double-Colon119610
Node: Automatic Prerequisites121267
Node: Commands125545
Node: Command Syntax126753
Node: Splitting Lines128778
Node: Variables in Commands131759
Node: Echoing133086
Node: Execution134378
Ref: Execution-Footnote-1135629
Node: Choosing the Shell135775
Node: Parallel139744
Node: Errors143337
Node: Interrupts146983
Node: Recursion148570
Node: MAKE Variable150664
Node: Variables/Recursion152931
Node: Options/Recursion158372
Node: -w Option163537
Node: Sequences164532
Node: Empty Commands167544
Node: Using Variables168718
Node: Reference171831
Node: Flavors173390
Node: Advanced179128
Node: Substitution Refs179633
Node: Computed Names181186
Node: Values185730
Node: Setting186643
Node: Appending188679
Node: Override Directive192605
Node: Defining193989
Node: Environment196453
Node: Target-specific198702
Node: Pattern-specific201669
Node: Conditionals203071
Node: Conditional Example203781
Node: Conditional Syntax206358
Node: Testing Flags212083
Node: Functions213185
Node: Syntax of Functions214605
Node: Text Functions216804
Node: File Name Functions225375
Node: Conditional Functions230597
Node: Foreach Function232971
Node: Call Function236183
Node: Value Function239068
Node: Eval Function240505
Node: Origin Function242779
Node: Flavor Function245997
Node: Shell Function247063
Node: Make Control Functions248697
Node: Running250366
Node: Makefile Arguments252355
Node: Goals253071
Node: Instead of Execution257812
Node: Avoiding Compilation261098
Node: Overriding263073
Node: Testing265371
Node: Options Summary267256
Node: Implicit Rules277382
Node: Using Implicit279530
Node: Catalogue of Rules283069
Node: Implicit Variables292419
Node: Chained Rules297254
Node: Pattern Rules301265
Node: Pattern Intro302801
Node: Pattern Examples305698
Node: Automatic Variables307507
Node: Pattern Match314878
Node: Match-Anything Rules316514
Node: Canceling Rules320389
Node: Last Resort321105
Node: Suffix Rules322952
Node: Implicit Rule Search326681
Node: Archives330200
Node: Archive Members330898
Node: Archive Update332511
Node: Archive Symbols334425
Node: Archive Pitfalls335659
Node: Archive Suffix Rules336382
Node: Features337929
Node: Missing346484
Node: Makefile Conventions350222
Node: Makefile Basics351008
Node: Utilities in Makefiles354175
Node: Command Variables356313
Node: Directory Variables359883
Node: Standard Targets374023
Ref: Standard Targets-Footnote-1387142
Node: Install Command Categories387242
Node: Quick Reference391768
Node: Error Messages402464
Node: Complex Makefile410154
Node: GNU Free Documentation License418872
Node: Concept Index441321
Node: Name Index506510

End Tag Table
