This is make.info, produced by makeinfo version 4.8 from make.texi.

   This file documents the GNU `make' utility, which determines
automatically which pieces of a large program need to be recompiled,
and issues the commands to recompile them.

   This is Edition 0.70, last updated 1 April 2006, of `The GNU Make
Manual', for GNU `make' version 3.81.

   Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996,
1997, 1998, 1999, 2000, 2002, 2003, 2004, 2005, 2006 Free Software
Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have freedom to copy and
     modify this GNU Manual, like GNU software.  Copies published by
     the Free Software Foundation raise funds for GNU development."

INFO-DIR-SECTION GNU Packages
START-INFO-DIR-ENTRY
* Make: (make).            Remake files automatically.
END-INFO-DIR-ENTRY


File: make.info,  Node: Pattern Rules,  Next: Last Resort,  Prev: Chained Rules,  Up: Implicit Rules

10.5 Defining and Redefining Pattern Rules
==========================================

You define an implicit rule by writing a "pattern rule".  A pattern
rule looks like an ordinary rule, except that its target contains the
character `%' (exactly one of them).  The target is considered a
pattern for matching file names; the `%' can match any nonempty
substring, while other characters match only themselves.  The
prerequisites likewise use `%' to show how their names relate to the
target name.

   Thus, a pattern rule `%.o : %.c' says how to make any file `STEM.o'
from another file `STEM.c'.

   Note that expansion using `%' in pattern rules occurs *after* any
variable or function expansions, which take place when the makefile is
read.  *Note How to Use Variables: Using Variables, and *Note Functions
for Transforming Text: Functions.

* Menu:

* Pattern Intro::               An introduction to pattern rules.
* Pattern Examples::            Examples of pattern rules.
* Automatic Variables::         How to use automatic variables in the
                                  commands of implicit rules.
* Pattern Match::               How patterns match.
* Match-Anything Rules::        Precautions you should take prior to
                                  defining rules that can match any
                                  target file whatever.
* Canceling Rules::             How to override or cancel built-in rules.


File: make.info,  Node: Pattern Intro,  Next: Pattern Examples,  Prev: Pattern Rules,  Up: Pattern Rules

10.5.1 Introduction to Pattern Rules
------------------------------------

A pattern rule contains the character `%' (exactly one of them) in the
target; otherwise, it looks exactly like an ordinary rule.  The target
is a pattern for matching file names; the `%' matches any nonempty
substring, while other characters match only themselves.  

   For example, `%.c' as a pattern matches any file name that ends in
`.c'.  `s.%.c' as a pattern matches any file name that starts with
`s.', ends in `.c' and is at least five characters long.  (There must
be at least one character to match the `%'.)  The substring that the
`%' matches is called the "stem".

   `%' in a prerequisite of a pattern rule stands for the same stem
that was matched by the `%' in the target.  In order for the pattern
rule to apply, its target pattern must match the file name under
consideration and all of its prerequisites (after pattern substitution)
must name files that exist or can be made.  These files become
prerequisites of the target.  

   Thus, a rule of the form

     %.o : %.c ; COMMAND...

specifies how to make a file `N.o', with another file `N.c' as its
prerequisite, provided that `N.c' exists or can be made.

   There may also be prerequisites that do not use `%'; such a
prerequisite attaches to every file made by this pattern rule.  These
unvarying prerequisites are useful occasionally.

   A pattern rule need not have any prerequisites that contain `%', or
in fact any prerequisites at all.  Such a rule is effectively a general
wildcard.  It provides a way to make any file that matches the target
pattern.  *Note Last Resort::.

   Pattern rules may have more than one target.  Unlike normal rules,
this does not act as many different rules with the same prerequisites
and commands.  If a pattern rule has multiple targets, `make' knows that
the rule's commands are responsible for making all of the targets.  The
commands are executed only once to make all the targets.  When searching
for a pattern rule to match a target, the target patterns of a rule
other than the one that matches the target in need of a rule are
incidental: `make' worries only about giving commands and prerequisites
to the file presently in question.  However, when this file's commands
are run, the other targets are marked as having been updated themselves.  

   The order in which pattern rules appear in the makefile is important
since this is the order in which they are considered.  Of equally
applicable rules, only the first one found is used.  The rules you
write take precedence over those that are built in.  Note however, that
a rule whose prerequisites actually exist or are mentioned always takes
priority over a rule with prerequisites that must be made by chaining
other implicit rules.  


File: make.info,  Node: Pattern Examples,  Next: Automatic Variables,  Prev: Pattern Intro,  Up: Pattern Rules

10.5.2 Pattern Rule Examples
----------------------------

Here are some examples of pattern rules actually predefined in `make'.
First, the rule that compiles `.c' files into `.o' files:

     %.o : %.c
             $(CC) -c $(CFLAGS) $(CPPFLAGS) $< -o $@

defines a rule that can make any file `X.o' from `X.c'.  The command
uses the automatic variables `$@' and `$<' to substitute the names of
the target file and the source file in each case where the rule applies
(*note Automatic Variables::).

   Here is a second built-in rule:

     % :: RCS/%,v
             $(CO) $(COFLAGS) $<

defines a rule that can make any file `X' whatsoever from a
corresponding file `X,v' in the subdirectory `RCS'.  Since the target
is `%', this rule will apply to any file whatever, provided the
appropriate prerequisite file exists.  The double colon makes the rule
"terminal", which means that its prerequisite may not be an intermediate
file (*note Match-Anything Pattern Rules: Match-Anything Rules.).

   This pattern rule has two targets:

     %.tab.c %.tab.h: %.y
             bison -d $<

This tells `make' that the command `bison -d X.y' will make both
`X.tab.c' and `X.tab.h'.  If the file `foo' depends on the files
`parse.tab.o' and `scan.o' and the file `scan.o' depends on the file
`parse.tab.h', when `parse.y' is changed, the command `bison -d parse.y'
will be executed only once, and the prerequisites of both `parse.tab.o'
and `scan.o' will be satisfied.  (Presumably the file `parse.tab.o'
will be recompiled from `parse.tab.c' and the file `scan.o' from
`scan.c', while `foo' is linked from `parse.tab.o', `scan.o', and its
other prerequisites, and it will execute happily ever after.)


File: make.info,  Node: Automatic Variables,  Next: Pattern Match,  Prev: Pattern Examples,  Up: Pattern Rules

10.5.3 Automatic Variables
--------------------------

Suppose you are writing a pattern rule to compile a `.c' file into a
`.o' file: how do you write the `cc' command so that it operates on the
right source file name?  You cannot write the name in the command,
because the name is different each time the implicit rule is applied.

   What you do is use a special feature of `make', the "automatic
variables".  These variables have values computed afresh for each rule
that is executed, based on the target and prerequisites of the rule.
In this example, you would use `$@' for the object file name and `$<'
for the source file name.

   It's very important that you recognize the limited scope in which
automatic variable values are available: they only have values within
the command script.  In particular, you cannot use them anywhere within
the target list of a rule; they have no value there and will expand to
the empty string.  Also, they cannot be accessed directly within the
prerequisite list of a rule.  A common mistake is attempting to use
`$@' within the prerequisites list; this will not work.  However, there
is a special feature of GNU `make', secondary expansion (*note
Secondary Expansion::), which will allow automatic variable values to
be used in prerequisite lists.

   Here is a table of automatic variables:

`$@'
     The file name of the target of the rule.  If the target is an
     archive member, then `$@' is the name of the archive file.  In a
     pattern rule that has multiple targets (*note Introduction to
     Pattern Rules: Pattern Intro.), `$@' is the name of whichever
     target caused the rule's commands to be run.

`$%'
     The target member name, when the target is an archive member.
     *Note Archives::.  For example, if the target is `foo.a(bar.o)'
     then `$%' is `bar.o' and `$@' is `foo.a'.  `$%' is empty when the
     target is not an archive member.

`$<'
     The name of the first prerequisite.  If the target got its
     commands from an implicit rule, this will be the first
     prerequisite added by the implicit rule (*note Implicit Rules::).

`$?'
     The names of all the prerequisites that are newer than the target,
     with spaces between them.  For prerequisites which are archive
     members, only the member named is used (*note Archives::).  

`$^'
     The names of all the prerequisites, with spaces between them.  For
     prerequisites which are archive members, only the member named is
     used (*note Archives::).  A target has only one prerequisite on
     each other file it depends on, no matter how many times each file
     is listed as a prerequisite.  So if you list a prerequisite more
     than once for a target, the value of `$^' contains just one copy
     of the name.  This list does *not* contain any of the order-only
     prerequisites; for those see the `$|' variable, below.  

`$+'
     This is like `$^', but prerequisites listed more than once are
     duplicated in the order they were listed in the makefile.  This is
     primarily useful for use in linking commands where it is
     meaningful to repeat library file names in a particular order.

`$|'
     The names of all the order-only prerequisites, with spaces between
     them.

`$*'
     The stem with which an implicit rule matches (*note How Patterns
     Match: Pattern Match.).  If the target is `dir/a.foo.b' and the
     target pattern is `a.%.b' then the stem is `dir/foo'.  The stem is
     useful for constructing names of related files.  

     In a static pattern rule, the stem is part of the file name that
     matched the `%' in the target pattern.

     In an explicit rule, there is no stem; so `$*' cannot be determined
     in that way.  Instead, if the target name ends with a recognized
     suffix (*note Old-Fashioned Suffix Rules: Suffix Rules.), `$*' is
     set to the target name minus the suffix.  For example, if the
     target name is `foo.c', then `$*' is set to `foo', since `.c' is a
     suffix.  GNU `make' does this bizarre thing only for compatibility
     with other implementations of `make'.  You should generally avoid
     using `$*' except in implicit rules or static pattern rules.

     If the target name in an explicit rule does not end with a
     recognized suffix, `$*' is set to the empty string for that rule.

   `$?' is useful even in explicit rules when you wish to operate on
only the prerequisites that have changed.  For example, suppose that an
archive named `lib' is supposed to contain copies of several object
files.  This rule copies just the changed object files into the archive:

     lib: foo.o bar.o lose.o win.o
             ar r lib $?

   Of the variables listed above, four have values that are single file
names, and three have values that are lists of file names.  These seven
have variants that get just the file's directory name or just the file
name within the directory.  The variant variables' names are formed by
appending `D' or `F', respectively.  These variants are semi-obsolete
in GNU `make' since the functions `dir' and `notdir' can be used to get
a similar effect (*note Functions for File Names: File Name
Functions.).  Note, however, that the `D' variants all omit the
trailing slash which always appears in the output of the `dir'
function.  Here is a table of the variants:

`$(@D)'
     The directory part of the file name of the target, with the
     trailing slash removed.  If the value of `$@' is `dir/foo.o' then
     `$(@D)' is `dir'.  This value is `.' if `$@' does not contain a
     slash.

`$(@F)'
     The file-within-directory part of the file name of the target.  If
     the value of `$@' is `dir/foo.o' then `$(@F)' is `foo.o'.  `$(@F)'
     is equivalent to `$(notdir $@)'.

`$(*D)'
`$(*F)'
     The directory part and the file-within-directory part of the stem;
     `dir' and `foo' in this example.

`$(%D)'
`$(%F)'
     The directory part and the file-within-directory part of the target
     archive member name.  This makes sense only for archive member
     targets of the form `ARCHIVE(MEMBER)' and is useful only when
     MEMBER may contain a directory name.  (*Note Archive Members as
     Targets: Archive Members.)

`$(<D)'
`$(<F)'
     The directory part and the file-within-directory part of the first
     prerequisite.

`$(^D)'
`$(^F)'
     Lists of the directory parts and the file-within-directory parts
     of all prerequisites.

`$(+D)'
`$(+F)'
     Lists of the directory parts and the file-within-directory parts
     of all prerequisites, including multiple instances of duplicated
     prerequisites.

`$(?D)'
`$(?F)'
     Lists of the directory parts and the file-within-directory parts of
     all prerequisites that are newer than the target.

   Note that we use a special stylistic convention when we talk about
these automatic variables; we write "the value of `$<'", rather than
"the variable `<'" as we would write for ordinary variables such as
`objects' and `CFLAGS'.  We think this convention looks more natural in
this special case.  Please do not assume it has a deep significance;
`$<' refers to the variable named `<' just as `$(CFLAGS)' refers to the
variable named `CFLAGS'.  You could just as well use `$(<)' in place of
`$<'.


File: make.info,  Node: Pattern Match,  Next: Match-Anything Rules,  Prev: Automatic Variables,  Up: Pattern Rules

10.5.4 How Patterns Match
-------------------------

A target pattern is composed of a `%' between a prefix and a suffix,
either or both of which may be empty.  The pattern matches a file name
only if the file name starts with the prefix and ends with the suffix,
without overlap.  The text between the prefix and the suffix is called
the "stem".  Thus, when the pattern `%.o' matches the file name
`test.o', the stem is `test'.  The pattern rule prerequisites are
turned into actual file names by substituting the stem for the character
`%'.  Thus, if in the same example one of the prerequisites is written
as `%.c', it expands to `test.c'.

   When the target pattern does not contain a slash (and it usually does
not), directory names in the file names are removed from the file name
before it is compared with the target prefix and suffix.  After the
comparison of the file name to the target pattern, the directory names,
along with the slash that ends them, are added on to the prerequisite
file names generated from the pattern rule's prerequisite patterns and
the file name.  The directories are ignored only for the purpose of
finding an implicit rule to use, not in the application of that rule.
Thus, `e%t' matches the file name `src/eat', with `src/a' as the stem.
When prerequisites are turned into file names, the directories from the
stem are added at the front, while the rest of the stem is substituted
for the `%'.  The stem `src/a' with a prerequisite pattern `c%r' gives
the file name `src/car'.


File: make.info,  Node: Match-Anything Rules,  Next: Canceling Rules,  Prev: Pattern Match,  Up: Pattern Rules

10.5.5 Match-Anything Pattern Rules
-----------------------------------

When a pattern rule's target is just `%', it matches any file name
whatever.  We call these rules "match-anything" rules.  They are very
useful, but it can take a lot of time for `make' to think about them,
because it must consider every such rule for each file name listed
either as a target or as a prerequisite.

   Suppose the makefile mentions `foo.c'.  For this target, `make'
would have to consider making it by linking an object file `foo.c.o',
or by C compilation-and-linking in one step from `foo.c.c', or by
Pascal compilation-and-linking from `foo.c.p', and many other
possibilities.

   We know these possibilities are ridiculous since `foo.c' is a C
source file, not an executable.  If `make' did consider these
possibilities, it would ultimately reject them, because files such as
`foo.c.o' and `foo.c.p' would not exist.  But these possibilities are so
numerous that `make' would run very slowly if it had to consider them.

   To gain speed, we have put various constraints on the way `make'
considers match-anything rules.  There are two different constraints
that can be applied, and each time you define a match-anything rule you
must choose one or the other for that rule.

   One choice is to mark the match-anything rule as "terminal" by
defining it with a double colon.  When a rule is terminal, it does not
apply unless its prerequisites actually exist.  Prerequisites that
could be made with other implicit rules are not good enough.  In other
words, no further chaining is allowed beyond a terminal rule.

   For example, the built-in implicit rules for extracting sources from
RCS and SCCS files are terminal; as a result, if the file `foo.c,v' does
not exist, `make' will not even consider trying to make it as an
intermediate file from `foo.c,v.o' or from `RCS/SCCS/s.foo.c,v'.  RCS
and SCCS files are generally ultimate source files, which should not be
remade from any other files; therefore, `make' can save time by not
looking for ways to remake them.

   If you do not mark the match-anything rule as terminal, then it is
nonterminal.  A nonterminal match-anything rule cannot apply to a file
name that indicates a specific type of data.  A file name indicates a
specific type of data if some non-match-anything implicit rule target
matches it.

   For example, the file name `foo.c' matches the target for the pattern
rule `%.c : %.y' (the rule to run Yacc).  Regardless of whether this
rule is actually applicable (which happens only if there is a file
`foo.y'), the fact that its target matches is enough to prevent
consideration of any nonterminal match-anything rules for the file
`foo.c'.  Thus, `make' will not even consider trying to make `foo.c' as
an executable file from `foo.c.o', `foo.c.c', `foo.c.p', etc.

   The motivation for this constraint is that nonterminal match-anything
rules are used for making files containing specific types of data (such
as executable files) and a file name with a recognized suffix indicates
some other specific type of data (such as a C source file).

   Special built-in dummy pattern rules are provided solely to recognize
certain file names so that nonterminal match-anything rules will not be
considered.  These dummy rules have no prerequisites and no commands,
and they are ignored for all other purposes.  For example, the built-in
implicit rule

     %.p :

exists to make sure that Pascal source files such as `foo.p' match a
specific target pattern and thereby prevent time from being wasted
looking for `foo.p.o' or `foo.p.c'.

   Dummy pattern rules such as the one for `%.p' are made for every
suffix listed as valid for use in suffix rules (*note Old-Fashioned
Suffix Rules: Suffix Rules.).


File: make.info,  Node: Canceling Rules,  Prev: Match-Anything Rules,  Up: Pattern Rules

10.5.6 Canceling Implicit Rules
-------------------------------

You can override a built-in implicit rule (or one you have defined
yourself) by defining a new pattern rule with the same target and
prerequisites, but different commands.  When the new rule is defined,
the built-in one is replaced.  The new rule's position in the sequence
of implicit rules is determined by where you write the new rule.

   You can cancel a built-in implicit rule by defining a pattern rule
with the same target and prerequisites, but no commands.  For example,
the following would cancel the rule that runs the assembler:

     %.o : %.s


File: make.info,  Node: Last Resort,  Next: Suffix Rules,  Prev: Pattern Rules,  Up: Implicit Rules

10.6 Defining Last-Resort Default Rules
=======================================

You can define a last-resort implicit rule by writing a terminal
match-anything pattern rule with no prerequisites (*note Match-Anything
Rules::).  This is just like any other pattern rule; the only thing
special about it is that it will match any target.  So such a rule's
commands are used for all targets and prerequisites that have no
commands of their own and for which no other implicit rule applies.

   For example, when testing a makefile, you might not care if the
source files contain real data, only that they exist.  Then you might
do this:

     %::
             touch $@

to cause all the source files needed (as prerequisites) to be created
automatically.

   You can instead define commands to be used for targets for which
there are no rules at all, even ones which don't specify commands.  You
do this by writing a rule for the target `.DEFAULT'.  Such a rule's
commands are used for all prerequisites which do not appear as targets
in any explicit rule, and for which no implicit rule applies.
Naturally, there is no `.DEFAULT' rule unless you write one.

   If you use `.DEFAULT' with no commands or prerequisites:

     .DEFAULT:

the commands previously stored for `.DEFAULT' are cleared.  Then `make'
acts as if you had never defined `.DEFAULT' at all.

   If you do not want a target to get the commands from a match-anything
pattern rule or `.DEFAULT', but you also do not want any commands to be
run for the target, you can give it empty commands (*note Defining
Empty Commands: Empty Commands.).

   You can use a last-resort rule to override part of another makefile.
*Note Overriding Part of Another Makefile: Overriding Makefiles.


File: make.info,  Node: Suffix Rules,  Next: Implicit Rule Search,  Prev: Last Resort,  Up: Implicit Rules

10.7 Old-Fashioned Suffix Rules
===============================

"Suffix rules" are the old-fashioned way of defining implicit rules for
`make'.  Suffix rules are obsolete because pattern rules are more
general and clearer.  They are supported in GNU `make' for
compatibility with old makefiles.  They come in two kinds:
"double-suffix" and "single-suffix".

   A double-suffix rule is defined by a pair of suffixes: the target
suffix and the source suffix.  It matches any file whose name ends with
the target suffix.  The corresponding implicit prerequisite is made by
replacing the target suffix with the source suffix in the file name.  A
two-suffix rule whose target and source suffixes are `.o' and `.c' is
equivalent to the pattern rule `%.o : %.c'.

   A single-suffix rule is defined by a single suffix, which is the
source suffix.  It matches any file name, and the corresponding implicit
prerequisite name is made by appending the source suffix.  A
single-suffix rule whose source suffix is `.c' is equivalent to the
pattern rule `% : %.c'.

   Suffix rule definitions are recognized by comparing each rule's
target against a defined list of known suffixes.  When `make' sees a
rule whose target is a known suffix, this rule is considered a
single-suffix rule.  When `make' sees a rule whose target is two known
suffixes concatenated, this rule is taken as a double-suffix rule.

   For example, `.c' and `.o' are both on the default list of known
suffixes.  Therefore, if you define a rule whose target is `.c.o',
`make' takes it to be a double-suffix rule with source suffix `.c' and
target suffix `.o'.  Here is the old-fashioned way to define the rule
for compiling a C source file:

     .c.o:
             $(CC) -c $(CFLAGS) $(CPPFLAGS) -o $@ $<

   Suffix rules cannot have any prerequisites of their own.  If they
have any, they are treated as normal files with funny names, not as
suffix rules.  Thus, the rule:

     .c.o: foo.h
             $(CC) -c $(CFLAGS) $(CPPFLAGS) -o $@ $<

tells how to make the file `.c.o' from the prerequisite file `foo.h',
and is not at all like the pattern rule:

     %.o: %.c foo.h
             $(CC) -c $(CFLAGS) $(CPPFLAGS) -o $@ $<

which tells how to make `.o' files from `.c' files, and makes all `.o'
files using this pattern rule also depend on `foo.h'.

   Suffix rules with no commands are also meaningless.  They do not
remove previous rules as do pattern rules with no commands (*note
Canceling Implicit Rules: Canceling Rules.).  They simply enter the
suffix or pair of suffixes concatenated as a target in the data base.

   The known suffixes are simply the names of the prerequisites of the
special target `.SUFFIXES'.  You can add your own suffixes by writing a
rule for `.SUFFIXES' that adds more prerequisites, as in:

     .SUFFIXES: .hack .win

which adds `.hack' and `.win' to the end of the list of suffixes.

   If you wish to eliminate the default known suffixes instead of just
adding to them, write a rule for `.SUFFIXES' with no prerequisites.  By
special dispensation, this eliminates all existing prerequisites of
`.SUFFIXES'.  You can then write another rule to add the suffixes you
want.  For example,

     .SUFFIXES:            # Delete the default suffixes
     .SUFFIXES: .c .o .h   # Define our suffix list

   The `-r' or `--no-builtin-rules' flag causes the default list of
suffixes to be empty.

   The variable `SUFFIXES' is defined to the default list of suffixes
before `make' reads any makefiles.  You can change the list of suffixes
with a rule for the special target `.SUFFIXES', but that does not alter
this variable.


File: make.info,  Node: Implicit Rule Search,  Prev: Suffix Rules,  Up: Implicit Rules

10.8 Implicit Rule Search Algorithm
===================================

Here is the procedure `make' uses for searching for an implicit rule
for a target T.  This procedure is followed for each double-colon rule
with no commands, for each target of ordinary rules none of which have
commands, and for each prerequisite that is not the target of any rule.
It is also followed recursively for prerequisites that come from
implicit rules, in the search for a chain of rules.

   Suffix rules are not mentioned in this algorithm because suffix
rules are converted to equivalent pattern rules once the makefiles have
been read in.

   For an archive member target of the form `ARCHIVE(MEMBER)', the
following algorithm is run twice, first using the entire target name T,
and second using `(MEMBER)' as the target T if the first run found no
rule.

  1. Split T into a directory part, called D, and the rest, called N.
     For example, if T is `src/foo.o', then D is `src/' and N is
     `foo.o'.

  2. Make a list of all the pattern rules one of whose targets matches
     T or N.  If the target pattern contains a slash, it is matched
     against T; otherwise, against N.

  3. If any rule in that list is _not_ a match-anything rule, then
     remove all nonterminal match-anything rules from the list.

  4. Remove from the list all rules with no commands.

  5. For each pattern rule in the list:

       a. Find the stem S, which is the nonempty part of T or N matched
          by the `%' in the target pattern.

       b. Compute the prerequisite names by substituting S for `%'; if
          the target pattern does not contain a slash, append D to the
          front of each prerequisite name.

       c. Test whether all the prerequisites exist or ought to exist.
          (If a file name is mentioned in the makefile as a target or
          as an explicit prerequisite, then we say it ought to exist.)

          If all prerequisites exist or ought to exist, or there are no
          prerequisites, then this rule applies.

  6. If no pattern rule has been found so far, try harder.  For each
     pattern rule in the list:

       a. If the rule is terminal, ignore it and go on to the next rule.

       b. Compute the prerequisite names as before.

       c. Test whether all the prerequisites exist or ought to exist.

       d. For each prerequisite that does not exist, follow this
          algorithm recursively to see if the prerequisite can be made
          by an implicit rule.

       e. If all prerequisites exist, ought to exist, or can be made by
          implicit rules, then this rule applies.

  7. If no implicit rule applies, the rule for `.DEFAULT', if any,
     applies.  In that case, give T the same commands that `.DEFAULT'
     has.  Otherwise, there are no commands for T.

   Once a rule that applies has been found, for each target pattern of
the rule other than the one that matched T or N, the `%' in the pattern
is replaced with S and the resultant file name is stored until the
commands to remake the target file T are executed.  After these
commands are executed, each of these stored file names are entered into
the data base and marked as having been updated and having the same
update status as the file T.

   When the commands of a pattern rule are executed for T, the automatic
variables are set corresponding to the target and prerequisites.  *Note
Automatic Variables::.


File: make.info,  Node: Archives,  Next: Features,  Prev: Implicit Rules,  Up: Top

11 Using `make' to Update Archive Files
***************************************

"Archive files" are files containing named subfiles called "members";
they are maintained with the program `ar' and their main use is as
subroutine libraries for linking.

* Menu:

* Archive Members::             Archive members as targets.
* Archive Update::              The implicit rule for archive member targets.
* Archive Pitfalls::            Dangers to watch out for when using archives.
* Archive Suffix Rules::        You can write a special kind of suffix rule
                                  for updating archives.


File: make.info,  Node: Archive Members,  Next: Archive Update,  Prev: Archives,  Up: Archives

11.1 Archive Members as Targets
===============================

An individual member of an archive file can be used as a target or
prerequisite in `make'.  You specify the member named MEMBER in archive
file ARCHIVE as follows:

     ARCHIVE(MEMBER)

This construct is available only in targets and prerequisites, not in
commands!  Most programs that you might use in commands do not support
this syntax and cannot act directly on archive members.  Only `ar' and
other programs specifically designed to operate on archives can do so.
Therefore, valid commands to update an archive member target probably
must use `ar'.  For example, this rule says to create a member `hack.o'
in archive `foolib' by copying the file `hack.o':

     foolib(hack.o) : hack.o
             ar cr foolib hack.o

   In fact, nearly all archive member targets are updated in just this
way and there is an implicit rule to do it for you.  *Please note:* The
`c' flag to `ar' is required if the archive file does not already exist.

   To specify several members in the same archive, you can write all the
member names together between the parentheses.  For example:

     foolib(hack.o kludge.o)

is equivalent to:

     foolib(hack.o) foolib(kludge.o)

   You can also use shell-style wildcards in an archive member
reference.  *Note Using Wildcard Characters in File Names: Wildcards.
For example, `foolib(*.o)' expands to all existing members of the
`foolib' archive whose names end in `.o'; perhaps `foolib(hack.o)
foolib(kludge.o)'.


File: make.info,  Node: Archive Update,  Next: Archive Pitfalls,  Prev: Archive Members,  Up: Archives

11.2 Implicit Rule for Archive Member Targets
=============================================

Recall that a target that looks like `A(M)' stands for the member named
M in the archive file A.

   When `make' looks for an implicit rule for such a target, as a
special feature it considers implicit rules that match `(M)', as well as
those that match the actual target `A(M)'.

   This causes one special rule whose target is `(%)' to match.  This
rule updates the target `A(M)' by copying the file M into the archive.
For example, it will update the archive member target `foo.a(bar.o)' by
copying the _file_ `bar.o' into the archive `foo.a' as a _member_ named
`bar.o'.

   When this rule is chained with others, the result is very powerful.
Thus, `make "foo.a(bar.o)"' (the quotes are needed to protect the `('
and `)' from being interpreted specially by the shell) in the presence
of a file `bar.c' is enough to cause the following commands to be run,
even without a makefile:

     cc -c bar.c -o bar.o
     ar r foo.a bar.o
     rm -f bar.o

Here `make' has envisioned the file `bar.o' as an intermediate file.
*Note Chains of Implicit Rules: Chained Rules.

   Implicit rules such as this one are written using the automatic
variable `$%'.  *Note Automatic Variables::.

   An archive member name in an archive cannot contain a directory
name, but it may be useful in a makefile to pretend that it does.  If
you write an archive member target `foo.a(dir/file.o)', `make' will
perform automatic updating with this command:

     ar r foo.a dir/file.o

which has the effect of copying the file `dir/file.o' into a member
named `file.o'.  In connection with such usage, the automatic variables
`%D' and `%F' may be useful.

* Menu:

* Archive Symbols::             How to update archive symbol directories.


File: make.info,  Node: Archive Symbols,  Prev: Archive Update,  Up: Archive Update

11.2.1 Updating Archive Symbol Directories
------------------------------------------

An archive file that is used as a library usually contains a special
member named `__.SYMDEF' that contains a directory of the external
symbol names defined by all the other members.  After you update any
other members, you need to update `__.SYMDEF' so that it will summarize
the other members properly.  This is done by running the `ranlib'
program:

     ranlib ARCHIVEFILE

   Normally you would put this command in the rule for the archive file,
and make all the members of the archive file prerequisites of that rule.
For example,

     libfoo.a: libfoo.a(x.o) libfoo.a(y.o) ...
             ranlib libfoo.a

The effect of this is to update archive members `x.o', `y.o', etc., and
then update the symbol directory member `__.SYMDEF' by running
`ranlib'.  The rules for updating the members are not shown here; most
likely you can omit them and use the implicit rule which copies files
into the archive, as described in the preceding section.

   This is not necessary when using the GNU `ar' program, which updates
the `__.SYMDEF' member automatically.


File: make.info,  Node: Archive Pitfalls,  Next: Archive Suffix Rules,  Prev: Archive Update,  Up: Archives

11.3 Dangers When Using Archives
================================

It is important to be careful when using parallel execution (the `-j'
switch; *note Parallel Execution: Parallel.) and archives.  If multiple
`ar' commands run at the same time on the same archive file, they will
not know about each other and can corrupt the file.

   Possibly a future version of `make' will provide a mechanism to
circumvent this problem by serializing all commands that operate on the
same archive file.  But for the time being, you must either write your
makefiles to avoid this problem in some other way, or not use `-j'.


File: make.info,  Node: Archive Suffix Rules,  Prev: Archive Pitfalls,  Up: Archives

11.4 Suffix Rules for Archive Files
===================================

You can write a special kind of suffix rule for dealing with archive
files.  *Note Suffix Rules::, for a full explanation of suffix rules.
Archive suffix rules are obsolete in GNU `make', because pattern rules
for archives are a more general mechanism (*note Archive Update::).
But they are retained for compatibility with other `make's.

   To write a suffix rule for archives, you simply write a suffix rule
using the target suffix `.a' (the usual suffix for archive files).  For
example, here is the old-fashioned suffix rule to update a library
archive from C source files:

     .c.a:
             $(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $*.o
             $(AR) r $@ $*.o
             $(RM) $*.o

This works just as if you had written the pattern rule:

     (%.o): %.c
             $(CC) $(CFLAGS) $(CPPFLAGS) -c $< -o $*.o
             $(AR) r $@ $*.o
             $(RM) $*.o

   In fact, this is just what `make' does when it sees a suffix rule
with `.a' as the target suffix.  Any double-suffix rule `.X.a' is
converted to a pattern rule with the target pattern `(%.o)' and a
prerequisite pattern of `%.X'.

   Since you might want to use `.a' as the suffix for some other kind
of file, `make' also converts archive suffix rules to pattern rules in
the normal way (*note Suffix Rules::).  Thus a double-suffix rule
`.X.a' produces two pattern rules: `(%.o): %.X' and `%.a: %.X'.


File: make.info,  Node: Features,  Next: Missing,  Prev: Archives,  Up: Top

12 Features of GNU `make'
*************************

Here is a summary of the features of GNU `make', for comparison with
and credit to other versions of `make'.  We consider the features of
`make' in 4.2 BSD systems as a baseline.  If you are concerned with
writing portable makefiles, you should not use the features of `make'
listed here, nor the ones in *Note Missing::.

   Many features come from the version of `make' in System V.

   * The `VPATH' variable and its special meaning.  *Note Searching
     Directories for Prerequisites: Directory Search.  This feature
     exists in System V `make', but is undocumented.  It is documented
     in 4.3 BSD `make' (which says it mimics System V's `VPATH'
     feature).

   * Included makefiles.  *Note Including Other Makefiles: Include.
     Allowing multiple files to be included with a single directive is
     a GNU extension.

   * Variables are read from and communicated via the environment.
     *Note Variables from the Environment: Environment.

   * Options passed through the variable `MAKEFLAGS' to recursive
     invocations of `make'.  *Note Communicating Options to a
     Sub-`make': Options/Recursion.

   * The automatic variable `$%' is set to the member name in an
     archive reference.  *Note Automatic Variables::.

   * The automatic variables `$@', `$*', `$<', `$%', and `$?' have
     corresponding forms like `$(@F)' and `$(@D)'.  We have generalized
     this to `$^' as an obvious extension.  *Note Automatic Variables::.

   * Substitution variable references.  *Note Basics of Variable
     References: Reference.

   * The command-line options `-b' and `-m', accepted and ignored.  In
     System V `make', these options actually do something.

   * Execution of recursive commands to run `make' via the variable
     `MAKE' even if `-n', `-q' or `-t' is specified.  *Note Recursive
     Use of `make': Recursion.

   * Support for suffix `.a' in suffix rules.  *Note Archive Suffix
     Rules::.  This feature is obsolete in GNU `make', because the
     general feature of rule chaining (*note Chains of Implicit Rules:
     Chained Rules.) allows one pattern rule for installing members in
     an archive (*note Archive Update::) to be sufficient.

   * The arrangement of lines and backslash-newline combinations in
     commands is retained when the commands are printed, so they appear
     as they do in the makefile, except for the stripping of initial
     whitespace.

   The following features were inspired by various other versions of
`make'.  In some cases it is unclear exactly which versions inspired
which others.

   * Pattern rules using `%'.  This has been implemented in several
     versions of `make'.  We're not sure who invented it first, but
     it's been spread around a bit.  *Note Defining and Redefining
     Pattern Rules: Pattern Rules.

   * Rule chaining and implicit intermediate files.  This was
     implemented by Stu Feldman in his version of `make' for AT&T
     Eighth Edition Research Unix, and later by Andrew Hume of AT&T
     Bell Labs in his `mk' program (where he terms it "transitive
     closure").  We do not really know if we got this from either of
     them or thought it up ourselves at the same time.  *Note Chains of
     Implicit Rules: Chained Rules.

   * The automatic variable `$^' containing a list of all prerequisites
     of the current target.  We did not invent this, but we have no
     idea who did.  *Note Automatic Variables::.  The automatic variable
     `$+' is a simple extension of `$^'.

   * The "what if" flag (`-W' in GNU `make') was (as far as we know)
     invented by Andrew Hume in `mk'.  *Note Instead of Executing the
     Commands: Instead of Execution.

   * The concept of doing several things at once (parallelism) exists in
     many incarnations of `make' and similar programs, though not in the
     System V or BSD implementations.  *Note Command Execution:
     Execution.

   * Modified variable references using pattern substitution come from
     SunOS 4.  *Note Basics of Variable References: Reference.  This
     functionality was provided in GNU `make' by the `patsubst'
     function before the alternate syntax was implemented for
     compatibility with SunOS 4.  It is not altogether clear who
     inspired whom, since GNU `make' had `patsubst' before SunOS 4 was
     released.

   * The special significance of `+' characters preceding command lines
     (*note Instead of Executing the Commands: Instead of Execution.) is
     mandated by `IEEE Standard 1003.2-1992' (POSIX.2).

   * The `+=' syntax to append to the value of a variable comes from
     SunOS 4 `make'.  *Note Appending More Text to Variables: Appending.

   * The syntax `ARCHIVE(MEM1 MEM2...)' to list multiple members in a
     single archive file comes from SunOS 4 `make'.  *Note Archive
     Members::.

   * The `-include' directive to include makefiles with no error for a
     nonexistent file comes from SunOS 4 `make'.  (But note that SunOS 4
     `make' does not allow multiple makefiles to be specified in one
     `-include' directive.)  The same feature appears with the name
     `sinclude' in SGI `make' and perhaps others.

   The remaining features are inventions new in GNU `make':

   * Use the `-v' or `--version' option to print version and copyright
     information.

   * Use the `-h' or `--help' option to summarize the options to `make'.

   * Simply-expanded variables.  *Note The Two Flavors of Variables:
     Flavors.

   * Pass command-line variable assignments automatically through the
     variable `MAKE' to recursive `make' invocations.  *Note Recursive
     Use of `make': Recursion.

   * Use the `-C' or `--directory' command option to change directory.
     *Note Summary of Options: Options Summary.

   * Make verbatim variable definitions with `define'.  *Note Defining
     Variables Verbatim: Defining.

   * Declare phony targets with the special target `.PHONY'.

     Andrew Hume of AT&T Bell Labs implemented a similar feature with a
     different syntax in his `mk' program.  This seems to be a case of
     parallel discovery.  *Note Phony Targets: Phony Targets.

   * Manipulate text by calling functions.  *Note Functions for
     Transforming Text: Functions.

   * Use the `-o' or `--old-file' option to pretend a file's
     modification-time is old.  *Note Avoiding Recompilation of Some
     Files: Avoiding Compilation.

   * Conditional execution.

     This feature has been implemented numerous times in various
     versions of `make'; it seems a natural extension derived from the
     features of the C preprocessor and similar macro languages and is
     not a revolutionary concept.  *Note Conditional Parts of
     Makefiles: Conditionals.

   * Specify a search path for included makefiles.  *Note Including
     Other Makefiles: Include.

   * Specify extra makefiles to read with an environment variable.
     *Note The Variable `MAKEFILES': MAKEFILES Variable.

   * Strip leading sequences of `./' from file names, so that `./FILE'
     and `FILE' are considered to be the same file.

   * Use a special search method for library prerequisites written in
     the form `-lNAME'.  *Note Directory Search for Link Libraries:
     Libraries/Search.

   * Allow suffixes for suffix rules (*note Old-Fashioned Suffix Rules:
     Suffix Rules.) to contain any characters.  In other versions of
     `make', they must begin with `.' and not contain any `/'
     characters.

   * Keep track of the current level of `make' recursion using the
     variable `MAKELEVEL'.  *Note Recursive Use of `make': Recursion.

   * Provide any goals given on the command line in the variable
     `MAKECMDGOALS'.  *Note Arguments to Specify the Goals: Goals.

   * Specify static pattern rules.  *Note Static Pattern Rules: Static
     Pattern.

   * Provide selective `vpath' search.  *Note Searching Directories for
     Prerequisites: Directory Search.

   * Provide computed variable references.  *Note Basics of Variable
     References: Reference.

   * Update makefiles.  *Note How Makefiles Are Remade: Remaking
     Makefiles.  System V `make' has a very, very limited form of this
     functionality in that it will check out SCCS files for makefiles.

   * Various new built-in implicit rules.  *Note Catalogue of Implicit
     Rules: Catalogue of Rules.

   * The built-in variable `MAKE_VERSION' gives the version number of
     `make'.  


File: make.info,  Node: Missing,  Next: Makefile Conventions,  Prev: Features,  Up: Top

13 Incompatibilities and Missing Features
*****************************************

The `make' programs in various other systems support a few features
that are not implemented in GNU `make'.  The POSIX.2 standard (`IEEE
Standard 1003.2-1992') which specifies `make' does not require any of
these features.

   * A target of the form `FILE((ENTRY))' stands for a member of
     archive file FILE.  The member is chosen, not by name, but by
     being an object file which defines the linker symbol ENTRY.

     This feature was not put into GNU `make' because of the
     nonmodularity of putting knowledge into `make' of the internal
     format of archive file symbol tables.  *Note Updating Archive
     Symbol Directories: Archive Symbols.

   * Suffixes (used in suffix rules) that end with the character `~'
     have a special meaning to System V `make'; they refer to the SCCS
     file that corresponds to the file one would get without the `~'.
     For example, the suffix rule `.c~.o' would make the file `N.o' from
     the SCCS file `s.N.c'.  For complete coverage, a whole series of
     such suffix rules is required.  *Note Old-Fashioned Suffix Rules:
     Suffix Rules.

     In GNU `make', this entire series of cases is handled by two
     pattern rules for extraction from SCCS, in combination with the
     general feature of rule chaining.  *Note Chains of Implicit Rules:
     Chained Rules.

   * In System V and 4.3 BSD `make', files found by `VPATH' search
     (*note Searching Directories for Prerequisites: Directory Search.)
     have their names changed inside command strings.  We feel it is
     much cleaner to always use automatic variables and thus make this
     feature obsolete.

   * In some Unix `make's, the automatic variable `$*' appearing in the
     prerequisites of a rule has the amazingly strange "feature" of
     expanding to the full name of the _target of that rule_.  We cannot
     imagine what went on in the minds of Unix `make' developers to do
     this; it is utterly inconsistent with the normal definition of
     `$*'.  

   * In some Unix `make's, implicit rule search (*note Using Implicit
     Rules: Implicit Rules.) is apparently done for _all_ targets, not
     just those without commands.  This means you can do:

          foo.o:
                  cc -c foo.c

     and Unix `make' will intuit that `foo.o' depends on `foo.c'.

     We feel that such usage is broken.  The prerequisite properties of
     `make' are well-defined (for GNU `make', at least), and doing such
     a thing simply does not fit the model.

   * GNU `make' does not include any built-in implicit rules for
     compiling or preprocessing EFL programs.  If we hear of anyone who
     is using EFL, we will gladly add them.

   * It appears that in SVR4 `make', a suffix rule can be specified with
     no commands, and it is treated as if it had empty commands (*note
     Empty Commands::).  For example:

          .c.a:

     will override the built-in `.c.a' suffix rule.

     We feel that it is cleaner for a rule without commands to always
     simply add to the prerequisite list for the target.  The above
     example can be easily rewritten to get the desired behavior in GNU
     `make':

          .c.a: ;

   * Some versions of `make' invoke the shell with the `-e' flag,
     except under `-k' (*note Testing the Compilation of a Program:
     Testing.).  The `-e' flag tells the shell to exit as soon as any
     program it runs returns a nonzero status.  We feel it is cleaner to
     write each shell command line to stand on its own and not require
     this special treatment.


File: make.info,  Node: Makefile Conventions,  Next: Quick Reference,  Prev: Missing,  Up: Top

14 Makefile Conventions
***********************

This node describes conventions for writing the Makefiles for GNU
programs.  Using Automake will help you write a Makefile that follows
these conventions.

* Menu:

* Makefile Basics::             General Conventions for Makefiles
* Utilities in Makefiles::      Utilities in Makefiles
* Command Variables::           Variables for Specifying Commands
* Directory Variables::         Variables for Installation Directories
* Standard Targets::            Standard Targets for Users
* Install Command Categories::  Three categories of commands in the `install'
                                  rule: normal, pre-install and post-install.


File: make.info,  Node: Makefile Basics,  Next: Utilities in Makefiles,  Up: Makefile Conventions

14.1 General Conventions for Makefiles
======================================

Every Makefile should contain this line:

     SHELL = /bin/sh

to avoid trouble on systems where the `SHELL' variable might be
inherited from the environment.  (This is never a problem with GNU
`make'.)

   Different `make' programs have incompatible suffix lists and
implicit rules, and this sometimes creates confusion or misbehavior.  So
it is a good idea to set the suffix list explicitly using only the
suffixes you need in the particular Makefile, like this:

     .SUFFIXES:
     .SUFFIXES: .c .o

The first line clears out the suffix list, the second introduces all
suffixes which may be subject to implicit rules in this Makefile.

   Don't assume that `.' is in the path for command execution.  When
you need to run programs that are a part of your package during the
make, please make sure that it uses `./' if the program is built as
part of the make or `$(srcdir)/' if the file is an unchanging part of
the source code.  Without one of these prefixes, the current search
path is used.

   The distinction between `./' (the "build directory") and
`$(srcdir)/' (the "source directory") is important because users can
build in a separate directory using the `--srcdir' option to
`configure'.  A rule of the form:

     foo.1 : foo.man sedscript
             sed -e sedscript foo.man > foo.1

will fail when the build directory is not the source directory, because
`foo.man' and `sedscript' are in the source directory.

   When using GNU `make', relying on `VPATH' to find the source file
will work in the case where there is a single dependency file, since
the `make' automatic variable `$<' will represent the source file
wherever it is.  (Many versions of `make' set `$<' only in implicit
rules.)  A Makefile target like

     foo.o : bar.c
             $(CC) -I. -I$(srcdir) $(CFLAGS) -c bar.c -o foo.o

should instead be written as

     foo.o : bar.c
             $(CC) -I. -I$(srcdir) $(CFLAGS) -c $< -o $@

in order to allow `VPATH' to work correctly.  When the target has
multiple dependencies, using an explicit `$(srcdir)' is the easiest way
to make the rule work well.  For example, the target above for `foo.1'
is best written as:

     foo.1 : foo.man sedscript
             sed -e $(srcdir)/sedscript $(srcdir)/foo.man > $@

   GNU distributions usually contain some files which are not source
files--for example, Info files, and the output from Autoconf, Automake,
Bison or Flex.  Since these files normally appear in the source
directory, they should always appear in the source directory, not in the
build directory.  So Makefile rules to update them should put the
updated files in the source directory.

   However, if a file does not appear in the distribution, then the
Makefile should not put it in the source directory, because building a
program in ordinary circumstances should not modify the source directory
in any way.

   Try to make the build and installation targets, at least (and all
their subtargets) work correctly with a parallel `make'.


File: make.info,  Node: Utilities in Makefiles,  Next: Command Variables,  Prev: Makefile Basics,  Up: Makefile Conventions

14.2 Utilities in Makefiles
===========================

Write the Makefile commands (and any shell scripts, such as
`configure') to run in `sh', not in `csh'.  Don't use any special
features of `ksh' or `bash'.

   The `configure' script and the Makefile rules for building and
installation should not use any utilities directly except these:

     cat cmp cp diff echo egrep expr false grep install-info
     ln ls mkdir mv pwd rm rmdir sed sleep sort tar test touch true

   The compression program `gzip' can be used in the `dist' rule.

   Stick to the generally supported options for these programs.  For
example, don't use `mkdir -p', convenient as it may be, because most
systems don't support it.

   It is a good idea to avoid creating symbolic links in makefiles,
since a few systems don't support them.

   The Makefile rules for building and installation can also use
compilers and related programs, but should do so via `make' variables
so that the user can substitute alternatives.  Here are some of the
programs we mean:

     ar bison cc flex install ld ldconfig lex
     make makeinfo ranlib texi2dvi yacc

   Use the following `make' variables to run those programs:

     $(AR) $(BISON) $(CC) $(FLEX) $(INSTALL) $(LD) $(LDCONFIG) $(LEX)
     $(MAKE) $(MAKEINFO) $(RANLIB) $(TEXI2DVI) $(YACC)

   When you use `ranlib' or `ldconfig', you should make sure nothing
bad happens if the system does not have the program in question.
Arrange to ignore an error from that command, and print a message before
the command to tell the user that failure of this command does not mean
a problem.  (The Autoconf `AC_PROG_RANLIB' macro can help with this.)

   If you use symbolic links, you should implement a fallback for
systems that don't have symbolic links.

   Additional utilities that can be used via Make variables are:

     chgrp chmod chown mknod

   It is ok to use other utilities in Makefile portions (or scripts)
intended only for particular systems where you know those utilities
exist.


File: make.info,  Node: Command Variables,  Next: Directory Variables,  Prev: Utilities in Makefiles,  Up: Makefile Conventions

14.3 Variables for Specifying Commands
======================================

Makefiles should provide variables for overriding certain commands,
options, and so on.

   In particular, you should run most utility programs via variables.
Thus, if you use Bison, have a variable named `BISON' whose default
value is set with `BISON = bison', and refer to it with `$(BISON)'
whenever you need to use Bison.

   File management utilities such as `ln', `rm', `mv', and so on, need
not be referred to through variables in this way, since users don't
need to replace them with other programs.

   Each program-name variable should come with an options variable that
is used to supply options to the program.  Append `FLAGS' to the
program-name variable name to get the options variable name--for
example, `BISONFLAGS'.  (The names `CFLAGS' for the C compiler,
`YFLAGS' for yacc, and `LFLAGS' for lex, are exceptions to this rule,
but we keep them because they are standard.)  Use `CPPFLAGS' in any
compilation command that runs the preprocessor, and use `LDFLAGS' in
any compilation command that does linking as well as in any direct use
of `ld'.

   If there are C compiler options that _must_ be used for proper
compilation of certain files, do not include them in `CFLAGS'.  Users
expect to be able to specify `CFLAGS' freely themselves.  Instead,
arrange to pass the necessary options to the C compiler independently
of `CFLAGS', by writing them explicitly in the compilation commands or
by defining an implicit rule, like this:

     CFLAGS = -g
     ALL_CFLAGS = -I. $(CFLAGS)
     .c.o:
             $(CC) -c $(CPPFLAGS) $(ALL_CFLAGS) $<

   Do include the `-g' option in `CFLAGS', because that is not
_required_ for proper compilation.  You can consider it a default that
is only recommended.  If the package is set up so that it is compiled
with GCC by default, then you might as well include `-O' in the default
value of `CFLAGS' as well.

   Put `CFLAGS' last in the compilation command, after other variables
containing compiler options, so the user can use `CFLAGS' to override
the others.

   `CFLAGS' should be used in every invocation of the C compiler, both
those which do compilation and those which do linking.

   Every Makefile should define the variable `INSTALL', which is the
basic command for installing a file into the system.

   Every Makefile should also define the variables `INSTALL_PROGRAM'
and `INSTALL_DATA'.  (The default for `INSTALL_PROGRAM' should be
`$(INSTALL)'; the default for `INSTALL_DATA' should be `${INSTALL} -m
644'.)  Then it should use those variables as the commands for actual
installation, for executables and nonexecutables respectively.  Use
these variables as follows:

     $(INSTALL_PROGRAM) foo $(bindir)/foo
     $(INSTALL_DATA) libfoo.a $(libdir)/libfoo.a

   Optionally, you may prepend the value of `DESTDIR' to the target
filename.  Doing this allows the installer to create a snapshot of the
installation to be copied onto the real target filesystem later.  Do not
set the value of `DESTDIR' in your Makefile, and do not include it in
any installed files.  With support for `DESTDIR', the above examples
become:

     $(INSTALL_PROGRAM) foo $(DESTDIR)$(bindir)/foo
     $(INSTALL_DATA) libfoo.a $(DESTDIR)$(libdir)/libfoo.a

Always use a file name, not a directory name, as the second argument of
the installation commands.  Use a separate command for each file to be
installed.


File: make.info,  Node: Directory Variables,  Next: Standard Targets,  Prev: Command Variables,  Up: Makefile Conventions

14.4 Variables for Installation Directories
===========================================

Installation directories should always be named by variables, so it is
easy to install in a nonstandard place.  The standard names for these
variables and the values they should have in GNU packages are described
below.  They are based on a standard filesystem layout; variants of it
are used in GNU/Linux and other modern operating systems.

   Installers are expected to override these values when calling `make'
(e.g., `make prefix=/usr install' or `configure' (e.g., `configure
--prefix=/usr').  GNU packages should not try to guess which value
should be appropriate for these variables on the system they are being
installed onto: use the default settings specified here so that all GNU
packages behave identically, allowing the installer to achieve any
desired layout.

   These two variables set the root for the installation.  All the other
installation directories should be subdirectories of one of these two,
and nothing should be directly installed into these two directories.

`prefix'
     A prefix used in constructing the default values of the variables
     listed below.  The default value of `prefix' should be
     `/usr/local'.  When building the complete GNU system, the prefix
     will be empty and `/usr' will be a symbolic link to `/'.  (If you
     are using Autoconf, write it as `@prefix@'.)

     Running `make install' with a different value of `prefix' from the
     one used to build the program should _not_ recompile the program.

`exec_prefix'
     A prefix used in constructing the default values of some of the
     variables listed below.  The default value of `exec_prefix' should
     be `$(prefix)'.  (If you are using Autoconf, write it as
     `@exec_prefix@'.)

     Generally, `$(exec_prefix)' is used for directories that contain
     machine-specific files (such as executables and subroutine
     libraries), while `$(prefix)' is used directly for other
     directories.

     Running `make install' with a different value of `exec_prefix'
     from the one used to build the program should _not_ recompile the
     program.

   Executable programs are installed in one of the following
directories.

`bindir'
     The directory for installing executable programs that users can
     run.  This should normally be `/usr/local/bin', but write it as
     `$(exec_prefix)/bin'.  (If you are using Autoconf, write it as
     `@bindir@'.)

`sbindir'
     The directory for installing executable programs that can be run
     from the shell, but are only generally useful to system
     administrators.  This should normally be `/usr/local/sbin', but
     write it as `$(exec_prefix)/sbin'.  (If you are using Autoconf,
     write it as `@sbindir@'.)

`libexecdir'
     The directory for installing executable programs to be run by other
     programs rather than by users.  This directory should normally be
     `/usr/local/libexec', but write it as `$(exec_prefix)/libexec'.
     (If you are using Autoconf, write it as `@libexecdir@'.)

     The definition of `libexecdir' is the same for all packages, so
     you should install your data in a subdirectory thereof.  Most
     packages install their data under `$(libexecdir)/PACKAGE-NAME/',
     possibly within additional subdirectories thereof, such as
     `$(libexecdir)/PACKAGE-NAME/MACHINE/VERSION'.

   Data files used by the program during its execution are divided into
categories in two ways.

   * Some files are normally modified by programs; others are never
     normally modified (though users may edit some of these).

   * Some files are architecture-independent and can be shared by all
     machines at a site; some are architecture-dependent and can be
     shared only by machines of the same kind and operating system;
     others may never be shared between two machines.

   This makes for six different possibilities.  However, we want to
discourage the use of architecture-dependent files, aside from object
files and libraries.  It is much cleaner to make other data files
architecture-independent, and it is generally not hard.

   Here are the variables Makefiles should use to specify directories
to put these various kinds of files in:

`datarootdir'
     The root of the directory tree for read-only
     architecture-independent data files.  This should normally be
     `/usr/local/share', but write it as `$(prefix)/share'.  (If you
     are using Autoconf, write it as `@datarootdir@'.)  `datadir''s
     default value is based on this variable; so are `infodir',
     `mandir', and others.

`datadir'
     The directory for installing idiosyncratic read-only
     architecture-independent data files for this program.  This is
     usually the same place as `datarootdir', but we use the two
     separate variables so that you can move these program-specific
     files without altering the location for Info files, man pages, etc.

     This should normally be `/usr/local/share', but write it as
     `$(datarootdir)'.  (If you are using Autoconf, write it as
     `@datadir@'.)

     The definition of `datadir' is the same for all packages, so you
     should install your data in a subdirectory thereof.  Most packages
     install their data under `$(datadir)/PACKAGE-NAME/'.

`sysconfdir'
     The directory for installing read-only data files that pertain to a
     single machine-that is to say, files for configuring a host.
     Mailer and network configuration files, `/etc/passwd', and so
     forth belong here.  All the files in this directory should be
     ordinary ASCII text files.  This directory should normally be
     `/usr/local/etc', but write it as `$(prefix)/etc'.  (If you are
     using Autoconf, write it as `@sysconfdir@'.)

     Do not install executables here in this directory (they probably
     belong in `$(libexecdir)' or `$(sbindir)').  Also do not install
     files that are modified in the normal course of their use (programs
     whose purpose is to change the configuration of the system
     excluded).  Those probably belong in `$(localstatedir)'.

`sharedstatedir'
     The directory for installing architecture-independent data files
     which the programs modify while they run.  This should normally be
     `/usr/local/com', but write it as `$(prefix)/com'.  (If you are
     using Autoconf, write it as `@sharedstatedir@'.)

`localstatedir'
     The directory for installing data files which the programs modify
     while they run, and that pertain to one specific machine.  Users
     should never need to modify files in this directory to configure
     the package's operation; put such configuration information in
     separate files that go in `$(datadir)' or `$(sysconfdir)'.
     `$(localstatedir)' should normally be `/usr/local/var', but write
     it as `$(prefix)/var'.  (If you are using Autoconf, write it as
     `@localstatedir@'.)

   These variables specify the directory for installing certain specific
types of files, if your program has them.  Every GNU package should
have Info files, so every program needs `infodir', but not all need
`libdir' or `lispdir'.

`includedir'
     The directory for installing header files to be included by user
     programs with the C `#include' preprocessor directive.  This
     should normally be `/usr/local/include', but write it as
     `$(prefix)/include'.  (If you are using Autoconf, write it as
     `@includedir@'.)

     Most compilers other than GCC do not look for header files in
     directory `/usr/local/include'.  So installing the header files
     this way is only useful with GCC.  Sometimes this is not a problem
     because some libraries are only really intended to work with GCC.
     But some libraries are intended to work with other compilers.
     They should install their header files in two places, one
     specified by `includedir' and one specified by `oldincludedir'.

`oldincludedir'
     The directory for installing `#include' header files for use with
     compilers other than GCC.  This should normally be `/usr/include'.
     (If you are using Autoconf, you can write it as `@oldincludedir@'.)

     The Makefile commands should check whether the value of
     `oldincludedir' is empty.  If it is, they should not try to use
     it; they should cancel the second installation of the header files.

     A package should not replace an existing header in this directory
     unless the header came from the same package.  Thus, if your Foo
     package provides a header file `foo.h', then it should install the
     header file in the `oldincludedir' directory if either (1) there
     is no `foo.h' there or (2) the `foo.h' that exists came from the
     Foo package.

     To tell whether `foo.h' came from the Foo package, put a magic
     string in the file--part of a comment--and `grep' for that string.

`docdir'
     The directory for installing documentation files (other than Info)
     for this package.  By default, it should be
     `/usr/local/share/doc/YOURPKG', but it should be written as
     `$(datarootdir)/doc/YOURPKG'.  (If you are using Autoconf, write
     it as `@docdir@'.)  The YOURPKG subdirectory, which may include a
     version number, prevents collisions among files with common names,
     such as `README'.

`infodir'
     The directory for installing the Info files for this package.  By
     default, it should be `/usr/local/share/info', but it should be
     written as `$(datarootdir)/info'.  (If you are using Autoconf,
     write it as `@infodir@'.)  `infodir' is separate from `docdir' for
     compatibility with existing practice.

`htmldir'
`dvidir'
`pdfdir'
`psdir'
     Directories for installing documentation files in the particular
     format.  (It is not required to support documentation in all these
     formats.)  They should all be set to `$(docdir)' by default.  (If
     you are using Autoconf, write them as `@htmldir@', `@dvidir@',
     etc.)  Packages which supply several translations of their
     documentation should install them in `$(htmldir)/'LL,
     `$(pdfdir)/'LL, etc. where LL is a locale abbreviation such as
     `en' or `pt_BR'.

`libdir'
     The directory for object files and libraries of object code.  Do
     not install executables here, they probably ought to go in
     `$(libexecdir)' instead.  The value of `libdir' should normally be
     `/usr/local/lib', but write it as `$(exec_prefix)/lib'.  (If you
     are using Autoconf, write it as `@libdir@'.)

`lispdir'
     The directory for installing any Emacs Lisp files in this package.
     By default, it should be `/usr/local/share/emacs/site-lisp', but
     it should be written as `$(datarootdir)/emacs/site-lisp'.

     If you are using Autoconf, write the default as `@lispdir@'.  In
     order to make `@lispdir@' work, you need the following lines in
     your `configure.in' file:

          lispdir='${datarootdir}/emacs/site-lisp'
          AC_SUBST(lispdir)

`localedir'
     The directory for installing locale-specific message catalogs for
     this package.  By default, it should be `/usr/local/share/locale',
     but it should be written as `$(datarootdir)/locale'.  (If you are
     using Autoconf, write it as `@localedir@'.)  This directory
     usually has a subdirectory per locale.

   Unix-style man pages are installed in one of the following:

`mandir'
     The top-level directory for installing the man pages (if any) for
     this package.  It will normally be `/usr/local/share/man', but you
     should write it as `$(datarootdir)/man'.  (If you are using
     Autoconf, write it as `@mandir@'.)

`man1dir'
     The directory for installing section 1 man pages.  Write it as
     `$(mandir)/man1'.

`man2dir'
     The directory for installing section 2 man pages.  Write it as
     `$(mandir)/man2'

`...'
     *Don't make the primary documentation for any GNU software be a
     man page.  Write a manual in Texinfo instead.  Man pages are just
     for the sake of people running GNU software on Unix, which is a
     secondary application only.*

`manext'
     The file name extension for the installed man page.  This should
     contain a period followed by the appropriate digit; it should
     normally be `.1'.

`man1ext'
     The file name extension for installed section 1 man pages.

`man2ext'
     The file name extension for installed section 2 man pages.

`...'
     Use these names instead of `manext' if the package needs to
     install man pages in more than one section of the manual.

   And finally, you should set the following variable:

`srcdir'
     The directory for the sources being compiled.  The value of this
     variable is normally inserted by the `configure' shell script.
     (If you are using Autconf, use `srcdir = @srcdir@'.)

   For example:

     # Common prefix for installation directories.
     # NOTE: This directory must exist when you start the install.
     prefix = /usr/local
     datarootdir = $(prefix)/share
     datadir = $(datarootdir)
     exec_prefix = $(prefix)
     # Where to put the executable for the command `gcc'.
     bindir = $(exec_prefix)/bin
     # Where to put the directories used by the compiler.
     libexecdir = $(exec_prefix)/libexec
     # Where to put the Info files.
     infodir = $(datarootdir)/info

   If your program installs a large number of files into one of the
standard user-specified directories, it might be useful to group them
into a subdirectory particular to that program.  If you do this, you
should write the `install' rule to create these subdirectories.

   Do not expect the user to include the subdirectory name in the value
of any of the variables listed above.  The idea of having a uniform set
of variable names for installation directories is to enable the user to
specify the exact same values for several different GNU packages.  In
order for this to be useful, all the packages must be designed so that
they will work sensibly when the user does so.


File: make.info,  Node: Standard Targets,  Next: Install Command Categories,  Prev: Directory Variables,  Up: Makefile Conventions

14.5 Standard Targets for Users
===============================

All GNU programs should have the following targets in their Makefiles:

`all'
     Compile the entire program.  This should be the default target.
     This target need not rebuild any documentation files; Info files
     should normally be included in the distribution, and DVI files
     should be made only when explicitly asked for.

     By default, the Make rules should compile and link with `-g', so
     that executable programs have debugging symbols.  Users who don't
     mind being helpless can strip the executables later if they wish.

`install'
     Compile the program and copy the executables, libraries, and so on
     to the file names where they should reside for actual use.  If
     there is a simple test to verify that a program is properly
     installed, this target should run that test.

     Do not strip executables when installing them.  Devil-may-care
     users can use the `install-strip' target to do that.

     If possible, write the `install' target rule so that it does not
     modify anything in the directory where the program was built,
     provided `make all' has just been done.  This is convenient for
     building the program under one user name and installing it under
     another.

     The commands should create all the directories in which files are
     to be installed, if they don't already exist.  This includes the
     directories specified as the values of the variables `prefix' and
     `exec_prefix', as well as all subdirectories that are needed.  One
     way to do this is by means of an `installdirs' target as described
     below.

     Use `-' before any command for installing a man page, so that
     `make' will ignore any errors.  This is in case there are systems
     that don't have the Unix man page documentation system installed.

     The way to install Info files is to copy them into `$(infodir)'
     with `$(INSTALL_DATA)' (*note Command Variables::), and then run
     the `install-info' program if it is present.  `install-info' is a
     program that edits the Info `dir' file to add or update the menu
     entry for the given Info file; it is part of the Texinfo package.
     Here is a sample rule to install an Info file:

          $(DESTDIR)$(infodir)/foo.info: foo.info
                  $(POST_INSTALL)
          # There may be a newer info file in . than in srcdir.
                  -if test -f foo.info; then d=.; \
                   else d=$(srcdir); fi; \
                  $(INSTALL_DATA) $$d/foo.info $(DESTDIR)$@; \
          # Run install-info only if it exists.
          # Use `if' instead of just prepending `-' to the
          # line so we notice real errors from install-info.
          # We use `$(SHELL) -c' because some shells do not
          # fail gracefully when there is an unknown command.
                  if $(SHELL) -c 'install-info --version' \
                     >/dev/null 2>&1; then \
                    install-info --dir-file=$(DESTDIR)$(infodir)/dir \
                                 $(DESTDIR)$(infodir)/foo.info; \
                  else true; fi

     When writing the `install' target, you must classify all the
     commands into three categories: normal ones, "pre-installation"
     commands and "post-installation" commands.  *Note Install Command
     Categories::.

`install-html'
`install-dvi'
`install-pdf'
`install-ps'
     These targets install documentation in formats other than Info;
     they're intended to be called explicitly by the person installing
     the package, if that format is desired.  GNU prefers Info files,
     so these must be installed by the `install' target.

     When you have many documentation files to install, we recommend
     that you avoid collisions and clutter by arranging for these
     targets to install in subdirectories of the appropriate
     installation directory, such as `htmldir'.  As one example, if
     your package has multiple manuals, and you wish to install HTML
     documentation with many files (such as the "split" mode output by
     `makeinfo --html'), you'll certainly want to use subdirectories,
     or two nodes with the same name in different manuals will
     overwrite each other.

`uninstall'
     Delete all the installed files--the copies that the `install' and
     `install-*' targets create.

     This rule should not modify the directories where compilation is
     done, only the directories where files are installed.

     The uninstallation commands are divided into three categories,
     just like the installation commands.  *Note Install Command
     Categories::.

`install-strip'
     Like `install', but strip the executable files while installing
     them.  In simple cases, this target can use the `install' target in
     a simple way:

          install-strip:
                  $(MAKE) INSTALL_PROGRAM='$(INSTALL_PROGRAM) -s' \
                          install

     But if the package installs scripts as well as real executables,
     the `install-strip' target can't just refer to the `install'
     target; it has to strip the executables but not the scripts.

     `install-strip' should not strip the executables in the build
     directory which are being copied for installation.  It should only
     strip the copies that are installed.

     Normally we do not recommend stripping an executable unless you
     are sure the program has no bugs.  However, it can be reasonable
     to install a stripped executable for actual execution while saving
     the unstripped executable elsewhere in case there is a bug.

`clean'
     Delete all files in the current directory that are normally
     created by building the program.  Also delete files in other
     directories if they are created by this makefile.  However, don't
     delete the files that record the configuration.  Also preserve
     files that could be made by building, but normally aren't because
     the distribution comes with them.  There is no need to delete
     parent directories that were created with `mkdir -p', since they
     could have existed anyway.

     Delete `.dvi' files here if they are not part of the distribution.

`distclean'
     Delete all files in the current directory (or created by this
     makefile) that are created by configuring or building the program.
     If you have unpacked the source and built the program without
     creating any other files, `make distclean' should leave only the
     files that were in the distribution.  However, there is no need to
     delete parent directories that were created with `mkdir -p', since
     they could have existed anyway.

`mostlyclean'
     Like `clean', but may refrain from deleting a few files that people
     normally don't want to recompile.  For example, the `mostlyclean'
     target for GCC does not delete `libgcc.a', because recompiling it
     is rarely necessary and takes a lot of time.

`maintainer-clean'
     Delete almost everything that can be reconstructed with this
     Makefile.  This typically includes everything deleted by
     `distclean', plus more: C source files produced by Bison, tags
     tables, Info files, and so on.

     The reason we say "almost everything" is that running the command
     `make maintainer-clean' should not delete `configure' even if
     `configure' can be remade using a rule in the Makefile.  More
     generally, `make maintainer-clean' should not delete anything that
     needs to exist in order to run `configure' and then begin to build
     the program.  Also, there is no need to delete parent directories
     that were created with `mkdir -p', since they could have existed
     anyway.  These are the only exceptions; `maintainer-clean' should
     delete everything else that can be rebuilt.

     The `maintainer-clean' target is intended to be used by a
     maintainer of the package, not by ordinary users.  You may need
     special tools to reconstruct some of the files that `make
     maintainer-clean' deletes.  Since these files are normally
     included in the distribution, we don't take care to make them easy
     to reconstruct.  If you find you need to unpack the full
     distribution again, don't blame us.

     To help make users aware of this, the commands for the special
     `maintainer-clean' target should start with these two:

          @echo 'This command is intended for maintainers to use; it'
          @echo 'deletes files that may need special tools to rebuild.'

`TAGS'
     Update a tags table for this program.

`info'
     Generate any Info files needed.  The best way to write the rules
     is as follows:

          info: foo.info

          foo.info: foo.texi chap1.texi chap2.texi
                  $(MAKEINFO) $(srcdir)/foo.texi

     You must define the variable `MAKEINFO' in the Makefile.  It should
     run the `makeinfo' program, which is part of the Texinfo
     distribution.

     Normally a GNU distribution comes with Info files, and that means
     the Info files are present in the source directory.  Therefore,
     the Make rule for an info file should update it in the source
     directory.  When users build the package, ordinarily Make will not
     update the Info files because they will already be up to date.

`dvi'
`html'
`pdf'
`ps'
     Generate documentation files in the given format, if possible.
     Here's an example rule for generating DVI files from Texinfo:

          dvi: foo.dvi

          foo.dvi: foo.texi chap1.texi chap2.texi
                  $(TEXI2DVI) $(srcdir)/foo.texi

     You must define the variable `TEXI2DVI' in the Makefile.  It should
     run the program `texi2dvi', which is part of the Texinfo
     distribution.(1)  Alternatively, write just the dependencies, and
     allow GNU `make' to provide the command.

     Here's another example, this one for generating HTML from Texinfo:

          html: foo.html

          foo.html: foo.texi chap1.texi chap2.texi
                  $(TEXI2HTML) $(srcdir)/foo.texi

     Again, you would define the variable `TEXI2HTML' in the Makefile;
     for example, it might run `makeinfo --no-split --html' (`makeinfo'
     is part of the Texinfo distribution).

`dist'
     Create a distribution tar file for this program.  The tar file
     should be set up so that the file names in the tar file start with
     a subdirectory name which is the name of the package it is a
     distribution for.  This name can include the version number.

     For example, the distribution tar file of GCC version 1.40 unpacks
     into a subdirectory named `gcc-1.40'.

     The easiest way to do this is to create a subdirectory
     appropriately named, use `ln' or `cp' to install the proper files
     in it, and then `tar' that subdirectory.

     Compress the tar file with `gzip'.  For example, the actual
     distribution file for GCC version 1.40 is called `gcc-1.40.tar.gz'.

     The `dist' target should explicitly depend on all non-source files
     that are in the distribution, to make sure they are up to date in
     the distribution.  *Note Making Releases: (standards)Releases.

`check'
     Perform self-tests (if any).  The user must build the program
     before running the tests, but need not install the program; you
     should write the self-tests so that they work when the program is
     built but not installed.

   The following targets are suggested as conventional names, for
programs in which they are useful.

`installcheck'
     Perform installation tests (if any).  The user must build and
     install the program before running the tests.  You should not
     assume that `$(bindir)' is in the search path.

`installdirs'
     It's useful to add a target named `installdirs' to create the
     directories where files are installed, and their parent
     directories.  There is a script called `mkinstalldirs' which is
     convenient for this; you can find it in the Texinfo package.  You
     can use a rule like this:

          # Make sure all installation directories (e.g. $(bindir))
          # actually exist by making them if necessary.
          installdirs: mkinstalldirs
                  $(srcdir)/mkinstalldirs $(bindir) $(datadir) \
                                          $(libdir) $(infodir) \
                                          $(mandir)

     or, if you wish to support `DESTDIR',

          # Make sure all installation directories (e.g. $(bindir))
          # actually exist by making them if necessary.
          installdirs: mkinstalldirs
                  $(srcdir)/mkinstalldirs \
                      $(DESTDIR)$(bindir) $(DESTDIR)$(datadir) \
                      $(DESTDIR)$(libdir) $(DESTDIR)$(infodir) \
                      $(DESTDIR)$(mandir)

     This rule should not modify the directories where compilation is
     done.  It should do nothing but create installation directories.

   ---------- Footnotes ----------

   (1) `texi2dvi' uses TeX to do the real work of formatting. TeX is
not distributed with Texinfo.


File: make.info,  Node: Install Command Categories,  Prev: Standard Targets,  Up: Makefile Conventions

14.6 Install Command Categories
===============================

When writing the `install' target, you must classify all the commands
into three categories: normal ones, "pre-installation" commands and
"post-installation" commands.

   Normal commands move files into their proper places, and set their
modes.  They may not alter any files except the ones that come entirely
from the package they belong to.

   Pre-installation and post-installation commands may alter other
files; in particular, they can edit global configuration files or data
bases.

   Pre-installation commands are typically executed before the normal
commands, and post-installation commands are typically run after the
normal commands.

   The most common use for a post-installation command is to run
`install-info'.  This cannot be done with a normal command, since it
alters a file (the Info directory) which does not come entirely and
solely from the package being installed.  It is a post-installation
command because it needs to be done after the normal command which
installs the package's Info files.

   Most programs don't need any pre-installation commands, but we have
the feature just in case it is needed.

   To classify the commands in the `install' rule into these three
categories, insert "category lines" among them.  A category line
specifies the category for the commands that follow.

   A category line consists of a tab and a reference to a special Make
variable, plus an optional comment at the end.  There are three
variables you can use, one for each category; the variable name
specifies the category.  Category lines are no-ops in ordinary execution
because these three Make variables are normally undefined (and you
_should not_ define them in the makefile).

   Here are the three possible category lines, each with a comment that
explains what it means:

             $(PRE_INSTALL)     # Pre-install commands follow.
             $(POST_INSTALL)    # Post-install commands follow.
             $(NORMAL_INSTALL)  # Normal commands follow.

   If you don't use a category line at the beginning of the `install'
rule, all the commands are classified as normal until the first category
line.  If you don't use any category lines, all the commands are
classified as normal.

   These are the category lines for `uninstall':

             $(PRE_UNINSTALL)     # Pre-uninstall commands follow.
             $(POST_UNINSTALL)    # Post-uninstall commands follow.
             $(NORMAL_UNINSTALL)  # Normal commands follow.

   Typically, a pre-uninstall command would be used for deleting entries
from the Info directory.

   If the `install' or `uninstall' target has any dependencies which
act as subroutines of installation, then you should start _each_
dependency's commands with a category line, and start the main target's
commands with a category line also.  This way, you can ensure that each
command is placed in the right category regardless of which of the
dependencies actually run.

   Pre-installation and post-installation commands should not run any
programs except for these:

     [ basename bash cat chgrp chmod chown cmp cp dd diff echo
     egrep expand expr false fgrep find getopt grep gunzip gzip
     hostname install install-info kill ldconfig ln ls md5sum
     mkdir mkfifo mknod mv printenv pwd rm rmdir sed sort tee
     test touch true uname xargs yes

   The reason for distinguishing the commands in this way is for the
sake of making binary packages.  Typically a binary package contains
all the executables and other files that need to be installed, and has
its own method of installing them--so it does not need to run the normal
installation commands.  But installing the binary package does need to
execute the pre-installation and post-installation commands.

   Programs to build binary packages work by extracting the
pre-installation and post-installation commands.  Here is one way of
extracting the pre-installation commands (the `-s' option to `make' is
needed to silence messages about entering subdirectories):

     make -s -n install -o all \
           PRE_INSTALL=pre-install \
           POST_INSTALL=post-install \
           NORMAL_INSTALL=normal-install \
       | gawk -f pre-install.awk

where the file `pre-install.awk' could contain this:

     $0 ~ /^(normal-install|post-install)[ \t]*$/ {on = 0}
     on {print $0}
     $0 ~ /^pre-install[ \t]*$/ {on = 1}


File: make.info,  Node: Quick Reference,  Next: Error Messages,  Prev: Makefile Conventions,  Up: Top

Appendix A Quick Reference
**************************

This appendix summarizes the directives, text manipulation functions,
and special variables which GNU `make' understands.  *Note Special
Targets::, *Note Catalogue of Implicit Rules: Catalogue of Rules, and
*Note Summary of Options: Options Summary, for other summaries.

   Here is a summary of the directives GNU `make' recognizes:

`define VARIABLE'
`endef'
     Define a multi-line, recursively-expanded variable.
     *Note Sequences::.

`ifdef VARIABLE'
`ifndef VARIABLE'
`ifeq (A,B)'
`ifeq "A" "B"'
`ifeq 'A' 'B''
`ifneq (A,B)'
`ifneq "A" "B"'
`ifneq 'A' 'B''
`else'
`endif'
     Conditionally evaluate part of the makefile.
     *Note Conditionals::.

`include FILE'
`-include FILE'
`sinclude FILE'
     Include another makefile.
     *Note Including Other Makefiles: Include.

`override VARIABLE = VALUE'
`override VARIABLE := VALUE'
`override VARIABLE += VALUE'
`override VARIABLE ?= VALUE'
`override define VARIABLE'
`endef'
     Define a variable, overriding any previous definition, even one
     from the command line.
     *Note The `override' Directive: Override Directive.

`export'
     Tell `make' to export all variables to child processes by default.
     *Note Communicating Variables to a Sub-`make': Variables/Recursion.

`export VARIABLE'
`export VARIABLE = VALUE'
`export VARIABLE := VALUE'
`export VARIABLE += VALUE'
`export VARIABLE ?= VALUE'
`unexport VARIABLE'
     Tell `make' whether or not to export a particular variable to child
     processes.
     *Note Communicating Variables to a Sub-`make': Variables/Recursion.

`vpath PATTERN PATH'
     Specify a search path for files matching a `%' pattern.
     *Note The `vpath' Directive: Selective Search.

`vpath PATTERN'
     Remove all search paths previously specified for PATTERN.

`vpath'
     Remove all search paths previously specified in any `vpath'
     directive.

   Here is a summary of the built-in functions (*note Functions::):

`$(subst FROM,TO,TEXT)'
     Replace FROM with TO in TEXT.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(patsubst PATTERN,REPLACEMENT,TEXT)'
     Replace words matching PATTERN with REPLACEMENT in TEXT.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(strip STRING)'
     Remove excess whitespace characters from STRING.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(findstring FIND,TEXT)'
     Locate FIND in TEXT.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(filter PATTERN...,TEXT)'
     Select words in TEXT that match one of the PATTERN words.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(filter-out PATTERN...,TEXT)'
     Select words in TEXT that _do not_ match any of the PATTERN words.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(sort LIST)'
     Sort the words in LIST lexicographically, removing duplicates.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(word N,TEXT)'
     Extract the Nth word (one-origin) of TEXT.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(words TEXT)'
     Count the number of words in TEXT.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(wordlist S,E,TEXT)'
     Returns the list of words in TEXT from S to E.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(firstword NAMES...)'
     Extract the first word of NAMES.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(lastword NAMES...)'
     Extract the last word of NAMES.
     *Note Functions for String Substitution and Analysis: Text
     Functions.

`$(dir NAMES...)'
     Extract the directory part of each file name.
     *Note Functions for File Names: File Name Functions.

`$(notdir NAMES...)'
     Extract the non-directory part of each file name.
     *Note Functions for File Names: File Name Functions.

`$(suffix NAMES...)'
     Extract the suffix (the last `.' and following characters) of each
     file name.
     *Note Functions for File Names: File Name Functions.

`$(basename NAMES...)'
     Extract the base name (name without suffix) of each file name.
     *Note Functions for File Names: File Name Functions.

`$(addsuffix SUFFIX,NAMES...)'
     Append SUFFIX to each word in NAMES.
     *Note Functions for File Names: File Name Functions.

`$(addprefix PREFIX,NAMES...)'
     Prepend PREFIX to each word in NAMES.
     *Note Functions for File Names: File Name Functions.

`$(join LIST1,LIST2)'
     Join two parallel lists of words.
     *Note Functions for File Names: File Name Functions.

`$(wildcard PATTERN...)'
     Find file names matching a shell file name pattern (_not_ a `%'
     pattern).
     *Note The Function `wildcard': Wildcard Function.

`$(realpath NAMES...)'
     For each file name in NAMES, expand to an absolute name that does
     not contain any `.', `..', nor symlinks.
     *Note Functions for File Names: File Name Functions.

`$(abspath NAMES...)'
     For each file name in NAMES, expand to an absolute name that does
     not contain any `.' or `..' components, but preserves symlinks.
     *Note Functions for File Names: File Name Functions.

`$(error TEXT...)'
     When this function is evaluated, `make' generates a fatal error
     with the message TEXT.
     *Note Functions That Control Make: Make Control Functions.

`$(warning TEXT...)'
     When this function is evaluated, `make' generates a warning with
     the message TEXT.
     *Note Functions That Control Make: Make Control Functions.

`$(shell COMMAND)'
     Execute a shell command and return its output.
     *Note The `shell' Function: Shell Function.

`$(origin VARIABLE)'
     Return a string describing how the `make' variable VARIABLE was
     defined.
     *Note The `origin' Function: Origin Function.

`$(flavor VARIABLE)'
     Return a string describing the flavor of the `make' variable
     VARIABLE.
     *Note The `flavor' Function: Flavor Function.

`$(foreach VAR,WORDS,TEXT)'
     Evaluate TEXT with VAR bound to each word in WORDS, and
     concatenate the results.
     *Note The `foreach' Function: Foreach Function.

`$(call VAR,PARAM,...)'
     Evaluate the variable VAR replacing any references to `$(1)',
     `$(2)' with the first, second, etc. PARAM values.
     *Note The `call' Function: Call Function.

`$(eval TEXT)'
     Evaluate TEXT then read the results as makefile commands.  Expands
     to the empty string.
     *Note The `eval' Function: Eval Function.

`$(value VAR)'
     Evaluates to the contents of the variable VAR, with no expansion
     performed on it.
     *Note The `value' Function: Value Function.

   Here is a summary of the automatic variables.  *Note Automatic
Variables::, for full information.

`$@'
     The file name of the target.

`$%'
     The target member name, when the target is an archive member.

`$<'
     The name of the first prerequisite.

`$?'
     The names of all the prerequisites that are newer than the target,
     with spaces between them.  For prerequisites which are archive
     members, only the member named is used (*note Archives::).

`$^'
`$+'
     The names of all the prerequisites, with spaces between them.  For
     prerequisites which are archive members, only the member named is
     used (*note Archives::).  The value of `$^' omits duplicate
     prerequisites, while `$+' retains them and preserves their order.

`$*'
     The stem with which an implicit rule matches (*note How Patterns
     Match: Pattern Match.).

`$(@D)'
`$(@F)'
     The directory part and the file-within-directory part of `$@'.

`$(*D)'
`$(*F)'
     The directory part and the file-within-directory part of `$*'.

`$(%D)'
`$(%F)'
     The directory part and the file-within-directory part of `$%'.

`$(<D)'
`$(<F)'
     The directory part and the file-within-directory part of `$<'.

`$(^D)'
`$(^F)'
     The directory part and the file-within-directory part of `$^'.

`$(+D)'
`$(+F)'
     The directory part and the file-within-directory part of `$+'.

`$(?D)'
`$(?F)'
     The directory part and the file-within-directory part of `$?'.

   These variables are used specially by GNU `make':

`MAKEFILES'
     Makefiles to be read on every invocation of `make'.
     *Note The Variable `MAKEFILES': MAKEFILES Variable.

`VPATH'
     Directory search path for files not found in the current directory.
     *Note `VPATH' Search Path for All Prerequisites: General Search.

`SHELL'
     The name of the system default command interpreter, usually
     `/bin/sh'.  You can set `SHELL' in the makefile to change the
     shell used to run commands.  *Note Command Execution: Execution.
     The `SHELL' variable is handled specially when importing from and
     exporting to the environment.  *Note Choosing the Shell::.

`MAKESHELL'
     On MS-DOS only, the name of the command interpreter that is to be
     used by `make'.  This value takes precedence over the value of
     `SHELL'.  *Note MAKESHELL variable: Execution.

`MAKE'
     The name with which `make' was invoked.  Using this variable in
     commands has special meaning.  *Note How the `MAKE' Variable
     Works: MAKE Variable.

`MAKELEVEL'
     The number of levels of recursion (sub-`make's).
     *Note Variables/Recursion::.

`MAKEFLAGS'
     The flags given to `make'.  You can set this in the environment or
     a makefile to set flags.
     *Note Communicating Options to a Sub-`make': Options/Recursion.

     It is _never_ appropriate to use `MAKEFLAGS' directly on a command
     line: its contents may not be quoted correctly for use in the
     shell.  Always allow recursive `make''s to obtain these values
     through the environment from its parent.

`MAKECMDGOALS'
     The targets given to `make' on the command line.  Setting this
     variable has no effect on the operation of `make'.
     *Note Arguments to Specify the Goals: Goals.

`CURDIR'
     Set to the pathname of the current working directory (after all
     `-C' options are processed, if any).  Setting this variable has no
     effect on the operation of `make'.
     *Note Recursive Use of `make': Recursion.

`SUFFIXES'
     The default list of suffixes before `make' reads any makefiles.

`.LIBPATTERNS'
     Defines the naming of the libraries `make' searches for, and their
     order.
     *Note Directory Search for Link Libraries: Libraries/Search.


File: make.info,  Node: Error Messages,  Next: Complex Makefile,  Prev: Quick Reference,  Up: Top

Appendix B Errors Generated by Make
***********************************

Here is a list of the more common errors you might see generated by
`make', and some information about what they mean and how to fix them.

   Sometimes `make' errors are not fatal, especially in the presence of
a `-' prefix on a command script line, or the `-k' command line option.
Errors that are fatal are prefixed with the string `***'.

   Error messages are all either prefixed with the name of the program
(usually `make'), or, if the error is found in a makefile, the name of
the file and linenumber containing the problem.

   In the table below, these common prefixes are left off.

`[FOO] Error NN'
`[FOO] SIGNAL DESCRIPTION'
     These errors are not really `make' errors at all.  They mean that a
     program that `make' invoked as part of a command script returned a
     non-0 error code (`Error NN'), which `make' interprets as failure,
     or it exited in some other abnormal fashion (with a signal of some
     type).  *Note Errors in Commands: Errors.

     If no `***' is attached to the message, then the subprocess failed
     but the rule in the makefile was prefixed with the `-' special
     character, so `make' ignored the error.

`missing separator.  Stop.'
`missing separator (did you mean TAB instead of 8 spaces?).  Stop.'
     This means that `make' could not understand much of anything about
     the command line it just read.  GNU `make' looks for various kinds
     of separators (`:', `=', TAB characters, etc.) to help it decide
     what kind of commandline it's seeing.  This means it couldn't find
     a valid one.

     One of the most common reasons for this message is that you (or
     perhaps your oh-so-helpful editor, as is the case with many
     MS-Windows editors) have attempted to indent your command scripts
     with spaces instead of a TAB character.  In this case, `make' will
     use the second form of the error above.  Remember that every line
     in the command script must begin with a TAB character.  Eight
     spaces do not count.  *Note Rule Syntax::.

`commands commence before first target.  Stop.'
`missing rule before commands.  Stop.'
     This means the first thing in the makefile seems to be part of a
     command script: it begins with a TAB character and doesn't appear
     to be a legal `make' command (such as a variable assignment).
     Command scripts must always be associated with a target.

     The second form is generated if the line has a semicolon as the
     first non-whitespace character; `make' interprets this to mean you
     left out the "target: prerequisite" section of a rule.  *Note Rule
     Syntax::.

`No rule to make target `XXX'.'
`No rule to make target `XXX', needed by `YYY'.'
     This means that `make' decided it needed to build a target, but
     then couldn't find any instructions in the makefile on how to do
     that, either explicit or implicit (including in the default rules
     database).

     If you want that file to be built, you will need to add a rule to
     your makefile describing how that target can be built.  Other
     possible sources of this problem are typos in the makefile (if
     that filename is wrong) or a corrupted source tree (if that file
     is not supposed to be built, but rather only a prerequisite).

`No targets specified and no makefile found.  Stop.'
`No targets.  Stop.'
     The former means that you didn't provide any targets to be built
     on the command line, and `make' couldn't find any makefiles to
     read in.  The latter means that some makefile was found, but it
     didn't contain any default goal and none was given on the command
     line.  GNU `make' has nothing to do in these situations.  *Note
     Arguments to Specify the Makefile: Makefile Arguments.

`Makefile `XXX' was not found.'
`Included makefile `XXX' was not found.'
     A makefile specified on the command line (first form) or included
     (second form) was not found.

`warning: overriding commands for target `XXX''
`warning: ignoring old commands for target `XXX''
     GNU `make' allows commands to be specified only once per target
     (except for double-colon rules).  If you give commands for a target
     which already has been defined to have commands, this warning is
     issued and the second set of commands will overwrite the first set.
     *Note Multiple Rules for One Target: Multiple Rules.

`Circular XXX <- YYY dependency dropped.'
     This means that `make' detected a loop in the dependency graph:
     after tracing the prerequisite YYY of target XXX, and its
     prerequisites, etc., one of them depended on XXX again.

`Recursive variable `XXX' references itself (eventually).  Stop.'
     This means you've defined a normal (recursive) `make' variable XXX
     that, when it's expanded, will refer to itself (XXX).  This is not
     allowed; either use simply-expanded variables (`:=') or use the
     append operator (`+=').  *Note How to Use Variables: Using
     Variables.

`Unterminated variable reference.  Stop.'
     This means you forgot to provide the proper closing parenthesis or
     brace in your variable or function reference.

`insufficient arguments to function `XXX'.  Stop.'
     This means you haven't provided the requisite number of arguments
     for this function.  See the documentation of the function for a
     description of its arguments.  *Note Functions for Transforming
     Text: Functions.

`missing target pattern.  Stop.'
`multiple target patterns.  Stop.'
`target pattern contains no `%'.  Stop.'
`mixed implicit and static pattern rules.  Stop.'
     These are generated for malformed static pattern rules.  The first
     means there's no pattern in the target section of the rule; the
     second means there are multiple patterns in the target section;
     the third means the target doesn't contain a pattern character
     (`%'); and the fourth means that all three parts of the static
     pattern rule contain pattern characters (`%')-only the first two
     parts should.  *Note Syntax of Static Pattern Rules: Static Usage.

`warning: -jN forced in submake: disabling jobserver mode.'
     This warning and the next are generated if `make' detects error
     conditions related to parallel processing on systems where
     sub-`make's can communicate (*note Communicating Options to a
     Sub-`make': Options/Recursion.).  This warning is generated if a
     recursive invocation of a `make' process is forced to have `-jN'
     in its argument list (where N is greater than one).  This could
     happen, for example, if you set the `MAKE' environment variable to
     `make -j2'.  In this case, the sub-`make' doesn't communicate with
     other `make' processes and will simply pretend it has two jobs of
     its own.

`warning: jobserver unavailable: using -j1.  Add `+' to parent make rule.'
     In order for `make' processes to communicate, the parent will pass
     information to the child.  Since this could result in problems if
     the child process isn't actually a `make', the parent will only do
     this if it thinks the child is a `make'.  The parent uses the
     normal algorithms to determine this (*note How the `MAKE' Variable
     Works: MAKE Variable.).  If the makefile is constructed such that
     the parent doesn't know the child is a `make' process, then the
     child will receive only part of the information necessary.  In
     this case, the child will generate this warning message and
     proceed with its build in a sequential manner.



File: make.info,  Node: Complex Makefile,  Next: GNU Free Documentation License,  Prev: Error Messages,  Up: Top

Appendix C Complex Makefile Example
***********************************

Here is the makefile for the GNU `tar' program.  This is a moderately
complex makefile.

   Because it is the first target, the default goal is `all'.  An
interesting feature of this makefile is that `testpad.h' is a source
file automatically created by the `testpad' program, itself compiled
from `testpad.c'.

   If you type `make' or `make all', then `make' creates the `tar'
executable, the `rmt' daemon that provides remote tape access, and the
`tar.info' Info file.

   If you type `make install', then `make' not only creates `tar',
`rmt', and `tar.info', but also installs them.

   If you type `make clean', then `make' removes the `.o' files, and
the `tar', `rmt', `testpad', `testpad.h', and `core' files.

   If you type `make distclean', then `make' not only removes the same
files as does `make clean' but also the `TAGS', `Makefile', and
`config.status' files.  (Although it is not evident, this makefile (and
`config.status') is generated by the user with the `configure' program,
which is provided in the `tar' distribution, but is not shown here.)

   If you type `make realclean', then `make' removes the same files as
does `make distclean' and also removes the Info files generated from
`tar.texinfo'.

   In addition, there are targets `shar' and `dist' that create
distribution kits.

     # Generated automatically from Makefile.in by configure.
     # Un*x Makefile for GNU tar program.
     # Copyright (C) 1991 Free Software Foundation, Inc.

     # This program is free software; you can redistribute
     # it and/or modify it under the terms of the GNU
     # General Public License ...
     ...
     ...

     SHELL = /bin/sh

     #### Start of system configuration section. ####

     srcdir = .

     # If you use gcc, you should either run the
     # fixincludes script that comes with it or else use
     # gcc with the -traditional option.  Otherwise ioctl
     # calls will be compiled incorrectly on some systems.
     CC = gcc -O
     YACC = bison -y
     INSTALL = /usr/local/bin/install -c
     INSTALLDATA = /usr/local/bin/install -c -m 644

     # Things you might add to DEFS:
     # -DSTDC_HEADERS        If you have ANSI C headers and
     #                       libraries.
     # -DPOSIX               If you have POSIX.1 headers and
     #                       libraries.
     # -DBSD42               If you have sys/dir.h (unless
     #                       you use -DPOSIX), sys/file.h,
     #                       and st_blocks in `struct stat'.
     # -DUSG                 If you have System V/ANSI C
     #                       string and memory functions
     #                       and headers, sys/sysmacros.h,
     #                       fcntl.h, getcwd, no valloc,
     #                       and ndir.h (unless
     #                       you use -DDIRENT).
     # -DNO_MEMORY_H         If USG or STDC_HEADERS but do not
     #                       include memory.h.
     # -DDIRENT              If USG and you have dirent.h
     #                       instead of ndir.h.
     # -DSIGTYPE=int         If your signal handlers
     #                       return int, not void.
     # -DNO_MTIO             If you lack sys/mtio.h
     #                       (magtape ioctls).
     # -DNO_REMOTE           If you do not have a remote shell
     #                       or rexec.
     # -DUSE_REXEC           To use rexec for remote tape
     #                       operations instead of
     #                       forking rsh or remsh.
     # -DVPRINTF_MISSING     If you lack vprintf function
     #                       (but have _doprnt).
     # -DDOPRNT_MISSING      If you lack _doprnt function.
     #                       Also need to define
     #                       -DVPRINTF_MISSING.
     # -DFTIME_MISSING       If you lack ftime system call.
     # -DSTRSTR_MISSING      If you lack strstr function.
     # -DVALLOC_MISSING      If you lack valloc function.
     # -DMKDIR_MISSING       If you lack mkdir and
     #                       rmdir system calls.
     # -DRENAME_MISSING      If you lack rename system call.
     # -DFTRUNCATE_MISSING   If you lack ftruncate
     #                       system call.
     # -DV7                  On Version 7 Unix (not
     #                       tested in a long time).
     # -DEMUL_OPEN3          If you lack a 3-argument version
     #                       of open, and want to emulate it
     #                       with system calls you do have.
     # -DNO_OPEN3            If you lack the 3-argument open
     #                       and want to disable the tar -k
     #                       option instead of emulating open.
     # -DXENIX               If you have sys/inode.h
     #                       and need it 94 to be included.

     DEFS =  -DSIGTYPE=int -DDIRENT -DSTRSTR_MISSING \
             -DVPRINTF_MISSING -DBSD42
     # Set this to rtapelib.o unless you defined NO_REMOTE,
     # in which case make it empty.
     RTAPELIB = rtapelib.o
     LIBS =
     DEF_AR_FILE = /dev/rmt8
     DEFBLOCKING = 20

     CDEBUG = -g
     CFLAGS = $(CDEBUG) -I. -I$(srcdir) $(DEFS) \
             -DDEF_AR_FILE=\"$(DEF_AR_FILE)\" \
             -DDEFBLOCKING=$(DEFBLOCKING)
     LDFLAGS = -g

     prefix = /usr/local
     # Prefix for each installed program,
     # normally empty or `g'.
     binprefix =

     # The directory to install tar in.
     bindir = $(prefix)/bin

     # The directory to install the info files in.
     infodir = $(prefix)/info

     #### End of system configuration section. ####

     SRC1 =  tar.c create.c extract.c buffer.c \
             getoldopt.c update.c gnu.c mangle.c
     SRC2 =  version.c list.c names.c diffarch.c \
             port.c wildmat.c getopt.c
     SRC3 =  getopt1.c regex.c getdate.y
     SRCS =  $(SRC1) $(SRC2) $(SRC3)
     OBJ1 =  tar.o create.o extract.o buffer.o \
             getoldopt.o update.o gnu.o mangle.o
     OBJ2 =  version.o list.o names.o diffarch.o \
             port.o wildmat.o getopt.o
     OBJ3 =  getopt1.o regex.o getdate.o $(RTAPELIB)
     OBJS =  $(OBJ1) $(OBJ2) $(OBJ3)
     AUX =   README COPYING ChangeLog Makefile.in  \
             makefile.pc configure configure.in \
             tar.texinfo tar.info* texinfo.tex \
             tar.h port.h open3.h getopt.h regex.h \
             rmt.h rmt.c rtapelib.c alloca.c \
             msd_dir.h msd_dir.c tcexparg.c \
             level-0 level-1 backup-specs testpad.c

     .PHONY: all
     all:    tar rmt tar.info

     .PHONY: tar
     tar:    $(OBJS)
             $(CC) $(LDFLAGS) -o $@ $(OBJS) $(LIBS)

     rmt:    rmt.c
             $(CC) $(CFLAGS) $(LDFLAGS) -o $@ rmt.c

     tar.info: tar.texinfo
             makeinfo tar.texinfo

     .PHONY: install
     install: all
             $(INSTALL) tar $(bindir)/$(binprefix)tar
             -test ! -f rmt || $(INSTALL) rmt /etc/rmt
             $(INSTALLDATA) $(srcdir)/tar.info* $(infodir)

     $(OBJS): tar.h port.h testpad.h
     regex.o buffer.o tar.o: regex.h
     # getdate.y has 8 shift/reduce conflicts.

     testpad.h: testpad
             ./testpad

     testpad: testpad.o
             $(CC) -o $@ testpad.o

     TAGS:   $(SRCS)
             etags $(SRCS)

     .PHONY: clean
     clean:
             rm -f *.o tar rmt testpad testpad.h core

     .PHONY: distclean
     distclean: clean
             rm -f TAGS Makefile config.status

     .PHONY: realclean
     realclean: distclean
             rm -f tar.info*

     .PHONY: shar
     shar: $(SRCS) $(AUX)
             shar $(SRCS) $(AUX) | compress \
               > tar-`sed -e '/version_string/!d' \
                          -e 's/[^0-9.]*\([0-9.]*\).*/\1/' \
                          -e q
                          version.c`.shar.Z

     .PHONY: dist
     dist: $(SRCS) $(AUX)
             echo tar-`sed \
                  -e '/version_string/!d' \
                  -e 's/[^0-9.]*\([0-9.]*\).*/\1/' \
                  -e q
                  version.c` > .fname
             -rm -rf `cat .fname`
             mkdir `cat .fname`
             ln $(SRCS) $(AUX) `cat .fname`
             tar chZf `cat .fname`.tar.Z `cat .fname`
             -rm -rf `cat .fname` .fname

     tar.zoo: $(SRCS) $(AUX)
             -rm -rf tmp.dir
             -mkdir tmp.dir
             -rm tar.zoo
             for X in $(SRCS) $(AUX) ; do \
                 echo $$X ; \
                 sed 's/$$/^M/' $$X \
                 > tmp.dir/$$X ; done
             cd tmp.dir ; zoo aM ../tar.zoo *
             -rm -rf tmp.dir


File: make.info,  Node: GNU Free Documentation License,  Next: Concept Index,  Prev: Complex Makefile,  Up: Top

Appendix D GNU Free Documentation License
*****************************************

                      Version 1.2, November 2002

     Copyright (C) 2000,2001,2002 Free Software Foundation, Inc.
     51 Franklin St, Fifth Floor, Boston, MA  02110-1301, USA

     Everyone is permitted to copy and distribute verbatim copies
     of this license document, but changing it is not allowed.

  0. PREAMBLE

     The purpose of this License is to make a manual, textbook, or other
     functional and useful document "free" in the sense of freedom: to
     assure everyone the effective freedom to copy and redistribute it,
     with or without modifying it, either commercially or
     noncommercially.  Secondarily, this License preserves for the
     author and publisher a way to get credit for their work, while not
     being considered responsible for modifications made by others.

     This License is a kind of "copyleft", which means that derivative
     works of the document must themselves be free in the same sense.
     It complements the GNU General Public License, which is a copyleft
     license designed for free software.

     We have designed this License in order to use it for manuals for
     free software, because free software needs free documentation: a
     free program should come with manuals providing the same freedoms
     that the software does.  But this License is not limited to
     software manuals; it can be used for any textual work, regardless
     of subject matter or whether it is published as a printed book.
     We recommend this License principally for works whose purpose is
     instruction or reference.

  1. APPLICABILITY AND DEFINITIONS

     This License applies to any manual or other work, in any medium,
     that contains a notice placed by the copyright holder saying it
     can be distributed under the terms of this License.  Such a notice
     grants a world-wide, royalty-free license, unlimited in duration,
     to use that work under the conditions stated herein.  The
     "Document", below, refers to any such manual or work.  Any member
     of the public is a licensee, and is addressed as "you".  You
     accept the license if you copy, modify or distribute the work in a
     way requiring permission under copyright law.

     A "Modified Version" of the Document means any work containing the
     Document or a portion of it, either copied verbatim, or with
     modifications and/or translated into another language.

     A "Secondary Section" is a named appendix or a front-matter section
     of the Document that deals exclusively with the relationship of the
     publishers or authors of the Document to the Document's overall
     subject (or to related matters) and contains nothing that could
     fall directly within that overall subject.  (Thus, if the Document
     is in part a textbook of mathematics, a Secondary Section may not
     explain any mathematics.)  The relationship could be a matter of
     historical connection with the subject or with related matters, or
     of legal, commercial, philosophical, ethical or political position
     regarding them.

     The "Invariant Sections" are certain Secondary Sections whose
     titles are designated, as being those of Invariant Sections, in
     the notice that says that the Document is released under this
     License.  If a section does not fit the above definition of
     Secondary then it is not allowed to be designated as Invariant.
     The Document may contain zero Invariant Sections.  If the Document
     does not identify any Invariant Sections then there are none.

     The "Cover Texts" are certain short passages of text that are
     listed, as Front-Cover Texts or Back-Cover Texts, in the notice
     that says that the Document is released under this License.  A
     Front-Cover Text may be at most 5 words, and a Back-Cover Text may
     be at most 25 words.

     A "Transparent" copy of the Document means a machine-readable copy,
     represented in a format whose specification is available to the
     general public, that is suitable for revising the document
     straightforwardly with generic text editors or (for images
     composed of pixels) generic paint programs or (for drawings) some
     widely available drawing editor, and that is suitable for input to
     text formatters or for automatic translation to a variety of
     formats suitable for input to text formatters.  A copy made in an
     otherwise Transparent file format whose markup, or absence of
     markup, has been arranged to thwart or discourage subsequent
     modification by readers is not Transparent.  An image format is
     not Transparent if used for any substantial amount of text.  A
     copy that is not "Transparent" is called "Opaque".

     Examples of suitable formats for Transparent copies include plain
     ASCII without markup, Texinfo input format, LaTeX input format,
     SGML or XML using a publicly available DTD, and
     standard-conforming simple HTML, PostScript or PDF designed for
     human modification.  Examples of transparent image formats include
     PNG, XCF and JPG.  Opaque formats include proprietary formats that
     can be read and edited only by proprietary word processors, SGML or
     XML for which the DTD and/or processing tools are not generally
     available, and the machine-generated HTML, PostScript or PDF
     produced by some word processors for output purposes only.

     The "Title Page" means, for a printed book, the title page itself,
     plus such following pages as are needed to hold, legibly, the
     material this License requires to appear in the title page.  For
     works in formats which do not have any title page as such, "Title
     Page" means the text near the most prominent appearance of the
     work's title, preceding the beginning of the body of the text.

     A section "Entitled XYZ" means a named subunit of the Document
     whose title either is precisely XYZ or contains XYZ in parentheses
     following text that translates XYZ in another language.  (Here XYZ
     stands for a specific section name mentioned below, such as
     "Acknowledgements", "Dedications", "Endorsements", or "History".)
     To "Preserve the Title" of such a section when you modify the
     Document means that it remains a section "Entitled XYZ" according
     to this definition.

     The Document may include Warranty Disclaimers next to the notice
     which states that this License applies to the Document.  These
     Warranty Disclaimers are considered to be included by reference in
     this License, but only as regards disclaiming warranties: any other
     implication that these Warranty Disclaimers may have is void and
     has no effect on the meaning of this License.

  2. VERBATIM COPYING

     You may copy and distribute the Document in any medium, either
     commercially or noncommercially, provided that this License, the
     copyright notices, and the license notice saying this License
     applies to the Document are reproduced in all copies, and that you
     add no other conditions whatsoever to those of this License.  You
     may not use technical measures to obstruct or control the reading
     or further copying of the copies you make or distribute.  However,
     you may accept compensation in exchange for copies.  If you
     distribute a large enough number of copies you must also follow
     the conditions in section 3.

     You may also lend copies, under the same conditions stated above,
     and you may publicly display copies.

  3. COPYING IN QUANTITY

     If you publish printed copies (or copies in media that commonly
     have printed covers) of the Document, numbering more than 100, and
     the Document's license notice requires Cover Texts, you must
     enclose the copies in covers that carry, clearly and legibly, all
     these Cover Texts: Front-Cover Texts on the front cover, and
     Back-Cover Texts on the back cover.  Both covers must also clearly
     and legibly identify you as the publisher of these copies.  The
     front cover must present the full title with all words of the
     title equally prominent and visible.  You may add other material
     on the covers in addition.  Copying with changes limited to the
     covers, as long as they preserve the title of the Document and
     satisfy these conditions, can be treated as verbatim copying in
     other respects.

     If the required texts for either cover are too voluminous to fit
     legibly, you should put the first ones listed (as many as fit
     reasonably) on the actual cover, and continue the rest onto
     adjacent pages.

     If you publish or distribute Opaque copies of the Document
     numbering more than 100, you must either include a
     machine-readable Transparent copy along with each Opaque copy, or
     state in or with each Opaque copy a computer-network location from
     which the general network-using public has access to download
     using public-standard network protocols a complete Transparent
     copy of the Document, free of added material.  If you use the
     latter option, you must take reasonably prudent steps, when you
     begin distribution of Opaque copies in quantity, to ensure that
     this Transparent copy will remain thus accessible at the stated
     location until at least one year after the last time you
     distribute an Opaque copy (directly or through your agents or
     retailers) of that edition to the public.

     It is requested, but not required, that you contact the authors of
     the Document well before redistributing any large number of
     copies, to give them a chance to provide you with an updated
     version of the Document.

  4. MODIFICATIONS

     You may copy and distribute a Modified Version of the Document
     under the conditions of sections 2 and 3 above, provided that you
     release the Modified Version under precisely this License, with
     the Modified Version filling the role of the Document, thus
     licensing distribution and modification of the Modified Version to
     whoever possesses a copy of it.  In addition, you must do these
     things in the Modified Version:

       A. Use in the Title Page (and on the covers, if any) a title
          distinct from that of the Document, and from those of
          previous versions (which should, if there were any, be listed
          in the History section of the Document).  You may use the
          same title as a previous version if the original publisher of
          that version gives permission.

       B. List on the Title Page, as authors, one or more persons or
          entities responsible for authorship of the modifications in
          the Modified Version, together with at least five of the
          principal authors of the Document (all of its principal
          authors, if it has fewer than five), unless they release you
          from this requirement.

       C. State on the Title page the name of the publisher of the
          Modified Version, as the publisher.

       D. Preserve all the copyright notices of the Document.

       E. Add an appropriate copyright notice for your modifications
          adjacent to the other copyright notices.

       F. Include, immediately after the copyright notices, a license
          notice giving the public permission to use the Modified
          Version under the terms of this License, in the form shown in
          the Addendum below.

       G. Preserve in that license notice the full lists of Invariant
          Sections and required Cover Texts given in the Document's
          license notice.

       H. Include an unaltered copy of this License.

       I. Preserve the section Entitled "History", Preserve its Title,
          and add to it an item stating at least the title, year, new
          authors, and publisher of the Modified Version as given on
          the Title Page.  If there is no section Entitled "History" in
          the Document, create one stating the title, year, authors,
          and publisher of the Document as given on its Title Page,
          then add an item describing the Modified Version as stated in
          the previous sentence.

       J. Preserve the network location, if any, given in the Document
          for public access to a Transparent copy of the Document, and
          likewise the network locations given in the Document for
          previous versions it was based on.  These may be placed in
          the "History" section.  You may omit a network location for a
          work that was published at least four years before the
          Document itself, or if the original publisher of the version
          it refers to gives permission.

       K. For any section Entitled "Acknowledgements" or "Dedications",
          Preserve the Title of the section, and preserve in the
          section all the substance and tone of each of the contributor
          acknowledgements and/or dedications given therein.

       L. Preserve all the Invariant Sections of the Document,
          unaltered in their text and in their titles.  Section numbers
          or the equivalent are not considered part of the section
          titles.

       M. Delete any section Entitled "Endorsements".  Such a section
          may not be included in the Modified Version.

       N. Do not retitle any existing section to be Entitled
          "Endorsements" or to conflict in title with any Invariant
          Section.

       O. Preserve any Warranty Disclaimers.

     If the Modified Version includes new front-matter sections or
     appendices that qualify as Secondary Sections and contain no
     material copied from the Document, you may at your option
     designate some or all of these sections as invariant.  To do this,
     add their titles to the list of Invariant Sections in the Modified
     Version's license notice.  These titles must be distinct from any
     other section titles.

     You may add a section Entitled "Endorsements", provided it contains
     nothing but endorsements of your Modified Version by various
     parties--for example, statements of peer review or that the text
     has been approved by an organization as the authoritative
     definition of a standard.

     You may add a passage of up to five words as a Front-Cover Text,
     and a passage of up to 25 words as a Back-Cover Text, to the end
     of the list of Cover Texts in the Modified Version.  Only one
     passage of Front-Cover Text and one of Back-Cover Text may be
     added by (or through arrangements made by) any one entity.  If the
     Document already includes a cover text for the same cover,
     previously added by you or by arrangement made by the same entity
     you are acting on behalf of, you may not add another; but you may
     replace the old one, on explicit permission from the previous
     publisher that added the old one.

     The author(s) and publisher(s) of the Document do not by this
     License give permission to use their names for publicity for or to
     assert or imply endorsement of any Modified Version.

  5. COMBINING DOCUMENTS

     You may combine the Document with other documents released under
     this License, under the terms defined in section 4 above for
     modified versions, provided that you include in the combination
     all of the Invariant Sections of all of the original documents,
     unmodified, and list them all as Invariant Sections of your
     combined work in its license notice, and that you preserve all
     their Warranty Disclaimers.

     The combined work need only contain one copy of this License, and
     multiple identical Invariant Sections may be replaced with a single
     copy.  If there are multiple Invariant Sections with the same name
     but different contents, make the title of each such section unique
     by adding at the end of it, in parentheses, the name of the
     original author or publisher of that section if known, or else a
     unique number.  Make the same adjustment to the section titles in
     the list of Invariant Sections in the license notice of the
     combined work.

     In the combination, you must combine any sections Entitled
     "History" in the various original documents, forming one section
     Entitled "History"; likewise combine any sections Entitled
     "Acknowledgements", and any sections Entitled "Dedications".  You
     must delete all sections Entitled "Endorsements."

  6. COLLECTIONS OF DOCUMENTS

     You may make a collection consisting of the Document and other
     documents released under this License, and replace the individual
     copies of this License in the various documents with a single copy
     that is included in the collection, provided that you follow the
     rules of this License for verbatim copying of each of the
     documents in all other respects.

     You may extract a single document from such a collection, and
     distribute it individually under this License, provided you insert
     a copy of this License into the extracted document, and follow
     this License in all other respects regarding verbatim copying of
     that document.

  7. AGGREGATION WITH INDEPENDENT WORKS

     A compilation of the Document or its derivatives with other
     separate and independent documents or works, in or on a volume of
     a storage or distribution medium, is called an "aggregate" if the
     copyright resulting from the compilation is not used to limit the
     legal rights of the compilation's users beyond what the individual
     works permit.  When the Document is included in an aggregate, this
     License does not apply to the other works in the aggregate which
     are not themselves derivative works of the Document.

     If the Cover Text requirement of section 3 is applicable to these
     copies of the Document, then if the Document is less than one half
     of the entire aggregate, the Document's Cover Texts may be placed
     on covers that bracket the Document within the aggregate, or the
     electronic equivalent of covers if the Document is in electronic
     form.  Otherwise they must appear on printed covers that bracket
     the whole aggregate.

  8. TRANSLATION

     Translation is considered a kind of modification, so you may
     distribute translations of the Document under the terms of section
     4.  Replacing Invariant Sections with translations requires special
     permission from their copyright holders, but you may include
     translations of some or all Invariant Sections in addition to the
     original versions of these Invariant Sections.  You may include a
     translation of this License, and all the license notices in the
     Document, and any Warranty Disclaimers, provided that you also
     include the original English version of this License and the
     original versions of those notices and disclaimers.  In case of a
     disagreement between the translation and the original version of
     this License or a notice or disclaimer, the original version will
     prevail.

     If a section in the Document is Entitled "Acknowledgements",
     "Dedications", or "History", the requirement (section 4) to
     Preserve its Title (section 1) will typically require changing the
     actual title.

  9. TERMINATION

     You may not copy, modify, sublicense, or distribute the Document
     except as expressly provided for under this License.  Any other
     attempt to copy, modify, sublicense or distribute the Document is
     void, and will automatically terminate your rights under this
     License.  However, parties who have received copies, or rights,
     from you under this License will not have their licenses
     terminated so long as such parties remain in full compliance.

 10. FUTURE REVISIONS OF THIS LICENSE

     The Free Software Foundation may publish new, revised versions of
     the GNU Free Documentation License from time to time.  Such new
     versions will be similar in spirit to the present version, but may
     differ in detail to address new problems or concerns.  See
     `http://www.gnu.org/copyleft/'.

     Each version of the License is given a distinguishing version
     number.  If the Document specifies that a particular numbered
     version of this License "or any later version" applies to it, you
     have the option of following the terms and conditions either of
     that specified version or of any later version that has been
     published (not as a draft) by the Free Software Foundation.  If
     the Document does not specify a version number of this License,
     you may choose any version ever published (not as a draft) by the
     Free Software Foundation.

D.1 ADDENDUM: How to use this License for your documents
========================================================

To use this License in a document you have written, include a copy of
the License in the document and put the following copyright and license
notices just after the title page:

       Copyright (C)  YEAR  YOUR NAME.
       Permission is granted to copy, distribute and/or modify this document
       under the terms of the GNU Free Documentation License, Version 1.2
       or any later version published by the Free Software Foundation;
       with no Invariant Sections, no Front-Cover Texts, and no Back-Cover
       Texts.  A copy of the license is included in the section entitled ``GNU
       Free Documentation License''.

   If you have Invariant Sections, Front-Cover Texts and Back-Cover
Texts, replace the "with...Texts." line with this:

         with the Invariant Sections being LIST THEIR TITLES, with
         the Front-Cover Texts being LIST, and with the Back-Cover Texts
         being LIST.

   If you have Invariant Sections without Cover Texts, or some other
combination of the three, merge those two alternatives to suit the
situation.

   If your document contains nontrivial examples of program code, we
recommend releasing these examples in parallel under your choice of
free software license, such as the GNU General Public License, to
permit their use in free software.


File: make.info,  Node: Concept Index,  Next: Name Index,  Prev: GNU Free Documentation License,  Up: Top

Index of Concepts
*****************

 [index ]
* Menu:

* # (comments), in commands:             Command Syntax.      (line  27)
* # (comments), in makefile:             Makefile Contents.   (line  41)
* #include:                              Automatic Prerequisites.
                                                              (line  16)
* $, in function call:                   Syntax of Functions. (line   6)
* $, in rules:                           Rule Syntax.         (line  32)
* $, in variable name:                   Computed Names.      (line   6)
* $, in variable reference:              Reference.           (line   6)
* %, in pattern rules:                   Pattern Intro.       (line   9)
* %, quoting in patsubst:                Text Functions.      (line  26)
* %, quoting in static pattern:          Static Usage.        (line  37)
* %, quoting in vpath:                   Selective Search.    (line  38)
* %, quoting with \ (backslash) <1>:     Text Functions.      (line  26)
* %, quoting with \ (backslash) <2>:     Static Usage.        (line  37)
* %, quoting with \ (backslash):         Selective Search.    (line  38)
* * (wildcard character):                Wildcards.           (line   6)
* +, and command execution:              Instead of Execution.
                                                              (line  58)
* +, and commands:                       MAKE Variable.       (line  18)
* +, and define:                         Sequences.           (line  50)
* +=:                                    Appending.           (line   6)
* +=, expansion:                         Reading Makefiles.   (line  33)
* ,v (RCS file extension):               Catalogue of Rules.  (line 164)
* - (in commands):                       Errors.              (line  19)
* -, and define:                         Sequences.           (line  50)
* --always-make:                         Options Summary.     (line  15)
* --assume-new <1>:                      Options Summary.     (line 242)
* --assume-new:                          Instead of Execution.
                                                              (line  33)
* --assume-new, and recursion:           Options/Recursion.   (line  22)
* --assume-old <1>:                      Options Summary.     (line 147)
* --assume-old:                          Avoiding Compilation.
                                                              (line   6)
* --assume-old, and recursion:           Options/Recursion.   (line  22)
* --check-symlink-times:                 Options Summary.     (line 130)
* --debug:                               Options Summary.     (line  42)
* --directory <1>:                       Options Summary.     (line  26)
* --directory:                           Recursion.           (line  20)
* --directory, and --print-directory:    -w Option.           (line  20)
* --directory, and recursion:            Options/Recursion.   (line  22)
* --dry-run <1>:                         Options Summary.     (line 140)
* --dry-run <2>:                         Instead of Execution.
                                                              (line  14)
* --dry-run:                             Echoing.             (line  18)
* --environment-overrides:               Options Summary.     (line  78)
* --file <1>:                            Options Summary.     (line  84)
* --file <2>:                            Makefile Arguments.  (line   6)
* --file:                                Makefile Names.      (line  23)
* --file, and recursion:                 Options/Recursion.   (line  22)
* --help:                                Options Summary.     (line  90)
* --ignore-errors <1>:                   Options Summary.     (line  94)
* --ignore-errors:                       Errors.              (line  30)
* --include-dir <1>:                     Options Summary.     (line  99)
* --include-dir:                         Include.             (line  52)
* --jobs <1>:                            Options Summary.     (line 106)
* --jobs:                                Parallel.            (line   6)
* --jobs, and recursion:                 Options/Recursion.   (line  25)
* --just-print <1>:                      Options Summary.     (line 139)
* --just-print <2>:                      Instead of Execution.
                                                              (line  14)
* --just-print:                          Echoing.             (line  18)
* --keep-going <1>:                      Options Summary.     (line 115)
* --keep-going <2>:                      Testing.             (line  16)
* --keep-going:                          Errors.              (line  47)
* --load-average <1>:                    Options Summary.     (line 122)
* --load-average:                        Parallel.            (line  57)
* --makefile <1>:                        Options Summary.     (line  85)
* --makefile <2>:                        Makefile Arguments.  (line   6)
* --makefile:                            Makefile Names.      (line  23)
* --max-load <1>:                        Options Summary.     (line 123)
* --max-load:                            Parallel.            (line  57)
* --new-file <1>:                        Options Summary.     (line 241)
* --new-file:                            Instead of Execution.
                                                              (line  33)
* --new-file, and recursion:             Options/Recursion.   (line  22)
* --no-builtin-rules:                    Options Summary.     (line 175)
* --no-builtin-variables:                Options Summary.     (line 188)
* --no-keep-going:                       Options Summary.     (line 203)
* --no-print-directory <1>:              Options Summary.     (line 233)
* --no-print-directory:                  -w Option.           (line  20)
* --old-file <1>:                        Options Summary.     (line 146)
* --old-file:                            Avoiding Compilation.
                                                              (line   6)
* --old-file, and recursion:             Options/Recursion.   (line  22)
* --print-data-base:                     Options Summary.     (line 155)
* --print-directory:                     Options Summary.     (line 225)
* --print-directory, and --directory:    -w Option.           (line  20)
* --print-directory, and recursion:      -w Option.           (line  20)
* --print-directory, disabling:          -w Option.           (line  20)
* --question <1>:                        Options Summary.     (line 167)
* --question:                            Instead of Execution.
                                                              (line  25)
* --quiet <1>:                           Options Summary.     (line 198)
* --quiet:                               Echoing.             (line  24)
* --recon <1>:                           Options Summary.     (line 141)
* --recon <2>:                           Instead of Execution.
                                                              (line  14)
* --recon:                               Echoing.             (line  18)
* --silent <1>:                          Options Summary.     (line 197)
* --silent:                              Echoing.             (line  24)
* --stop:                                Options Summary.     (line 204)
* --touch <1>:                           Options Summary.     (line 212)
* --touch:                               Instead of Execution.
                                                              (line  19)
* --touch, and recursion:                MAKE Variable.       (line  34)
* --version:                             Options Summary.     (line 220)
* --warn-undefined-variables:            Options Summary.     (line 251)
* --what-if <1>:                         Options Summary.     (line 240)
* --what-if:                             Instead of Execution.
                                                              (line  33)
* -B:                                    Options Summary.     (line  14)
* -b:                                    Options Summary.     (line   9)
* -C <1>:                                Options Summary.     (line  25)
* -C:                                    Recursion.           (line  20)
* -C, and -w:                            -w Option.           (line  20)
* -C, and recursion:                     Options/Recursion.   (line  22)
* -d:                                    Options Summary.     (line  33)
* -e:                                    Options Summary.     (line  77)
* -e (shell flag):                       Automatic Prerequisites.
                                                              (line  66)
* -f <1>:                                Options Summary.     (line  83)
* -f <2>:                                Makefile Arguments.  (line   6)
* -f:                                    Makefile Names.      (line  23)
* -f, and recursion:                     Options/Recursion.   (line  22)
* -h:                                    Options Summary.     (line  89)
* -I:                                    Options Summary.     (line  98)
* -i <1>:                                Options Summary.     (line  93)
* -i:                                    Errors.              (line  30)
* -I:                                    Include.             (line  52)
* -j <1>:                                Options Summary.     (line 105)
* -j:                                    Parallel.            (line   6)
* -j, and archive update:                Archive Pitfalls.    (line   6)
* -j, and recursion:                     Options/Recursion.   (line  25)
* -k <1>:                                Options Summary.     (line 114)
* -k <2>:                                Testing.             (line  16)
* -k:                                    Errors.              (line  47)
* -L:                                    Options Summary.     (line 129)
* -l:                                    Options Summary.     (line 121)
* -l (library search):                   Libraries/Search.    (line   6)
* -l (load average):                     Parallel.            (line  57)
* -m:                                    Options Summary.     (line  10)
* -M (to compiler):                      Automatic Prerequisites.
                                                              (line  18)
* -MM (to GNU compiler):                 Automatic Prerequisites.
                                                              (line  68)
* -n <1>:                                Options Summary.     (line 138)
* -n <2>:                                Instead of Execution.
                                                              (line  14)
* -n:                                    Echoing.             (line  18)
* -o <1>:                                Options Summary.     (line 145)
* -o:                                    Avoiding Compilation.
                                                              (line   6)
* -o, and recursion:                     Options/Recursion.   (line  22)
* -p:                                    Options Summary.     (line 154)
* -q <1>:                                Options Summary.     (line 166)
* -q:                                    Instead of Execution.
                                                              (line  25)
* -R:                                    Options Summary.     (line 187)
* -r:                                    Options Summary.     (line 174)
* -S:                                    Options Summary.     (line 202)
* -s <1>:                                Options Summary.     (line 196)
* -s:                                    Echoing.             (line  24)
* -t <1>:                                Options Summary.     (line 211)
* -t:                                    Instead of Execution.
                                                              (line  19)
* -t, and recursion:                     MAKE Variable.       (line  34)
* -v:                                    Options Summary.     (line 219)
* -W:                                    Options Summary.     (line 239)
* -w:                                    Options Summary.     (line 224)
* -W:                                    Instead of Execution.
                                                              (line  33)
* -w, and -C:                            -w Option.           (line  20)
* -w, and recursion:                     -w Option.           (line  20)
* -W, and recursion:                     Options/Recursion.   (line  22)
* -w, disabling:                         -w Option.           (line  20)
* .a (archives):                         Archive Suffix Rules.
                                                              (line   6)
* .C:                                    Catalogue of Rules.  (line  39)
* .c:                                    Catalogue of Rules.  (line  35)
* .cc:                                   Catalogue of Rules.  (line  39)
* .ch:                                   Catalogue of Rules.  (line 151)
* .cpp:                                  Catalogue of Rules.  (line  39)
* .d:                                    Automatic Prerequisites.
                                                              (line  81)
* .def:                                  Catalogue of Rules.  (line  74)
* .dvi:                                  Catalogue of Rules.  (line 151)
* .F:                                    Catalogue of Rules.  (line  49)
* .f:                                    Catalogue of Rules.  (line  49)
* .info:                                 Catalogue of Rules.  (line 158)
* .l:                                    Catalogue of Rules.  (line 124)
* .LIBPATTERNS, and link libraries:      Libraries/Search.    (line   6)
* .ln:                                   Catalogue of Rules.  (line 146)
* .mod:                                  Catalogue of Rules.  (line  74)
* .o:                                    Catalogue of Rules.  (line  35)
* .p:                                    Catalogue of Rules.  (line  45)
* .PRECIOUS intermediate files:          Chained Rules.       (line  56)
* .r:                                    Catalogue of Rules.  (line  49)
* .S:                                    Catalogue of Rules.  (line  82)
* .s:                                    Catalogue of Rules.  (line  79)
* .sh:                                   Catalogue of Rules.  (line 180)
* .sym:                                  Catalogue of Rules.  (line  74)
* .tex:                                  Catalogue of Rules.  (line 151)
* .texi:                                 Catalogue of Rules.  (line 158)
* .texinfo:                              Catalogue of Rules.  (line 158)
* .txinfo:                               Catalogue of Rules.  (line 158)
* .w:                                    Catalogue of Rules.  (line 151)
* .web:                                  Catalogue of Rules.  (line 151)
* .y:                                    Catalogue of Rules.  (line 120)
* :: rules (double-colon):               Double-Colon.        (line   6)
* := <1>:                                Setting.             (line   6)
* :=:                                    Flavors.             (line  56)
* = <1>:                                 Setting.             (line   6)
* =:                                     Flavors.             (line  10)
* =, expansion:                          Reading Makefiles.   (line  33)
* ? (wildcard character):                Wildcards.           (line   6)
* ?= <1>:                                Setting.             (line   6)
* ?=:                                    Flavors.             (line 129)
* ?=, expansion:                         Reading Makefiles.   (line  33)
* @ (in commands):                       Echoing.             (line   6)
* @, and define:                         Sequences.           (line  50)
* [...] (wildcard characters):           Wildcards.           (line   6)
* \ (backslash), for continuation lines: Simple Makefile.     (line  40)
* \ (backslash), in commands:            Splitting Lines.     (line   6)
* \ (backslash), to quote % <1>:         Text Functions.      (line  26)
* \ (backslash), to quote % <2>:         Static Usage.        (line  37)
* \ (backslash), to quote %:             Selective Search.    (line  38)
* __.SYMDEF:                             Archive Symbols.     (line   6)
* abspath:                               File Name Functions. (line 121)
* algorithm for directory search:        Search Algorithm.    (line   6)
* all (standard target):                 Goals.               (line  72)
* appending to variables:                Appending.           (line   6)
* ar:                                    Implicit Variables.  (line  41)
* archive:                               Archives.            (line   6)
* archive member targets:                Archive Members.     (line   6)
* archive symbol directory updating:     Archive Symbols.     (line   6)
* archive, and -j:                       Archive Pitfalls.    (line   6)
* archive, and parallel execution:       Archive Pitfalls.    (line   6)
* archive, suffix rule for:              Archive Suffix Rules.
                                                              (line   6)
* Arg list too long:                     Options/Recursion.   (line  57)
* arguments of functions:                Syntax of Functions. (line   6)
* as <1>:                                Implicit Variables.  (line  44)
* as:                                    Catalogue of Rules.  (line  79)
* assembly, rule to compile:             Catalogue of Rules.  (line  79)
* automatic generation of prerequisites <1>: Automatic Prerequisites.
                                                              (line   6)
* automatic generation of prerequisites: Include.             (line  50)
* automatic variables:                   Automatic Variables. (line   6)
* automatic variables in prerequisites:  Automatic Variables. (line  17)
* backquotes:                            Shell Function.      (line   6)
* backslash (\), for continuation lines: Simple Makefile.     (line  40)
* backslash (\), in commands:            Splitting Lines.     (line   6)
* backslash (\), to quote % <1>:         Text Functions.      (line  26)
* backslash (\), to quote % <2>:         Static Usage.        (line  37)
* backslash (\), to quote %:             Selective Search.    (line  38)
* backslashes in pathnames and wildcard expansion: Wildcard Pitfall.
                                                              (line  31)
* basename:                              File Name Functions. (line  57)
* binary packages:                       Install Command Categories.
                                                              (line  80)
* broken pipe:                           Parallel.            (line  30)
* bugs, reporting:                       Bugs.                (line   6)
* built-in special targets:              Special Targets.     (line   6)
* C++, rule to compile:                  Catalogue of Rules.  (line  39)
* C, rule to compile:                    Catalogue of Rules.  (line  35)
* cc <1>:                                Implicit Variables.  (line  47)
* cc:                                    Catalogue of Rules.  (line  35)
* cd (shell command) <1>:                MAKE Variable.       (line  16)
* cd (shell command):                    Execution.           (line  10)
* chains of rules:                       Chained Rules.       (line   6)
* check (standard target):               Goals.               (line 114)
* clean (standard target):               Goals.               (line  75)
* clean target <1>:                      Cleanup.             (line  11)
* clean target:                          Simple Makefile.     (line  83)
* cleaning up:                           Cleanup.             (line   6)
* clobber (standard target):             Goals.               (line  86)
* co <1>:                                Implicit Variables.  (line  56)
* co:                                    Catalogue of Rules.  (line 164)
* combining rules by prerequisite:       Combine By Prerequisite.
                                                              (line   6)
* command line variable definitions, and recursion: Options/Recursion.
                                                              (line  17)
* command line variables:                Overriding.          (line   6)
* command syntax:                        Command Syntax.      (line   6)
* commands:                              Rule Syntax.         (line  26)
* commands setting shell variables:      Execution.           (line  10)
* commands, backslash (\) in:            Splitting Lines.     (line   6)
* commands, comments in:                 Command Syntax.      (line  27)
* commands, echoing:                     Echoing.             (line   6)
* commands, empty:                       Empty Commands.      (line   6)
* commands, errors in:                   Errors.              (line   6)
* commands, execution:                   Execution.           (line   6)
* commands, execution in parallel:       Parallel.            (line   6)
* commands, expansion:                   Shell Function.      (line   6)
* commands, how to write:                Commands.            (line   6)
* commands, instead of executing:        Instead of Execution.
                                                              (line   6)
* commands, introduction to:             Rule Introduction.   (line   8)
* commands, quoting newlines in:         Splitting Lines.     (line   6)
* commands, sequences of:                Sequences.           (line   6)
* commands, splitting:                   Splitting Lines.     (line   6)
* commands, using variables in:          Variables in Commands.
                                                              (line   6)
* comments, in commands:                 Command Syntax.      (line  27)
* comments, in makefile:                 Makefile Contents.   (line  41)
* compatibility:                         Features.            (line   6)
* compatibility in exporting:            Variables/Recursion. (line 105)
* compilation, testing:                  Testing.             (line   6)
* computed variable name:                Computed Names.      (line   6)
* conditional expansion:                 Conditional Functions.
                                                              (line   6)
* conditional variable assignment:       Flavors.             (line 129)
* conditionals:                          Conditionals.        (line   6)
* continuation lines:                    Simple Makefile.     (line  40)
* controlling make:                      Make Control Functions.
                                                              (line   6)
* conventions for makefiles:             Makefile Conventions.
                                                              (line   6)
* ctangle <1>:                           Implicit Variables.  (line 107)
* ctangle:                               Catalogue of Rules.  (line 151)
* cweave <1>:                            Implicit Variables.  (line 101)
* cweave:                                Catalogue of Rules.  (line 151)
* data base of make rules:               Options Summary.     (line 155)
* deducing commands (implicit rules):    make Deduces.        (line   6)
* default directories for included makefiles: Include.        (line  52)
* default goal <1>:                      Rules.               (line  11)
* default goal:                          How Make Works.      (line  11)
* default makefile name:                 Makefile Names.      (line   6)
* default rules, last-resort:            Last Resort.         (line   6)
* define, expansion:                     Reading Makefiles.   (line  33)
* defining variables verbatim:           Defining.            (line   6)
* deletion of target files <1>:          Interrupts.          (line   6)
* deletion of target files:              Errors.              (line  64)
* directive:                             Makefile Contents.   (line  28)
* directories, printing them:            -w Option.           (line   6)
* directories, updating archive symbol:  Archive Symbols.     (line   6)
* directory part:                        File Name Functions. (line  17)
* directory search (VPATH):              Directory Search.    (line   6)
* directory search (VPATH), and implicit rules: Implicit/Search.
                                                              (line   6)
* directory search (VPATH), and link libraries: Libraries/Search.
                                                              (line   6)
* directory search (VPATH), and shell commands: Commands/Search.
                                                              (line   6)
* directory search algorithm:            Search Algorithm.    (line   6)
* directory search, traditional (GPATH): Search Algorithm.    (line  42)
* dist (standard target):                Goals.               (line 106)
* distclean (standard target):           Goals.               (line  84)
* dollar sign ($), in function call:     Syntax of Functions. (line   6)
* dollar sign ($), in rules:             Rule Syntax.         (line  32)
* dollar sign ($), in variable name:     Computed Names.      (line   6)
* dollar sign ($), in variable reference: Reference.          (line   6)
* DOS, choosing a shell in:              Choosing the Shell.  (line  36)
* double-colon rules:                    Double-Colon.        (line   6)
* duplicate words, removing:             Text Functions.      (line 155)
* E2BIG:                                 Options/Recursion.   (line  57)
* echoing of commands:                   Echoing.             (line   6)
* editor:                                Introduction.        (line  22)
* Emacs (M-x compile):                   Errors.              (line  62)
* empty commands:                        Empty Commands.      (line   6)
* empty targets:                         Empty Targets.       (line   6)
* environment:                           Environment.         (line   6)
* environment, and recursion:            Variables/Recursion. (line   6)
* environment, SHELL in:                 Choosing the Shell.  (line  10)
* error, stopping on:                    Make Control Functions.
                                                              (line  11)
* errors (in commands):                  Errors.              (line   6)
* errors with wildcards:                 Wildcard Pitfall.    (line   6)
* evaluating makefile syntax:            Eval Function.       (line   6)
* execution, in parallel:                Parallel.            (line   6)
* execution, instead of:                 Instead of Execution.
                                                              (line   6)
* execution, of commands:                Execution.           (line   6)
* exit status (errors):                  Errors.              (line   6)
* exit status of make:                   Running.             (line  18)
* expansion, secondary:                  Secondary Expansion. (line   6)
* explicit rule, definition of:          Makefile Contents.   (line  10)
* explicit rule, expansion:              Reading Makefiles.   (line  62)
* explicit rules, secondary expansion of: Secondary Expansion.
                                                              (line 106)
* exporting variables:                   Variables/Recursion. (line   6)
* f77 <1>:                               Implicit Variables.  (line  64)
* f77:                                   Catalogue of Rules.  (line  49)
* FDL, GNU Free Documentation License:   GNU Free Documentation License.
                                                              (line   6)
* features of GNU make:                  Features.            (line   6)
* features, missing:                     Missing.             (line   6)
* file name functions:                   File Name Functions. (line   6)
* file name of makefile:                 Makefile Names.      (line   6)
* file name of makefile, how to specify: Makefile Names.      (line  30)
* file name prefix, adding:              File Name Functions. (line  79)
* file name suffix:                      File Name Functions. (line  43)
* file name suffix, adding:              File Name Functions. (line  68)
* file name with wildcards:              Wildcards.           (line   6)
* file name, abspath of:                 File Name Functions. (line 121)
* file name, basename of:                File Name Functions. (line  57)
* file name, directory part:             File Name Functions. (line  17)
* file name, nondirectory part:          File Name Functions. (line  27)
* file name, realpath of:                File Name Functions. (line 114)
* files, assuming new:                   Instead of Execution.
                                                              (line  33)
* files, assuming old:                   Avoiding Compilation.
                                                              (line   6)
* files, avoiding recompilation of:      Avoiding Compilation.
                                                              (line   6)
* files, intermediate:                   Chained Rules.       (line  16)
* filtering out words:                   Text Functions.      (line 132)
* filtering words:                       Text Functions.      (line 114)
* finding strings:                       Text Functions.      (line 103)
* flags:                                 Options Summary.     (line   6)
* flags for compilers:                   Implicit Variables.  (line   6)
* flavor of variable:                    Flavor Function.     (line   6)
* flavors of variables:                  Flavors.             (line   6)
* FORCE:                                 Force Targets.       (line   6)
* force targets:                         Force Targets.       (line   6)
* Fortran, rule to compile:              Catalogue of Rules.  (line  49)
* functions:                             Functions.           (line   6)
* functions, for controlling make:       Make Control Functions.
                                                              (line   6)
* functions, for file names:             File Name Functions. (line   6)
* functions, for text:                   Text Functions.      (line   6)
* functions, syntax of:                  Syntax of Functions. (line   6)
* functions, user defined:               Call Function.       (line   6)
* g++ <1>:                               Implicit Variables.  (line  53)
* g++:                                   Catalogue of Rules.  (line  39)
* gcc:                                   Catalogue of Rules.  (line  35)
* generating prerequisites automatically <1>: Automatic Prerequisites.
                                                              (line   6)
* generating prerequisites automatically: Include.            (line  50)
* get <1>:                               Implicit Variables.  (line  67)
* get:                                   Catalogue of Rules.  (line 173)
* globbing (wildcards):                  Wildcards.           (line   6)
* goal:                                  How Make Works.      (line  11)
* goal, default <1>:                     Rules.               (line  11)
* goal, default:                         How Make Works.      (line  11)
* goal, how to specify:                  Goals.               (line   6)
* home directory:                        Wildcards.           (line  11)
* IEEE Standard 1003.2:                  Overview.            (line  13)
* ifdef, expansion:                      Reading Makefiles.   (line  51)
* ifeq, expansion:                       Reading Makefiles.   (line  51)
* ifndef, expansion:                     Reading Makefiles.   (line  51)
* ifneq, expansion:                      Reading Makefiles.   (line  51)
* implicit rule:                         Implicit Rules.      (line   6)
* implicit rule, and directory search:   Implicit/Search.     (line   6)
* implicit rule, and VPATH:              Implicit/Search.     (line   6)
* implicit rule, definition of:          Makefile Contents.   (line  16)
* implicit rule, expansion:              Reading Makefiles.   (line  62)
* implicit rule, how to use:             Using Implicit.      (line   6)
* implicit rule, introduction to:        make Deduces.        (line   6)
* implicit rule, predefined:             Catalogue of Rules.  (line   6)
* implicit rule, search algorithm:       Implicit Rule Search.
                                                              (line   6)
* implicit rules, secondary expansion of: Secondary Expansion.
                                                              (line 146)
* included makefiles, default directories: Include.           (line  52)
* including (MAKEFILE_LIST variable):    MAKEFILE_LIST Variable.
                                                              (line   6)
* including (MAKEFILES variable):        MAKEFILES Variable.  (line   6)
* including other makefiles:             Include.             (line   6)
* incompatibilities:                     Missing.             (line   6)
* Info, rule to format:                  Catalogue of Rules.  (line 158)
* install (standard target):             Goals.               (line  92)
* intermediate files:                    Chained Rules.       (line  16)
* intermediate files, preserving:        Chained Rules.       (line  46)
* intermediate targets, explicit:        Special Targets.     (line  44)
* interrupt:                             Interrupts.          (line   6)
* job slots:                             Parallel.            (line   6)
* job slots, and recursion:              Options/Recursion.   (line  25)
* jobs, limiting based on load:          Parallel.            (line  57)
* joining lists of words:                File Name Functions. (line  90)
* killing (interruption):                Interrupts.          (line   6)
* last-resort default rules:             Last Resort.         (line   6)
* ld:                                    Catalogue of Rules.  (line  86)
* lex <1>:                               Implicit Variables.  (line  71)
* lex:                                   Catalogue of Rules.  (line 124)
* Lex, rule to run:                      Catalogue of Rules.  (line 124)
* libraries for linking, directory search: Libraries/Search.  (line   6)
* library archive, suffix rule for:      Archive Suffix Rules.
                                                              (line   6)
* limiting jobs based on load:           Parallel.            (line  57)
* link libraries, and directory search:  Libraries/Search.    (line   6)
* link libraries, patterns matching:     Libraries/Search.    (line   6)
* linking, predefined rule for:          Catalogue of Rules.  (line  86)
* lint <1>:                              Implicit Variables.  (line  78)
* lint:                                  Catalogue of Rules.  (line 146)
* lint, rule to run:                     Catalogue of Rules.  (line 146)
* list of all prerequisites:             Automatic Variables. (line  61)
* list of changed prerequisites:         Automatic Variables. (line  51)
* load average:                          Parallel.            (line  57)
* loops in variable expansion:           Flavors.             (line  44)
* lpr (shell command) <1>:               Empty Targets.       (line  25)
* lpr (shell command):                   Wildcard Examples.   (line  21)
* m2c <1>:                               Implicit Variables.  (line  81)
* m2c:                                   Catalogue of Rules.  (line  74)
* macro:                                 Using Variables.     (line  10)
* make depend:                           Automatic Prerequisites.
                                                              (line  37)
* makefile:                              Introduction.        (line   7)
* makefile name:                         Makefile Names.      (line   6)
* makefile name, how to specify:         Makefile Names.      (line  30)
* makefile rule parts:                   Rule Introduction.   (line   6)
* makefile syntax, evaluating:           Eval Function.       (line   6)
* makefile, and MAKEFILES variable:      MAKEFILES Variable.  (line   6)
* makefile, conventions for:             Makefile Conventions.
                                                              (line   6)
* makefile, how make processes:          How Make Works.      (line   6)
* makefile, how to write:                Makefiles.           (line   6)
* makefile, including:                   Include.             (line   6)
* makefile, overriding:                  Overriding Makefiles.
                                                              (line   6)
* makefile, parsing:                     Reading Makefiles.   (line   6)
* makefile, remaking of:                 Remaking Makefiles.  (line   6)
* makefile, simple:                      Simple Makefile.     (line   6)
* makefiles, and MAKEFILE_LIST variable: MAKEFILE_LIST Variable.
                                                              (line   6)
* makefiles, and special variables:      Special Variables.   (line   6)
* makeinfo <1>:                          Implicit Variables.  (line  88)
* makeinfo:                              Catalogue of Rules.  (line 158)
* match-anything rule:                   Match-Anything Rules.
                                                              (line   6)
* match-anything rule, used to override: Overriding Makefiles.
                                                              (line  12)
* missing features:                      Missing.             (line   6)
* mistakes with wildcards:               Wildcard Pitfall.    (line   6)
* modified variable reference:           Substitution Refs.   (line   6)
* Modula-2, rule to compile:             Catalogue of Rules.  (line  74)
* mostlyclean (standard target):         Goals.               (line  78)
* multiple rules for one target:         Multiple Rules.      (line   6)
* multiple rules for one target (::):    Double-Colon.        (line   6)
* multiple targets:                      Multiple Targets.    (line   6)
* multiple targets, in pattern rule:     Pattern Intro.       (line  49)
* name of makefile:                      Makefile Names.      (line   6)
* name of makefile, how to specify:      Makefile Names.      (line  30)
* nested variable reference:             Computed Names.      (line   6)
* newline, quoting, in commands:         Splitting Lines.     (line   6)
* newline, quoting, in makefile:         Simple Makefile.     (line  40)
* nondirectory part:                     File Name Functions. (line  27)
* normal prerequisites:                  Prerequisite Types.  (line   6)
* OBJ:                                   Variables Simplify.  (line  20)
* obj:                                   Variables Simplify.  (line  20)
* OBJECTS:                               Variables Simplify.  (line  20)
* objects:                               Variables Simplify.  (line  14)
* OBJS:                                  Variables Simplify.  (line  20)
* objs:                                  Variables Simplify.  (line  20)
* old-fashioned suffix rules:            Suffix Rules.        (line   6)
* options:                               Options Summary.     (line   6)
* options, and recursion:                Options/Recursion.   (line   6)
* options, setting from environment:     Options/Recursion.   (line  81)
* options, setting in makefiles:         Options/Recursion.   (line  81)
* order of pattern rules:                Pattern Intro.       (line  57)
* order-only prerequisites:              Prerequisite Types.  (line   6)
* origin of variable:                    Origin Function.     (line   6)
* overriding makefiles:                  Overriding Makefiles.
                                                              (line   6)
* overriding variables with arguments:   Overriding.          (line   6)
* overriding with override:              Override Directive.  (line   6)
* parallel execution:                    Parallel.            (line   6)
* parallel execution, and archive update: Archive Pitfalls.   (line   6)
* parallel execution, overriding:        Special Targets.     (line 135)
* parts of makefile rule:                Rule Introduction.   (line   6)
* Pascal, rule to compile:               Catalogue of Rules.  (line  45)
* pattern rule:                          Pattern Intro.       (line   6)
* pattern rule, expansion:               Reading Makefiles.   (line  62)
* pattern rules, order of:               Pattern Intro.       (line  57)
* pattern rules, static (not implicit):  Static Pattern.      (line   6)
* pattern rules, static, syntax of:      Static Usage.        (line   6)
* pattern-specific variables:            Pattern-specific.    (line   6)
* pc <1>:                                Implicit Variables.  (line  84)
* pc:                                    Catalogue of Rules.  (line  45)
* phony targets:                         Phony Targets.       (line   6)
* pitfalls of wildcards:                 Wildcard Pitfall.    (line   6)
* portability:                           Features.            (line   6)
* POSIX:                                 Overview.            (line  13)
* POSIX.2:                               Options/Recursion.   (line  60)
* post-installation commands:            Install Command Categories.
                                                              (line   6)
* pre-installation commands:             Install Command Categories.
                                                              (line   6)
* precious targets:                      Special Targets.     (line  29)
* predefined rules and variables, printing: Options Summary.  (line 155)
* prefix, adding:                        File Name Functions. (line  79)
* prerequisite:                          Rules.               (line   6)
* prerequisite pattern, implicit:        Pattern Intro.       (line  22)
* prerequisite pattern, static (not implicit): Static Usage.  (line  30)
* prerequisite types:                    Prerequisite Types.  (line   6)
* prerequisite, expansion:               Reading Makefiles.   (line  62)
* prerequisites:                         Rule Syntax.         (line  46)
* prerequisites, and automatic variables: Automatic Variables.
                                                              (line  17)
* prerequisites, automatic generation <1>: Automatic Prerequisites.
                                                              (line   6)
* prerequisites, automatic generation:   Include.             (line  50)
* prerequisites, introduction to:        Rule Introduction.   (line   8)
* prerequisites, list of all:            Automatic Variables. (line  61)
* prerequisites, list of changed:        Automatic Variables. (line  51)
* prerequisites, normal:                 Prerequisite Types.  (line   6)
* prerequisites, order-only:             Prerequisite Types.  (line   6)
* prerequisites, varying (static pattern): Static Pattern.    (line   6)
* preserving intermediate files:         Chained Rules.       (line  46)
* preserving with .PRECIOUS <1>:         Chained Rules.       (line  56)
* preserving with .PRECIOUS:             Special Targets.     (line  29)
* preserving with .SECONDARY:            Special Targets.     (line  49)
* print (standard target):               Goals.               (line  97)
* print target <1>:                      Empty Targets.       (line  25)
* print target:                          Wildcard Examples.   (line  21)
* printing directories:                  -w Option.           (line   6)
* printing messages:                     Make Control Functions.
                                                              (line  43)
* printing of commands:                  Echoing.             (line   6)
* printing user warnings:                Make Control Functions.
                                                              (line  35)
* problems and bugs, reporting:          Bugs.                (line   6)
* problems with wildcards:               Wildcard Pitfall.    (line   6)
* processing a makefile:                 How Make Works.      (line   6)
* question mode:                         Instead of Execution.
                                                              (line  25)
* quoting %, in patsubst:                Text Functions.      (line  26)
* quoting %, in static pattern:          Static Usage.        (line  37)
* quoting %, in vpath:                   Selective Search.    (line  38)
* quoting newline, in commands:          Splitting Lines.     (line   6)
* quoting newline, in makefile:          Simple Makefile.     (line  40)
* Ratfor, rule to compile:               Catalogue of Rules.  (line  49)
* RCS, rule to extract from:             Catalogue of Rules.  (line 164)
* reading makefiles:                     Reading Makefiles.   (line   6)
* README:                                Makefile Names.      (line   9)
* realclean (standard target):           Goals.               (line  85)
* realpath:                              File Name Functions. (line 114)
* recompilation:                         Introduction.        (line  22)
* recompilation, avoiding:               Avoiding Compilation.
                                                              (line   6)
* recording events with empty targets:   Empty Targets.       (line   6)
* recursion:                             Recursion.           (line   6)
* recursion, and -C:                     Options/Recursion.   (line  22)
* recursion, and -f:                     Options/Recursion.   (line  22)
* recursion, and -j:                     Options/Recursion.   (line  25)
* recursion, and -o:                     Options/Recursion.   (line  22)
* recursion, and -t:                     MAKE Variable.       (line  34)
* recursion, and -w:                     -w Option.           (line  20)
* recursion, and -W:                     Options/Recursion.   (line  22)
* recursion, and command line variable definitions: Options/Recursion.
                                                              (line  17)
* recursion, and environment:            Variables/Recursion. (line   6)
* recursion, and MAKE variable:          MAKE Variable.       (line   6)
* recursion, and MAKEFILES variable:     MAKEFILES Variable.  (line  14)
* recursion, and options:                Options/Recursion.   (line   6)
* recursion, and printing directories:   -w Option.           (line   6)
* recursion, and variables:              Variables/Recursion. (line   6)
* recursion, level of:                   Variables/Recursion. (line 115)
* recursive variable expansion <1>:      Flavors.             (line   6)
* recursive variable expansion:          Using Variables.     (line   6)
* recursively expanded variables:        Flavors.             (line   6)
* reference to variables <1>:            Advanced.            (line   6)
* reference to variables:                Reference.           (line   6)
* relinking:                             How Make Works.      (line  46)
* remaking makefiles:                    Remaking Makefiles.  (line   6)
* removal of target files <1>:           Interrupts.          (line   6)
* removal of target files:               Errors.              (line  64)
* removing duplicate words:              Text Functions.      (line 155)
* removing targets on failure:           Special Targets.     (line  68)
* removing, to clean up:                 Cleanup.             (line   6)
* reporting bugs:                        Bugs.                (line   6)
* rm:                                    Implicit Variables.  (line 110)
* rm (shell command) <1>:                Errors.              (line  27)
* rm (shell command) <2>:                Phony Targets.       (line  20)
* rm (shell command) <3>:                Wildcard Examples.   (line  12)
* rm (shell command):                    Simple Makefile.     (line  83)
* rule commands:                         Commands.            (line   6)
* rule prerequisites:                    Rule Syntax.         (line  46)
* rule syntax:                           Rule Syntax.         (line   6)
* rule targets:                          Rule Syntax.         (line  18)
* rule, double-colon (::):               Double-Colon.        (line   6)
* rule, explicit, definition of:         Makefile Contents.   (line  10)
* rule, how to write:                    Rules.               (line   6)
* rule, implicit:                        Implicit Rules.      (line   6)
* rule, implicit, and directory search:  Implicit/Search.     (line   6)
* rule, implicit, and VPATH:             Implicit/Search.     (line   6)
* rule, implicit, chains of:             Chained Rules.       (line   6)
* rule, implicit, definition of:         Makefile Contents.   (line  16)
* rule, implicit, how to use:            Using Implicit.      (line   6)
* rule, implicit, introduction to:       make Deduces.        (line   6)
* rule, implicit, predefined:            Catalogue of Rules.  (line   6)
* rule, introduction to:                 Rule Introduction.   (line   6)
* rule, multiple for one target:         Multiple Rules.      (line   6)
* rule, no commands or prerequisites:    Force Targets.       (line   6)
* rule, pattern:                         Pattern Intro.       (line   6)
* rule, static pattern:                  Static Pattern.      (line   6)
* rule, static pattern versus implicit:  Static versus Implicit.
                                                              (line   6)
* rule, with multiple targets:           Multiple Targets.    (line   6)
* rules, and $:                          Rule Syntax.         (line  32)
* s. (SCCS file prefix):                 Catalogue of Rules.  (line 173)
* SCCS, rule to extract from:            Catalogue of Rules.  (line 173)
* search algorithm, implicit rule:       Implicit Rule Search.
                                                              (line   6)
* search path for prerequisites (VPATH): Directory Search.    (line   6)
* search path for prerequisites (VPATH), and implicit rules: Implicit/Search.
                                                              (line   6)
* search path for prerequisites (VPATH), and link libraries: Libraries/Search.
                                                              (line   6)
* searching for strings:                 Text Functions.      (line 103)
* secondary expansion:                   Secondary Expansion. (line   6)
* secondary expansion and explicit rules: Secondary Expansion.
                                                              (line 106)
* secondary expansion and implicit rules: Secondary Expansion.
                                                              (line 146)
* secondary expansion and static pattern rules: Secondary Expansion.
                                                              (line 138)
* secondary files:                       Chained Rules.       (line  46)
* secondary targets:                     Special Targets.     (line  49)
* sed (shell command):                   Automatic Prerequisites.
                                                              (line  73)
* selecting a word:                      Text Functions.      (line 159)
* selecting word lists:                  Text Functions.      (line 168)
* sequences of commands:                 Sequences.           (line   6)
* setting options from environment:      Options/Recursion.   (line  81)
* setting options in makefiles:          Options/Recursion.   (line  81)
* setting variables:                     Setting.             (line   6)
* several rules for one target:          Multiple Rules.      (line   6)
* several targets in a rule:             Multiple Targets.    (line   6)
* shar (standard target):                Goals.               (line 103)
* shell command:                         Simple Makefile.     (line  72)
* shell command, and directory search:   Commands/Search.     (line   6)
* shell command, execution:              Execution.           (line   6)
* shell command, function for:           Shell Function.      (line   6)
* shell file name pattern (in include):  Include.             (line  13)
* shell variables, setting in commands:  Execution.           (line  10)
* shell wildcards (in include):          Include.             (line  13)
* shell, choosing the:                   Choosing the Shell.  (line   6)
* SHELL, exported value:                 Variables/Recursion. (line  23)
* SHELL, import from environment:        Environment.         (line  37)
* shell, in DOS and Windows:             Choosing the Shell.  (line  36)
* SHELL, MS-DOS specifics:               Choosing the Shell.  (line  42)
* SHELL, value of:                       Choosing the Shell.  (line   6)
* signal:                                Interrupts.          (line   6)
* silent operation:                      Echoing.             (line   6)
* simple makefile:                       Simple Makefile.     (line   6)
* simple variable expansion:             Using Variables.     (line   6)
* simplifying with variables:            Variables Simplify.  (line   6)
* simply expanded variables:             Flavors.             (line  56)
* sorting words:                         Text Functions.      (line 146)
* spaces, in variable values:            Flavors.             (line 103)
* spaces, stripping:                     Text Functions.      (line  80)
* special targets:                       Special Targets.     (line   6)
* special variables:                     Special Variables.   (line   6)
* specifying makefile name:              Makefile Names.      (line  30)
* splitting commands:                    Splitting Lines.     (line   6)
* standard input:                        Parallel.            (line  30)
* standards conformance:                 Overview.            (line  13)
* standards for makefiles:               Makefile Conventions.
                                                              (line   6)
* static pattern rule:                   Static Pattern.      (line   6)
* static pattern rule, syntax of:        Static Usage.        (line   6)
* static pattern rule, versus implicit:  Static versus Implicit.
                                                              (line   6)
* static pattern rules, secondary expansion of: Secondary Expansion.
                                                              (line 138)
* stem <1>:                              Pattern Match.       (line   6)
* stem:                                  Static Usage.        (line  17)
* stem, variable for:                    Automatic Variables. (line  77)
* stopping make:                         Make Control Functions.
                                                              (line  11)
* strings, searching for:                Text Functions.      (line 103)
* stripping whitespace:                  Text Functions.      (line  80)
* sub-make:                              Variables/Recursion. (line   6)
* subdirectories, recursion for:         Recursion.           (line   6)
* substitution variable reference:       Substitution Refs.   (line   6)
* suffix rule:                           Suffix Rules.        (line   6)
* suffix rule, for archive:              Archive Suffix Rules.
                                                              (line   6)
* suffix, adding:                        File Name Functions. (line  68)
* suffix, function to find:              File Name Functions. (line  43)
* suffix, substituting in variables:     Substitution Refs.   (line   6)
* switches:                              Options Summary.     (line   6)
* symbol directories, updating archive:  Archive Symbols.     (line   6)
* syntax of commands:                    Command Syntax.      (line   6)
* syntax of rules:                       Rule Syntax.         (line   6)
* tab character (in commands):           Rule Syntax.         (line  26)
* tabs in rules:                         Rule Introduction.   (line  21)
* TAGS (standard target):                Goals.               (line 111)
* tangle <1>:                            Implicit Variables.  (line 104)
* tangle:                                Catalogue of Rules.  (line 151)
* tar (standard target):                 Goals.               (line 100)
* target:                                Rules.               (line   6)
* target pattern, implicit:              Pattern Intro.       (line   9)
* target pattern, static (not implicit): Static Usage.        (line  17)
* target, deleting on error:             Errors.              (line  64)
* target, deleting on interrupt:         Interrupts.          (line   6)
* target, expansion:                     Reading Makefiles.   (line  62)
* target, multiple in pattern rule:      Pattern Intro.       (line  49)
* target, multiple rules for one:        Multiple Rules.      (line   6)
* target, touching:                      Instead of Execution.
                                                              (line  19)
* target-specific variables:             Target-specific.     (line   6)
* targets:                               Rule Syntax.         (line  18)
* targets without a file:                Phony Targets.       (line   6)
* targets, built-in special:             Special Targets.     (line   6)
* targets, empty:                        Empty Targets.       (line   6)
* targets, force:                        Force Targets.       (line   6)
* targets, introduction to:              Rule Introduction.   (line   8)
* targets, multiple:                     Multiple Targets.    (line   6)
* targets, phony:                        Phony Targets.       (line   6)
* terminal rule:                         Match-Anything Rules.
                                                              (line   6)
* test (standard target):                Goals.               (line 115)
* testing compilation:                   Testing.             (line   6)
* tex <1>:                               Implicit Variables.  (line  91)
* tex:                                   Catalogue of Rules.  (line 151)
* TeX, rule to run:                      Catalogue of Rules.  (line 151)
* texi2dvi <1>:                          Implicit Variables.  (line  95)
* texi2dvi:                              Catalogue of Rules.  (line 158)
* Texinfo, rule to format:               Catalogue of Rules.  (line 158)
* tilde (~):                             Wildcards.           (line  11)
* touch (shell command) <1>:             Empty Targets.       (line  25)
* touch (shell command):                 Wildcard Examples.   (line  21)
* touching files:                        Instead of Execution.
                                                              (line  19)
* traditional directory search (GPATH):  Search Algorithm.    (line  42)
* types of prerequisites:                Prerequisite Types.  (line   6)
* undefined variables, warning message:  Options Summary.     (line 251)
* updating archive symbol directories:   Archive Symbols.     (line   6)
* updating makefiles:                    Remaking Makefiles.  (line   6)
* user defined functions:                Call Function.       (line   6)
* value:                                 Using Variables.     (line   6)
* value, how a variable gets it:         Values.              (line   6)
* variable:                              Using Variables.     (line   6)
* variable definition:                   Makefile Contents.   (line  22)
* variable references in commands:       Variables in Commands.
                                                              (line   6)
* variables:                             Variables Simplify.  (line   6)
* variables, $ in name:                  Computed Names.      (line   6)
* variables, and implicit rule:          Automatic Variables. (line   6)
* variables, appending to:               Appending.           (line   6)
* variables, automatic:                  Automatic Variables. (line   6)
* variables, command line:               Overriding.          (line   6)
* variables, command line, and recursion: Options/Recursion.  (line  17)
* variables, computed names:             Computed Names.      (line   6)
* variables, conditional assignment:     Flavors.             (line 129)
* variables, defining verbatim:          Defining.            (line   6)
* variables, environment <1>:            Environment.         (line   6)
* variables, environment:                Variables/Recursion. (line   6)
* variables, exporting:                  Variables/Recursion. (line   6)
* variables, flavor of:                  Flavor Function.     (line   6)
* variables, flavors:                    Flavors.             (line   6)
* variables, how they get their values:  Values.              (line   6)
* variables, how to reference:           Reference.           (line   6)
* variables, loops in expansion:         Flavors.             (line  44)
* variables, modified reference:         Substitution Refs.   (line   6)
* variables, nested references:          Computed Names.      (line   6)
* variables, origin of:                  Origin Function.     (line   6)
* variables, overriding:                 Override Directive.  (line   6)
* variables, overriding with arguments:  Overriding.          (line   6)
* variables, pattern-specific:           Pattern-specific.    (line   6)
* variables, recursively expanded:       Flavors.             (line   6)
* variables, setting:                    Setting.             (line   6)
* variables, simply expanded:            Flavors.             (line  56)
* variables, spaces in values:           Flavors.             (line 103)
* variables, substituting suffix in:     Substitution Refs.   (line   6)
* variables, substitution reference:     Substitution Refs.   (line   6)
* variables, target-specific:            Target-specific.     (line   6)
* variables, unexpanded value:           Value Function.      (line   6)
* variables, warning for undefined:      Options Summary.     (line 251)
* varying prerequisites:                 Static Pattern.      (line   6)
* verbatim variable definition:          Defining.            (line   6)
* vpath:                                 Directory Search.    (line   6)
* VPATH, and implicit rules:             Implicit/Search.     (line   6)
* VPATH, and link libraries:             Libraries/Search.    (line   6)
* warnings, printing:                    Make Control Functions.
                                                              (line  35)
* weave <1>:                             Implicit Variables.  (line  98)
* weave:                                 Catalogue of Rules.  (line 151)
* Web, rule to run:                      Catalogue of Rules.  (line 151)
* what if:                               Instead of Execution.
                                                              (line  33)
* whitespace, in variable values:        Flavors.             (line 103)
* whitespace, stripping:                 Text Functions.      (line  80)
* wildcard:                              Wildcards.           (line   6)
* wildcard pitfalls:                     Wildcard Pitfall.    (line   6)
* wildcard, function:                    File Name Functions. (line 107)
* wildcard, in archive member:           Archive Members.     (line  36)
* wildcard, in include:                  Include.             (line  13)
* wildcards and MS-DOS/MS-Windows backslashes: Wildcard Pitfall.
                                                              (line  31)
* Windows, choosing a shell in:          Choosing the Shell.  (line  36)
* word, selecting a:                     Text Functions.      (line 159)
* words, extracting first:               Text Functions.      (line 184)
* words, extracting last:                Text Functions.      (line 197)
* words, filtering:                      Text Functions.      (line 114)
* words, filtering out:                  Text Functions.      (line 132)
* words, finding number:                 Text Functions.      (line 180)
* words, iterating over:                 Foreach Function.    (line   6)
* words, joining lists:                  File Name Functions. (line  90)
* words, removing duplicates:            Text Functions.      (line 155)
* words, selecting lists of:             Text Functions.      (line 168)
* writing rule commands:                 Commands.            (line   6)
* writing rules:                         Rules.               (line   6)
* yacc <1>:                              Implicit Variables.  (line  75)
* yacc <2>:                              Catalogue of Rules.  (line 120)
* yacc:                                  Sequences.           (line  18)
* Yacc, rule to run:                     Catalogue of Rules.  (line 120)
* ~ (tilde):                             Wildcards.           (line  11)


File: make.info,  Node: Name Index,  Prev: Concept Index,  Up: Top

Index of Functions, Variables, & Directives
*******************************************

 [index ]
* Menu:

* $%:                                    Automatic Variables. (line  37)
* $(%D):                                 Automatic Variables. (line 129)
* $(%F):                                 Automatic Variables. (line 130)
* $(*D):                                 Automatic Variables. (line 124)
* $(*F):                                 Automatic Variables. (line 125)
* $(+D):                                 Automatic Variables. (line 147)
* $(+F):                                 Automatic Variables. (line 148)
* $(<D):                                 Automatic Variables. (line 137)
* $(<F):                                 Automatic Variables. (line 138)
* $(?D):                                 Automatic Variables. (line 153)
* $(?F):                                 Automatic Variables. (line 154)
* $(@D):                                 Automatic Variables. (line 113)
* $(@F):                                 Automatic Variables. (line 119)
* $(^D):                                 Automatic Variables. (line 142)
* $(^F):                                 Automatic Variables. (line 143)
* $*:                                    Automatic Variables. (line  73)
* $*, and static pattern:                Static Usage.        (line  81)
* $+:                                    Automatic Variables. (line  63)
* $<:                                    Automatic Variables. (line  43)
* $?:                                    Automatic Variables. (line  48)
* $@:                                    Automatic Variables. (line  30)
* $^:                                    Automatic Variables. (line  53)
* $|:                                    Automatic Variables. (line  69)
* % (automatic variable):                Automatic Variables. (line  37)
* %D (automatic variable):               Automatic Variables. (line 129)
* %F (automatic variable):               Automatic Variables. (line 130)
* * (automatic variable):                Automatic Variables. (line  73)
* * (automatic variable), unsupported bizarre usage: Missing. (line  44)
* *D (automatic variable):               Automatic Variables. (line 124)
* *F (automatic variable):               Automatic Variables. (line 125)
* + (automatic variable):                Automatic Variables. (line  63)
* +D (automatic variable):               Automatic Variables. (line 147)
* +F (automatic variable):               Automatic Variables. (line 148)
* .DEFAULT <1>:                          Last Resort.         (line  23)
* .DEFAULT:                              Special Targets.     (line  20)
* .DEFAULT, and empty commands:          Empty Commands.      (line  16)
* .DEFAULT_GOAL (define default goal):   Special Variables.   (line  10)
* .DELETE_ON_ERROR <1>:                  Errors.              (line  64)
* .DELETE_ON_ERROR:                      Special Targets.     (line  67)
* .EXPORT_ALL_VARIABLES <1>:             Variables/Recursion. (line  99)
* .EXPORT_ALL_VARIABLES:                 Special Targets.     (line 129)
* .FEATURES (list of supported features): Special Variables.  (line  65)
* .IGNORE <1>:                           Errors.              (line  30)
* .IGNORE:                               Special Targets.     (line  74)
* .INCLUDE_DIRS (list of include directories): Special Variables.
                                                              (line  98)
* .INTERMEDIATE:                         Special Targets.     (line  43)
* .LIBPATTERNS:                          Libraries/Search.    (line   6)
* .LOW_RESOLUTION_TIME:                  Special Targets.     (line  86)
* .NOTPARALLEL:                          Special Targets.     (line 134)
* .PHONY <1>:                            Special Targets.     (line   8)
* .PHONY:                                Phony Targets.       (line  22)
* .POSIX:                                Options/Recursion.   (line  60)
* .PRECIOUS <1>:                         Interrupts.          (line  22)
* .PRECIOUS:                             Special Targets.     (line  28)
* .SECONDARY:                            Special Targets.     (line  48)
* .SECONDEXPANSION <1>:                  Special Targets.     (line  57)
* .SECONDEXPANSION:                      Secondary Expansion. (line   6)
* .SILENT <1>:                           Echoing.             (line  24)
* .SILENT:                               Special Targets.     (line 116)
* .SUFFIXES <1>:                         Suffix Rules.        (line  61)
* .SUFFIXES:                             Special Targets.     (line  15)
* .VARIABLES (list of variables):        Special Variables.   (line  56)
* /usr/gnu/include:                      Include.             (line  52)
* /usr/include:                          Include.             (line  52)
* /usr/local/include:                    Include.             (line  52)
* < (automatic variable):                Automatic Variables. (line  43)
* <D (automatic variable):               Automatic Variables. (line 137)
* <F (automatic variable):               Automatic Variables. (line 138)
* ? (automatic variable):                Automatic Variables. (line  48)
* ?D (automatic variable):               Automatic Variables. (line 153)
* ?F (automatic variable):               Automatic Variables. (line 154)
* @ (automatic variable):                Automatic Variables. (line  30)
* @D (automatic variable):               Automatic Variables. (line 113)
* @F (automatic variable):               Automatic Variables. (line 119)
* ^ (automatic variable):                Automatic Variables. (line  53)
* ^D (automatic variable):               Automatic Variables. (line 142)
* ^F (automatic variable):               Automatic Variables. (line 143)
* abspath:                               File Name Functions. (line 121)
* addprefix:                             File Name Functions. (line  79)
* addsuffix:                             File Name Functions. (line  68)
* and:                                   Conditional Functions.
                                                              (line  45)
* AR:                                    Implicit Variables.  (line  41)
* ARFLAGS:                               Implicit Variables.  (line 117)
* AS:                                    Implicit Variables.  (line  44)
* ASFLAGS:                               Implicit Variables.  (line 120)
* basename:                              File Name Functions. (line  57)
* bindir:                                Directory Variables. (line  53)
* call:                                  Call Function.       (line   6)
* CC:                                    Implicit Variables.  (line  47)
* CFLAGS:                                Implicit Variables.  (line 124)
* CO:                                    Implicit Variables.  (line  50)
* COFLAGS:                               Implicit Variables.  (line 130)
* COMSPEC:                               Choosing the Shell.  (line  39)
* CPP:                                   Implicit Variables.  (line  59)
* CPPFLAGS:                              Implicit Variables.  (line 133)
* CTANGLE:                               Implicit Variables.  (line 107)
* CURDIR:                                Recursion.           (line  28)
* CWEAVE:                                Implicit Variables.  (line 101)
* CXX:                                   Implicit Variables.  (line  53)
* CXXFLAGS:                              Implicit Variables.  (line 127)
* define:                                Defining.            (line   6)
* dir:                                   File Name Functions. (line  17)
* else:                                  Conditional Syntax.  (line   6)
* endef:                                 Defining.            (line   6)
* endif:                                 Conditional Syntax.  (line   6)
* error:                                 Make Control Functions.
                                                              (line  11)
* eval:                                  Eval Function.       (line   6)
* exec_prefix:                           Directory Variables. (line  35)
* export:                                Variables/Recursion. (line  40)
* FC:                                    Implicit Variables.  (line  63)
* FFLAGS:                                Implicit Variables.  (line 137)
* filter:                                Text Functions.      (line 114)
* filter-out:                            Text Functions.      (line 132)
* findstring:                            Text Functions.      (line 103)
* firstword:                             Text Functions.      (line 184)
* flavor:                                Flavor Function.     (line   6)
* foreach:                               Foreach Function.    (line   6)
* GET:                                   Implicit Variables.  (line  67)
* GFLAGS:                                Implicit Variables.  (line 140)
* GNUmakefile:                           Makefile Names.      (line   7)
* GPATH:                                 Search Algorithm.    (line  48)
* if:                                    Conditional Functions.
                                                              (line   6)
* ifdef:                                 Conditional Syntax.  (line   6)
* ifeq:                                  Conditional Syntax.  (line   6)
* ifndef:                                Conditional Syntax.  (line   6)
* ifneq:                                 Conditional Syntax.  (line   6)
* include:                               Include.             (line   6)
* info:                                  Make Control Functions.
                                                              (line  43)
* join:                                  File Name Functions. (line  90)
* lastword:                              Text Functions.      (line 197)
* LDFLAGS:                               Implicit Variables.  (line 143)
* LEX:                                   Implicit Variables.  (line  70)
* LFLAGS:                                Implicit Variables.  (line 147)
* libexecdir:                            Directory Variables. (line  66)
* LINT:                                  Implicit Variables.  (line  78)
* LINTFLAGS:                             Implicit Variables.  (line 159)
* M2C:                                   Implicit Variables.  (line  81)
* MAKE <1>:                              Flavors.             (line  84)
* MAKE:                                  MAKE Variable.       (line   6)
* MAKE_RESTARTS (number of times make has restarted): Special Variables.
                                                              (line  49)
* MAKE_VERSION:                          Features.            (line 197)
* MAKECMDGOALS:                          Goals.               (line  30)
* makefile:                              Makefile Names.      (line   7)
* Makefile:                              Makefile Names.      (line   7)
* MAKEFILE_LIST:                         MAKEFILE_LIST Variable.
                                                              (line   6)
* MAKEFILES <1>:                         Variables/Recursion. (line 127)
* MAKEFILES:                             MAKEFILES Variable.  (line   6)
* MAKEFLAGS:                             Options/Recursion.   (line   6)
* MAKEINFO:                              Implicit Variables.  (line  87)
* MAKELEVEL <1>:                         Flavors.             (line  84)
* MAKELEVEL:                             Variables/Recursion. (line 115)
* MAKEOVERRIDES:                         Options/Recursion.   (line  49)
* MAKESHELL (MS-DOS alternative to SHELL): Choosing the Shell.
                                                              (line  25)
* MFLAGS:                                Options/Recursion.   (line  65)
* notdir:                                File Name Functions. (line  27)
* or:                                    Conditional Functions.
                                                              (line  37)
* origin:                                Origin Function.     (line   6)
* OUTPUT_OPTION:                         Catalogue of Rules.  (line 202)
* override:                              Override Directive.  (line   6)
* patsubst <1>:                          Text Functions.      (line  18)
* patsubst:                              Substitution Refs.   (line  28)
* PC:                                    Implicit Variables.  (line  84)
* PFLAGS:                                Implicit Variables.  (line 153)
* prefix:                                Directory Variables. (line  25)
* realpath:                              File Name Functions. (line 114)
* RFLAGS:                                Implicit Variables.  (line 156)
* RM:                                    Implicit Variables.  (line 110)
* sbindir:                               Directory Variables. (line  59)
* shell:                                 Shell Function.      (line   6)
* SHELL:                                 Choosing the Shell.  (line   6)
* SHELL (command execution):             Execution.           (line   6)
* sort:                                  Text Functions.      (line 146)
* strip:                                 Text Functions.      (line  80)
* subst <1>:                             Text Functions.      (line   9)
* subst:                                 Multiple Targets.    (line  28)
* suffix:                                File Name Functions. (line  43)
* SUFFIXES:                              Suffix Rules.        (line  81)
* TANGLE:                                Implicit Variables.  (line 104)
* TEX:                                   Implicit Variables.  (line  91)
* TEXI2DVI:                              Implicit Variables.  (line  94)
* unexport:                              Variables/Recursion. (line  45)
* value:                                 Value Function.      (line   6)
* vpath:                                 Selective Search.    (line   6)
* VPATH:                                 General Search.      (line   6)
* vpath:                                 Directory Search.    (line   6)
* VPATH:                                 Directory Search.    (line   6)
* warning:                               Make Control Functions.
                                                              (line  35)
* WEAVE:                                 Implicit Variables.  (line  98)
* wildcard <1>:                          File Name Functions. (line 107)
* wildcard:                              Wildcard Function.   (line   6)
* word:                                  Text Functions.      (line 159)
* wordlist:                              Text Functions.      (line 168)
* words:                                 Text Functions.      (line 180)
* YACC:                                  Implicit Variables.  (line  74)
* YFLAGS:                                Implicit Variables.  (line 150)
* | (automatic variable):                Automatic Variables. (line  69)


