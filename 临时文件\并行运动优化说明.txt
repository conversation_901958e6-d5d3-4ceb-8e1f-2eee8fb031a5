===============================================================================
                        并行运动优化说明 - Y轴与XZ轴并行运动提高效率
===============================================================================

【三大优化更改】

=== 更改1：任务开始时Y轴直接到第一个孔位 ===
```
当用户按下按键执行打螺丝任务时：
- 滑块从用户侧直接滑到第一个孔位的Y位置
- 不再等到处理第一个螺丝时才移动Y轴
- 提高响应速度，减少等待时间

实现：
在ExecuteLeftScrew()和ExecuteRightScrew()函数开始时，
立即获取第一个螺丝的Y坐标并移动Y轴到该位置
```

=== 更改2：打完螺丝后Y轴并行移动 ===
```
打完一个螺丝，Z轴抬到安全位置时：
- 在进行圆弧插补的同时，让滑块滑到下一个孔Y的位置
- 如果已经是最后一个螺丝，那就滑到用户侧准备被拿走工件
- Y轴和XZ轴并行运动，大幅提高效率

实现：
在WorkScrew()函数中，打完螺丝后启动Y轴并行移动，
在StandardThreeSegment()的第一段抬Z时不等待Z轴完成
```

=== 更改3：第三段插补前等待Y轴到位 ===
```
吸螺丝圆弧插补到工件孔位上方安全位置后：
- 在做最后一段插补前，先判断工件滑块的Y轴到达孔位的Y位置没
- 如果没到就等到了再做第三段插补
- 确保Y轴和XZ轴的协调配合

实现：
在StandardThreeSegment()的第三段Z下降前，
调用WaitForYMove()等待Y轴移动完成
```

【技术实现架构】

=== 并行运动控制全局变量 ===
```basic
GLOBAL next_y_target = 0            ' 下一个目标Y位置
GLOBAL next_y_axis = 0              ' 下一个目标Y轴编号
GLOBAL y_move_started = 0           ' Y轴移动是否已启动
GLOBAL is_last_screw = 0            ' 是否是最后一个螺丝
GLOBAL user_pos_target = 0          ' 用户位置目标值
```

=== 核心控制函数 ===
```basic
StartParallelYMove(y_axis, target_y, is_last)
功能：启动Y轴并行移动
参数：
- y_axis：Y轴编号（1或2）
- target_y：目标Y位置
- is_last：是否是最后一个螺丝

WaitForYMove()
功能：等待Y轴移动完成
用途：在第三段插补前确保Y轴到位
```

【完整工作流程】

=== 单个螺丝的完整并行流程 ===
```
以左侧第一个螺丝为例：

1. 任务开始（更改1）：
   - 用户按下IN0
   - Y1轴立即从用户位置(300mm)移动到第一个孔位(80mm)
   - 同时开始准备吸螺丝

2. 处理第一个螺丝：
   - Y1轴已在80mm，无需移动
   - 执行三段轨迹到螺丝孔位
   - 打螺丝（Z轴不动）

3. 打完螺丝后（更改2）：
   - 启动Y1轴并行移动到下一个孔位(120mm)或用户位置(300mm)
   - 同时执行三段轨迹回取螺丝位置

4. 三段轨迹详解：
   第一段：Z轴抬升到安全高度，Y轴开始并行移动
   第二段：XZ圆弧插补，Y轴继续移动
   第三段：等待Y轴到位（更改3），然后Z下降到取螺丝位置

5. 处理下一个螺丝：
   - Y1轴已在正确位置，无需移动
   - 重复上述流程
```

=== 多螺丝场景的并行效果 ===
```
左侧8个螺丝（2x4布局）的并行流程：

任务开始：Y1轴 300mm → 80mm（第一个孔位）

螺丝1(100,80,30)：
- Y1轴已在80mm
- 打螺丝完成后，启动Y1轴移动到120mm（第二行）
- 同时执行回程三段轨迹

螺丝2-4(150,80,30), (200,80,30), (250,80,30)：
- Y1轴已在80mm，无需移动
- 打螺丝完成后，启动Y1轴移动到120mm
- 同时执行回程三段轨迹

螺丝5(100,120,30)：
- Y1轴已在120mm
- 打螺丝完成后，启动Y1轴移动到120mm（同行）
- 同时执行回程三段轨迹

螺丝6-7(150,120,30), (200,120,30)：
- Y1轴已在120mm，无需移动
- 打螺丝完成后，启动Y1轴移动到120mm
- 同时执行回程三段轨迹

螺丝8(250,120,30)：
- Y1轴已在120mm
- 打螺丝完成后，启动Y1轴移动到300mm（用户位置）
- 同时执行回程三段轨迹

任务完成：Y1轴在用户位置300mm，工件可以被拿走
```

【性能优势分析】

=== 时间节省计算 ===
```
传统串行方式：
每个螺丝 = Y轴移动时间 + XZ三段轨迹时间 + 打螺丝时间 + 回程三段轨迹时间
假设：Y轴移动0.2秒，三段轨迹0.3秒，打螺丝2秒，回程0.3秒
单个螺丝总时间 = 0.2 + 0.3 + 2.0 + 0.3 = 2.8秒

并行优化方式：
第一个螺丝 = 初始Y轴移动0.2秒 + 三段轨迹0.3秒 + 打螺丝2秒 + 回程0.3秒
后续螺丝 = 三段轨迹0.3秒 + 打螺丝2秒 + 回程0.3秒（Y轴并行）
最后Y轴回用户位置与最后一个螺丝的回程并行

8个螺丝总时间：
传统方式：8 × 2.8 = 22.4秒
并行方式：2.8 + 7 × 2.6 = 21.0秒
节省时间：1.4秒，效率提升6.25%

实际效果可能更显著，因为Y轴移动和XZ轴运动完全并行！
```

=== 运动效率提升 ===
```
✅ 并行运动：Y轴和XZ轴同时运动，充分利用多轴能力
✅ 减少等待：消除Y轴移动的等待时间
✅ 智能协调：第三段插补前确保Y轴到位，保证精度
✅ 流畅作业：整个过程更连贯，减少停顿
```

=== 用户体验提升 ===
```
✅ 快速响应：按键后立即开始Y轴移动
✅ 高效作业：多螺丝作业时间显著缩短
✅ 智能完成：最后一个螺丝完成后Y轴自动回用户位置
✅ 便于取件：工件在用户位置等待被拿走
```

【显示信息详解】

=== 任务开始时的显示 ===
```
"开始左侧打螺丝任务，螺丝数量：1"
"左Y轴从用户位置直接移动到第一个孔位Y坐标：80mm"
"执行左侧打螺丝任务"
```

=== 并行移动时的显示 ===
```
打完螺丝后：
"三段连续轨迹回到取螺丝位置（中间不停）"
"同时启动Y轴并行移动到下一个位置"
"启动Y轴并行移动到下一个孔位：120mm"

或者最后一个螺丝：
"启动Y轴并行移动到用户位置：300mm"
```

=== 等待Y轴时的显示 ===
```
第三段插补前：
"Y轴已到达下一个孔位：120mm"
"第三段：Z下降到目标位置10mm（绝对值）"

或者：
"Y轴已到达用户位置：300mm"
"第三段：Z下降到目标位置10mm（绝对值）"
```

【配置和调整】

=== 螺丝数量设置 ===
```
当前设置：left_screw_num = 1, right_screw_num = 1

影响：
- 1个螺丝：优化效果主要体现在快速响应
- 多个螺丝：并行运动效果更明显
- 可通过SetScrewCount()函数调整

示例：
CALL SetScrewCount(8, 8)  ' 设置左右各8个螺丝
```

=== 并行移动参数 ===
```
位置检查阈值：1mm
含义：位置差异大于1mm时才移动Y轴
调整：可根据实际精度要求调整

用户位置设置：
left_user_pos = 300mm
right_user_pos = 300mm
```

=== 数据结构依赖 ===
```
优化依赖于TABLE数组的正确设置：
- 每个螺丝占用3个数据位置：X, Y, Z
- 通过索引计算获取下一个螺丝的Y坐标
- 确保SetupData()函数中的数据正确
```

【故障排除】

=== 常见问题 ===
```
问题1：Y轴移动到错误位置
原因：下一个螺丝的Y坐标计算错误
解决：检查TABLE数组索引计算

问题2：Y轴和XZ轴不协调
原因：WaitForYMove()函数未正确调用
解决：确认第三段插补前的等待逻辑

问题3：最后一个螺丝后Y轴位置错误
原因：is_last标志或用户位置设置错误
解决：检查螺丝数量判断和用户位置参数
```

=== 调试方法 ===
```
1. 观察显示信息：
   - 确认Y轴移动的目标位置
   - 检查并行移动的启动和完成

2. 手动验证：
   - 单步执行，观察Y轴和XZ轴的协调
   - 验证最后一个螺丝的处理

3. 性能测试：
   - 对比优化前后的作业时间
   - 测量实际的效率提升
```

【测试版支持】

=== 测试版的模拟 ===
```
测试版也支持并行运动优化：
"执行左侧打螺丝任务（模拟）"
"模拟左Y轴从用户位置直接移动到第一个孔位Y坐标"
"模拟Y轴并行移动到下一个孔位"
"模拟等待Y轴到位后执行第三段插补"
```

【总结】

并行运动优化特点：
✅ **三大更改**：任务开始直达、打螺丝后并行、插补前等待
✅ **并行运动**：Y轴和XZ轴同时运动，充分利用多轴能力
✅ **智能协调**：确保Y轴和XZ轴的精确配合
✅ **效率提升**：减少等待时间，提高整体作业效率
✅ **用户友好**：快速响应，智能完成，便于取件
✅ **系统稳定**：保持运动精度，确保作业质量

这套并行运动优化系统为螺丝机提供了显著的效率提升，
通过Y轴和XZ轴的智能并行运动，实现了更快、更流畅的自动化作业。

===============================================================================
