===============================================================================
                    螺丝机增强版 - 滑轨控制和队列功能使用说明
===============================================================================

【新增功能概述】
✅ 滑轨自动控制：空闲时在用户侧，作业时在电批侧
✅ 队列控制系统：支持电批忙碌时排队等待
✅ 五轴控制：增加滑轨轴（第5轴）
✅ 智能调度：自动处理任务队列和滑轨移动

【硬件配置】
轴配置：
- 轴0 (X轴)：横向移动轴
- 轴1 (Y1轴)：左侧纵向移动轴
- 轴2 (Y2轴)：右侧纵向移动轴  
- 轴3 (Z轴)：垂直升降轴
- 轴4 (滑轨轴)：滑轨控制轴 ⭐ 新增

滑轨参数：
- 螺距：10mm/圈
- 脉冲当量：100脉冲/mm
- 用户侧位置：0mm（原点）
- 工作侧位置：100mm
- 运动速度：80mm/s

输入信号：
- IN0：左侧开始按键
- IN1：右侧开始按键
- IN2：系统回零按键
- IN3：急停按键
- IN4：手动滑块到用户侧 ⭐ 新增
- IN5：手动滑块到电批侧 ⭐ 新增
- IN8-IN12：正向限位开关（包含滑轨）
- IN13-IN17：负向限位开关（包含滑轨）
- IN18-IN22：原点开关（包含滑轨）

【操作流程】

=== 标准作业流程 ===
1. 系统回零
   - 按下IN2进行五轴回零
   - 回零完成后滑块自动在用户侧

2. 放置工件
   - 滑块在用户侧，方便用户放置待打螺丝的工件
   - 确保工件放置正确

3. 开始作业
   - 按下IN0（左侧）或IN1（右侧）
   - 滑块自动移动到工作侧（电批侧）
   - 系统开始打螺丝作业

4. 自动完成
   - 打完所有螺丝后
   - 滑块自动回到用户侧
   - 用户可以取走工件，放置新工件

=== 队列功能 ===
场景1：电批空闲时
- 用户按下按键 → 滑块移动到工作侧 → 立即开始打螺丝

场景2：电批忙碌时
- 用户按下按键 → 滑块移动到工作侧 → 任务加入队列等待
- 当前任务完成后 → 自动开始队列中的任务

场景3：双侧同时请求
- 左右两侧都可以独立请求
- 系统按先来先服务原则处理

【状态说明】

=== 滑轨状态 ===
- 0：用户侧（方便放置工件）
- 1：工作侧（电批作业位置）
- 2：移动中（正在移动）

=== 队列状态 ===
- 0：无任务
- 1：等待中（已请求，等待电批空闲）
- 2：执行中（正在打螺丝）

=== 电批状态 ===
- 0：空闲（可以接受新任务）
- 1：忙碌（正在打螺丝）

【手动控制】
IN4：手动滑块到用户侧
- 仅在系统待机时有效
- 用于手动调整滑块位置

IN5：手动滑块到电批侧
- 仅在系统待机时有效
- 用于测试或手动调整

【测试功能】
查看系统状态：
CALL ShowStatus()

测试滑轨运动：
CALL TestSlide()

测试队列功能：
CALL TestQueue()

清除所有队列：
CALL ClearQueue()

设置滑轨位置：
CALL SetSlidePos(0, 120)  ' 用户侧0mm，工作侧120mm

【优势特点】

=== 用户友好 ===
✅ 滑块空闲时在用户侧，方便放置工件
✅ 按键后滑块自动移动，无需手动操作
✅ 作业完成后自动回到用户侧

=== 高效生产 ===
✅ 支持队列等待，电批利用率高
✅ 无论电批状态，都可以随时请求作业
✅ 自动调度，减少等待时间

=== 安全可靠 ===
✅ 急停功能清除所有队列
✅ 回零时滑块自动回到用户侧
✅ 完整的状态监控

【典型使用场景】

场景1：单件连续生产
1. 用户放置工件1，按下按键
2. 滑块移动到工作侧，开始打螺丝
3. 打螺丝完成，滑块回到用户侧
4. 用户取走工件1，放置工件2
5. 重复步骤1-4

场景2：双侧交替生产
1. 用户在左侧放置工件，按下IN0
2. 滑块移动到工作侧，开始左侧打螺丝
3. 打螺丝过程中，用户在右侧放置工件，按下IN1
4. 右侧任务加入队列等待
5. 左侧完成后，自动开始右侧任务

场景3：批量预排队
1. 用户连续按下多个按键
2. 任务依次加入队列
3. 系统按顺序自动执行

【注意事项】
1. 必须先进行五轴回零（包含滑轨轴）
2. 滑轨轴需要正确配置限位和原点开关
3. 滑轨螺距参数需要根据实际硬件调整
4. 急停后需要重新回零
5. 手动控制仅在系统待机时有效

【参数调整】
滑轨脉冲当量：
- 当前设置：100脉冲/mm（适用于10mm螺距）
- 如需调整：修改UNITS参数中的第5个值

滑轨位置：
- 用户侧：slide_user_pos = 0（可调整）
- 工作侧：slide_work_pos = 100（可调整）
- 调用SetSlidePos()函数修改

滑轨速度：
- 当前设置：80mm/s
- 如需调整：修改SPEED参数中的第5个值

【故障排除】
1. 滑轨不动作
   - 检查滑轨轴连接
   - 检查限位和原点开关
   - 确认轴4已正确回零

2. 队列不工作
   - 调用ShowStatus()查看队列状态
   - 调用ClearQueue()清除队列
   - 检查电批忙碌状态

3. 滑块位置不对
   - 重新执行回零
   - 调用SetSlidePos()重新设置位置
   - 检查脉冲当量设置

【版本对比】
完美版 → 增强版：
+ 增加滑轨控制（第5轴）
+ 增加队列控制系统
+ 增加用户侧/工作侧自动切换
+ 增加手动滑轨控制
+ 增加更多状态监控
+ 增加测试和调试功能

===============================================================================
