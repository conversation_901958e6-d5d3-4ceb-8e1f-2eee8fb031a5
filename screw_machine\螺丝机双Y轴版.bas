'=============================================================================
' 螺丝机双Y轴版（用双Y轴实现滑轨功能）
' 新增功能：
' 1. 双Y轴滑轨控制：用户侧↔工作侧（电批侧）
' 2. 队列控制：支持电批忙碌时排队等待
' 3. Y轴螺距：10mm/圈，电机转一圈滑块移动10mm
' 4. 用户侧：Y轴在靠近用户位置，工作侧：Y轴在第一个螺丝位置
'=============================================================================

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL axis_home(4)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL left_screw_num              ' 左侧螺丝点位数量
GLOBAL right_screw_num             ' 右侧螺丝点位数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'双Y轴滑轨控制变量
GLOBAL left_user_pos = 50           ' 左侧用户位置（靠近用户侧）
GLOBAL left_work_pos = 80           ' 左侧工作位置（第一个螺丝Y坐标）
GLOBAL right_user_pos = 200         ' 右侧用户位置（靠近用户侧）
GLOBAL right_work_pos = 220         ' 右侧工作位置（第一个螺丝Y坐标）

GLOBAL left_slide_status = 0        ' 左侧滑轨状态：0-用户侧，1-工作侧，2-移动中
GLOBAL right_slide_status = 0       ' 右侧滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue = 0               ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue = 0              ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy = 0         ' 电批忙碌状态：0-空闲，1-忙碌

'螺丝位置数据存储
GLOBAL left_start = 0               ' 左侧螺丝位置数据起始地址
GLOBAL right_start = 100            ' 右侧螺丝位置数据起始地址

'吸螺丝位置
GLOBAL pick_x = 50                  ' 吸螺丝位置X
GLOBAL pick_y = 150                 ' 吸螺丝位置Y  
GLOBAL pick_z = 5                   ' 吸螺丝位置Z

'主程序
CALL InitSystem()
CALL SetupAxis()
CALL SetupData()

PRINT "=== 螺丝机双Y轴版启动 ==="
PRINT "新增功能："
PRINT "1. 双Y轴滑轨控制（用户侧<->工作侧）"
PRINT "2. 队列控制（支持排队等待）"
PRINT "3. Y轴螺距10mm/圈适配"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 左侧开始（左Y轴自动移动到工作位置）"
PRINT "IN1 - 右侧开始（右Y轴自动移动到工作位置）"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动左Y轴到用户侧"
PRINT "IN5 - 手动右Y轴到用户侧"
PRINT "OP0 - 吸螺丝控制"

'主循环
WHILE 1
    CALL ScanInput()
    CALL ProcessQueue()
    CALL UpdateStatus()
    DELAY(50)
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 4              ' 默认左侧4个螺丝
    right_screw_num = 4             ' 默认右侧4个螺丝
    cur_screw = 0
    
    '初始化回零状态
    FOR i = 0 TO 3
        axis_home(i) = 0
    NEXT
    
    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0
    left_slide_status = 0
    right_slide_status = 0
    
    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB SetupAxis()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0, 1, 2, 3)
    ATYPE = 1, 1, 1, 1              ' 步进电机开环脉冲控制
    UNITS = 1000, 100, 100, 1000    ' Y轴脉冲当量100脉冲/mm（10mm螺距）
    SPEED = 100, 80, 80, 50         ' Y轴速度80mm/s
    ACCEL = 1000, 800, 800, 500     ' Y轴加速度800mm/s²
    DECEL = 1000, 800, 800, 500     ' Y轴减速度800mm/s²
    CREEP = 10, 10, 10, 5           ' 回零爬行速度
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11           ' 正限位 IN8-IN11
    REV_IN = 12, 13, 14, 15         ' 负限位 IN12-IN15
    DATUM_IN = 16, 17, 18, 19       ' 原点开关 IN16-IN19
    
    '信号反转(根据实际硬件调整)
    FOR i = 8 TO 19
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    
    PRINT "轴参数设置完成（Y轴适配10mm螺距）"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    '左侧螺丝位置数据(2x2阵列示例)
    '注意：Y坐标使用工作位置作为第一个螺丝位置
    TABLE(0) = 100
    TABLE(1) = left_work_pos        ' 第一个螺丝在工作位置
    TABLE(2) = 0
    TABLE(3) = 150
    TABLE(4) = left_work_pos        ' 第一个螺丝在工作位置
    TABLE(5) = 0
    TABLE(6) = 100
    TABLE(7) = left_work_pos + 40   ' 第二排螺丝
    TABLE(8) = 0
    TABLE(9) = 150
    TABLE(10) = left_work_pos + 40  ' 第二排螺丝
    TABLE(11) = 0
    
    '右侧螺丝位置数据(2x2阵列示例)
    TABLE(100) = 100
    TABLE(101) = right_work_pos     ' 第一个螺丝在工作位置
    TABLE(102) = 0
    TABLE(103) = 150
    TABLE(104) = right_work_pos     ' 第一个螺丝在工作位置
    TABLE(105) = 0
    TABLE(106) = 100
    TABLE(107) = right_work_pos + 40 ' 第二排螺丝
    TABLE(108) = 0
    TABLE(109) = 150
    TABLE(110) = right_work_pos + 40 ' 第二排螺丝
    TABLE(111) = 0
    
    PRINT "数据设置完成"
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num
    PRINT "左侧用户位置：", left_user_pos, "mm，工作位置：", left_work_pos, "mm"
    PRINT "右侧用户位置：", right_user_pos, "mm，工作位置：", right_work_pos, "mm"
END SUB

'================ 输入扫描 ================
GLOBAL SUB ScanInput()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1          ' 设置为等待状态
                PRINT "左侧任务加入队列"
                CALL LeftSlideToWork()  ' 左Y轴移动到工作位置
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF CheckHome() = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1         ' 设置为等待状态
                PRINT "右侧任务加入队列"
                CALL RightSlideToWork() ' 右Y轴移动到工作位置
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "开始系统回零"
            CALL WorkHome()
        ELSE
            PRINT "系统运行中，无法回零"
        ENDIF
    ENDIF
    
    '手动左Y轴到用户侧
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "手动左Y轴到用户侧"
            CALL LeftSlideToUser()
        ELSE
            PRINT "系统运行中，无法手动控制"
        ENDIF
    ENDIF
    
    '手动右Y轴到用户侧
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF sys_status = 0 THEN
            PRINT "手动右Y轴到用户侧"
            CALL RightSlideToUser()
        ELSE
            PRINT "系统运行中，无法手动控制"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        OP(0, OFF)                  ' 关闭吸螺丝
    ENDIF
END SUB

'================ 队列处理 ================
GLOBAL SUB ProcessQueue()
    '检查电批是否空闲
    IF screwdriver_busy = 0 THEN
        '电批空闲，检查是否有等待的任务
        IF left_queue = 1 AND left_slide_status = 1 THEN
            '左侧任务等待中且左Y轴在工作位置
            PRINT "开始执行左侧任务"
            left_queue = 2          ' 设置为执行中
            screwdriver_busy = 1    ' 电批设为忙碌
            CALL WorkLeft()
        ELSEIF right_queue = 1 AND right_slide_status = 1 THEN
            '右侧任务等待中且右Y轴在工作位置
            PRINT "开始执行右侧任务"
            right_queue = 2         ' 设置为执行中
            screwdriver_busy = 1    ' 电批设为忙碌
            CALL WorkRight()
        ENDIF
    ENDIF
END SUB

'================ 左Y轴滑轨控制 ================
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置..."
        left_slide_status = 2       ' 设置为移动中

        BASE(1)
        MOVEABS(left_user_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 0       ' 设置为用户侧
        PRINT "左Y轴已到达用户位置：", left_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB LeftSlideToWork()
    IF left_slide_status <> 1 THEN
        PRINT "左Y轴移动到工作位置..."
        left_slide_status = 2       ' 设置为移动中

        BASE(1)
        MOVEABS(left_work_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 1       ' 设置为工作侧
        PRINT "左Y轴已到达工作位置：", left_work_pos, "mm"
    ENDIF
END SUB

'================ 右Y轴滑轨控制 ================
GLOBAL SUB RightSlideToUser()
    IF right_slide_status <> 0 THEN
        PRINT "右Y轴移动到用户位置..."
        right_slide_status = 2      ' 设置为移动中

        BASE(2)
        MOVEABS(right_user_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_status = 0      ' 设置为用户侧
        PRINT "右Y轴已到达用户位置：", right_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToWork()
    IF right_slide_status <> 1 THEN
        PRINT "右Y轴移动到工作位置..."
        right_slide_status = 2      ' 设置为移动中

        BASE(2)
        MOVEABS(right_work_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_status = 1      ' 设置为工作侧
        PRINT "右Y轴已到达工作位置：", right_work_pos, "mm"
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO 3
        IF axis_home(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = sys_status
    MODBUS_REG(1) = left_screw_num
    MODBUS_REG(2) = right_screw_num
    MODBUS_REG(3) = cur_screw
    MODBUS_REG(4) = left_slide_status
    MODBUS_REG(5) = right_slide_status
    MODBUS_REG(6) = left_queue
    MODBUS_REG(7) = right_queue
    MODBUS_REG(8) = screwdriver_busy

    '更新各轴回零状态
    FOR i = 0 TO 3
        MODBUS_REG(10 + i) = axis_home(i)
    NEXT

    '更新各轴当前位置
    FOR i = 0 TO 3
        MODBUS_IEEE(20 + i) = DPOS(i)
    NEXT
END SUB

'================ 回零作业 ================
GLOBAL SUB WorkHome()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始四轴回零..."

    '按Z-Y-X顺序回零，避免碰撞
    FOR i = 3 TO 0 STEP -1
        PRINT "开始轴", i, "回零"
        axis_home(i) = 1            ' 设置为回零中

        BASE(i)
        DATUM(0) AXIS(i)            ' 清除错误状态
        DELAY(10)
        DATUM(3)                    ' 正向找原点回零

        WAIT UNTIL IDLE(i) = -1
        DELAY(10)

        IF AXISSTATUS(i) = 0 THEN
            axis_home(i) = 2        ' 回零成功
            PRINT "轴", i, "回零成功"
        ELSE
            axis_home(i) = 3        ' 回零失败
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            sys_status = 0
            RETURN
        ENDIF

        DELAY(500)                  ' 轴间延时
    NEXT

    '回零完成后，双Y轴移动到用户位置
    PRINT "双Y轴移动到用户位置..."
    CALL LeftSlideToUser()
    CALL RightSlideToUser()

    PRINT "所有轴回零完成，双Y轴在用户位置"
    sys_status = 0                  ' 回到待机状态
END SUB

'================ 左侧作业 ================
GLOBAL SUB WorkLeft()
    PRINT "执行左侧打螺丝任务"

    FOR screw_idx = 0 TO left_screw_num - 1
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "左侧打螺丝任务完成"

    '任务完成后的处理
    left_queue = 0                  ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '左Y轴回到用户位置
    CALL LeftSlideToUser()
    PRINT "左侧任务完成，左Y轴已回到用户位置"
END SUB

'================ 右侧作业 ================
GLOBAL SUB WorkRight()
    PRINT "执行右侧打螺丝任务"

    FOR screw_idx = 0 TO right_screw_num - 1
        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(right_start + screw_idx * 3)
        screw_y = TABLE(right_start + screw_idx * 3 + 1)
        screw_z = TABLE(right_start + screw_idx * 3 + 2)

        '执行打螺丝流程
        CALL WorkScrew(screw_x, screw_y, screw_z, 2)  ' 2表示右侧Y2轴

        DELAY(500)                  ' 螺丝间延时
    NEXT

    PRINT "右侧打螺丝任务完成"

    '任务完成后的处理
    right_queue = 0                 ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '右Y轴回到用户位置
    CALL RightSlideToUser()
    PRINT "右侧任务完成，右Y轴已回到用户位置"
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 移动到吸螺丝位置
    PRINT "移动到吸螺丝位置"
    CALL MoveToPick(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 直线运动到螺丝孔位
    PRINT "直线运动到螺丝孔位"
    CALL LinearMoveToTarget(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 直线运动回到吸螺丝位置
    PRINT "直线运动回到吸螺丝位置"
    CALL LinearMoveBack(y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    PRINT "螺丝完成"
END SUB

'================ 移动到吸螺丝位置 ================
GLOBAL SUB MoveToPick(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升Z轴到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到吸螺丝位置（分别移动）
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动到目标位置 ================
GLOBAL SUB LinearMoveToTarget(target_x, target_y, target_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到目标XY位置
    MOVEABS(target_x) AXIS(0)
    MOVEABS(target_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到螺丝孔位高度
    MOVEABS(target_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 直线运动回到吸螺丝位置 ================
GLOBAL SUB LinearMoveBack(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动回吸螺丝位置
    MOVEABS(pick_x) AXIS(0)
    MOVEABS(pick_y) AXIS(y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(pick_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '下降到接触位置
    MOVEABS(target_z - 5) AXIS(3)   ' 下降到螺丝孔上方5mm
    WAIT IDLE(3)

    '模拟电批锁紧
    PRINT "模拟电批锁紧..."
    DELAY(2000)                     ' 模拟锁紧时间
    PRINT "电批锁紧完成"

    '抬升Z轴
    MOVEABS(target_z + 10) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", sys_status, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num

    PRINT "=== 双Y轴滑轨状态 ==="
    PRINT "左Y轴状态：", left_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "左Y轴位置：", DPOS(1), " mm"
    PRINT "右Y轴状态：", right_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "右Y轴位置：", DPOS(2), " mm"

    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "右侧队列：", right_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "电批状态：", screwdriver_busy, " (0-空闲,1-忙碌)"

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", axis_home(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT
END SUB
