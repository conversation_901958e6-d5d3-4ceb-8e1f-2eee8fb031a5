'=============================================================================
' 螺丝机测试程序V2
' 用于测试螺丝机V2程序的各项功能
' 注意：请先运行螺丝机程序V2.bas，再运行本测试程序
'=============================================================================

'测试主程序
PRINT "=== 螺丝机系统测试程序V2 ==="
PRINT "开始系统测试..."

CALL TestHardware()
CALL TestAxis()
CALL TestInputs()
CALL TestOutputs()
CALL TestModbus()
CALL TestMotion()

PRINT "系统测试完成！"
END

'================ 硬件连接测试 ================
GLOBAL SUB TestHardware()
    PRINT ""
    PRINT "【1/6】硬件连接测试"
    
    '测试控制器连接
    PRINT "控制器连接正常"
    
    '测试轴连接
    FOR i = 0 TO 3
        BASE(i)
        PRINT "轴", i, "状态：", HEX(AXISSTATUS(i))
        IF AXISSTATUS(i) AND 16 THEN
            PRINT "  警告：轴", i, "驱动器报警"
        ELSE
            PRINT "  ✓ 轴", i, "连接正常"
        ENDIF
    NEXT
    
    PRINT "硬件连接测试完成"
END SUB

'================ 轴参数测试 ================
GLOBAL SUB TestAxis()
    PRINT ""
    PRINT "【2/6】轴参数测试"
    
    FOR i = 0 TO 3
        BASE(i)
        PRINT "轴", i, "参数："
        PRINT "  ATYPE：", ATYPE(i)
        PRINT "  UNITS：", UNITS(i)
        PRINT "  SPEED：", SPEED(i)
        PRINT "  ACCEL：", ACCEL(i)
        
        '验证参数合理性
        IF ATYPE(i) <> 1 THEN
            PRINT "  警告：ATYPE应为1（脉冲轴）"
        ENDIF
        
        IF UNITS(i) <= 0 THEN
            PRINT "  错误：UNITS必须大于0"
        ELSE
            PRINT "  ✓ 参数设置正常"
        ENDIF
    NEXT
    
    PRINT "轴参数测试完成"
END SUB

'================ 输入信号测试 ================
GLOBAL SUB TestInputs()
    PRINT ""
    PRINT "【3/6】输入信号测试"
    PRINT "请在5秒内触发各个输入信号进行测试"
    
    DIM test_inputs(20)
    FOR i = 0 TO 19
        test_inputs(i) = 0
    NEXT
    
    '监控输入信号5秒
    FOR test_time = 1 TO 50
        FOR i = 0 TO 19
            IF IN(i) = ON AND test_inputs(i) = 0 THEN
                test_inputs(i) = 1
                PRINT "  ✓ 检测到IN", i, "信号"
            ENDIF
        NEXT
        DELAY(100)
    WEND
    
    '检查关键信号
    PRINT "关键信号测试结果："
    IF test_inputs(0) = 1 THEN
        PRINT "  ✓ IN0（左侧开始）信号正常"
    ELSE
        PRINT "  - IN0（左侧开始）信号未测试"
    ENDIF
    
    IF test_inputs(1) = 1 THEN
        PRINT "  ✓ IN1（右侧开始）信号正常"
    ELSE
        PRINT "  - IN1（右侧开始）信号未测试"
    ENDIF
    
    IF test_inputs(2) = 1 THEN
        PRINT "  ✓ IN2（系统回零）信号正常"
    ELSE
        PRINT "  - IN2（系统回零）信号未测试"
    ENDIF
    
    IF test_inputs(3) = 1 THEN
        PRINT "  ✓ IN3（急停）信号正常"
    ELSE
        PRINT "  - IN3（急停）信号未测试"
    ENDIF
    
    PRINT "输入信号测试完成"
END SUB

'================ 输出信号测试 ================
GLOBAL SUB TestOutputs()
    PRINT ""
    PRINT "【4/6】输出信号测试"
    
    '测试吸螺丝输出
    PRINT "测试OP0（吸螺丝）输出..."
    OP(0, ON)
    PRINT "  OP0输出ON，请检查吸螺丝动作"
    DELAY(2000)
    
    OP(0, OFF)
    PRINT "  OP0输出OFF，请检查吸螺丝停止"
    DELAY(1000)
    
    '测试其他输出
    FOR i = 1 TO 3
        PRINT "测试OP", i, "输出..."
        OP(i, ON)
        DELAY(500)
        OP(i, OFF)
        DELAY(500)
    NEXT
    
    PRINT "输出信号测试完成"
END SUB

'================ Modbus通讯测试 ================
GLOBAL SUB TestModbus()
    PRINT ""
    PRINT "【5/6】Modbus通讯测试"
    
    '测试串口配置
    PRINT "串口配置：115200, 8, 1, 0"
    
    '测试电批连接
    DIM result
    result = MODBUSM_DES(10, 500, 1)
    IF result = 0 THEN
        PRINT "  ✓ 电批连接成功，地址：10"
    ELSE
        PRINT "  ✗ 电批连接失败，错误码：", result
        PRINT "  请检查：1.RS485连接 2.电批地址 3.波特率设置"
    ENDIF
    
    '测试读取电批状态
    result = MODBUSM_REGGET(9728, 1, 100)
    IF result = 0 THEN
        PRINT "  ✓ 电批状态读取成功：", MODBUS_REG(100)
    ELSE
        PRINT "  ✗ 电批状态读取失败"
    ENDIF
    
    PRINT "Modbus通讯测试完成"
END SUB

'================ 运动功能测试 ================
GLOBAL SUB TestMotion()
    PRINT ""
    PRINT "【6/6】运动功能测试"
    
    '检查是否可以运动
    DIM can_move
    can_move = 1
    
    FOR i = 0 TO 3
        IF AXISSTATUS(i) <> 0 THEN
            PRINT "  警告：轴", i, "状态异常，无法测试运动"
            can_move = 0
        ENDIF
    NEXT
    
    IF can_move = 0 THEN
        PRINT "  建议先执行回零后再测试运动"
        PRINT "运动功能测试跳过"
        RETURN
    ENDIF
    
    '测试单轴运动
    PRINT "测试单轴运动..."
    FOR i = 0 TO 3
        BASE(i)
        PRINT "  测试轴", i, "运动..."
        
        DIM start_pos
        start_pos = DPOS(i)
        
        '小幅度运动测试
        MOVE(5)
        WAIT IDLE(i)
        DELAY(500)
        
        MOVE(-5)
        WAIT IDLE(i)
        DELAY(500)
        
        '检查是否回到原位
        IF ABS(DPOS(i) - start_pos) < 0.1 THEN
            PRINT "    ✓ 轴", i, "运动正常"
        ELSE
            PRINT "    ✗ 轴", i, "运动异常，位置偏差：", ABS(DPOS(i) - start_pos)
        ENDIF
    NEXT
    
    '测试两轴插补
    PRINT "测试XY插补运动..."
    BASE(0, 1)
    
    DIM start_x, start_y
    start_x = DPOS(0)
    start_y = DPOS(1)
    
    '小矩形轨迹测试
    MOVE(5, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(0, 5)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(-5, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(0, -5)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '检查是否回到原位
    IF ABS(DPOS(0) - start_x) < 0.1 AND ABS(DPOS(1) - start_y) < 0.1 THEN
        PRINT "  ✓ XY插补运动正常"
    ELSE
        PRINT "  ✗ XY插补运动异常"
    ENDIF
    
    PRINT "运动功能测试完成"
END SUB

'================ 生成测试报告 ================
GLOBAL SUB TestReport()
    PRINT ""
    PRINT "========================================"
    PRINT "           螺丝机系统测试报告V2"
    PRINT "========================================"
    PRINT "测试项目："
    PRINT "✓ 硬件连接测试"
    PRINT "✓ 轴参数测试"
    PRINT "✓ 输入信号测试"
    PRINT "✓ 输出信号测试"
    PRINT "✓ Modbus通讯测试"
    PRINT "✓ 运动功能测试"
    PRINT ""
    PRINT "系统状态："
    FOR i = 0 TO 3
        PRINT "轴", i, "状态：", HEX(AXISSTATUS(i))
    NEXT
    PRINT ""
    PRINT "建议："
    PRINT "1. 如有测试失败项目，请检查硬件连接"
    PRINT "2. 确保所有信号连接正确"
    PRINT "3. 执行系统回零后开始正式作业"
    PRINT "========================================"
END SUB
