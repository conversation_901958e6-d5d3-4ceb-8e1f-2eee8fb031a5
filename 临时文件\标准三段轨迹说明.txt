===============================================================================
                        标准三段轨迹说明 - 强制三段插补，Z轴不参与打螺丝
===============================================================================

【系统更新说明】

=== 需求变更 ===
根据最新需求，系统进行了以下重要更新：

1. **执行打螺丝时Z轴不用下降**
   - 三段插补完成后已经到达可以打螺丝的位置
   - 打螺丝时只需要控制电批，Z轴保持不动
   - 简化了打螺丝流程，提高了精度

2. **安全高度不能设置为0**
   - 系统强制使用标准三段轨迹
   - pick_safe_height和work_safe_height必须大于0
   - 确保运动安全性和轨迹一致性

【标准三段轨迹架构】

=== 唯一轨迹模式 ===
```
标准三段轨迹（强制模式）：
第一段：抬Z到起点安全高度
第二段：安全高度间真圆弧插补（MOVECIRC2ABS）
第三段：Z下降到目标工作位置

特点：
✅ 安全高度必须>0
✅ 轨迹完全一致
✅ 运动安全可靠
✅ 到达目标位置后直接打螺丝
```

=== 安全高度设置 ===
```basic
pick_safe_height = 8            ' 取螺丝位置安全高度（必须>0）
work_safe_height = 25           ' 工件位置安全高度（必须>0）
arc_top_height = 20             ' 圆弧最高点（固定值）

验证机制：
IF start_safe_z <= 0 OR end_safe_z <= 0 THEN
    PRINT "错误：安全高度必须大于0！"
    RETURN
ENDIF
```

【完整作业流程】

=== 单个螺丝的完整流程 ===
```
以左侧第一个螺丝(100,80,30)为例：

1. 确保在取螺丝位置
   - Y轴：当前位置 → 150mm
   - XZ：检查是否在(50,10)，不在则移动过去

2. 吸取螺丝
   - 在(50,150,10)位置吸取螺丝
   - OP(0, ON) 开启吸螺丝，等待1秒

3. 标准三段轨迹到螺丝孔位 ⭐
   - Y轴：150mm → 80mm
   - 第一段：(50,10) → (50,8) 抬Z到安全高度
   - 第二段：MOVECIRC2ABS(75,20,100,25) 真圆弧插补
   - 第三段：(100,25) → (100,30) Z下降到工作位置

4. 执行打螺丝（Z轴不动）⭐
   - Z轴保持在30mm工作位置
   - OP(1, ON) 开启电批
   - DELAY(2000) 锁紧2秒
   - OP(1, OFF) 关闭电批

5. 标准三段轨迹回取螺丝位置 ⭐
   - Y轴：80mm → 150mm
   - 第一段：(100,30) → (100,25) 抬Z到安全高度
   - 第二段：MOVECIRC2ABS(75,20,50,8) 真圆弧插补
   - 第三段：(50,8) → (50,10) Z下降到取螺丝位置

6. 关闭吸螺丝
   - OP(0, OFF) 关闭吸螺丝
```

【核心函数详解】

=== ThreeSegmentMove() - 标准三段轨迹核心函数 ===
```basic
功能：执行标准三段轨迹，安全高度必须大于0
参数：
- start_x, start_z：起点坐标
- end_x, end_z：终点坐标
- start_safe_z：起点安全高度（必须>0）
- end_safe_z：终点安全高度（必须>0）

验证：
- 检查安全高度是否大于0
- 如果不满足条件，输出错误并返回

执行：
- 调用StandardThreeSegment()执行标准三段轨迹
```

=== StandardThreeSegment() - 标准三段轨迹执行函数 ===
```basic
第一段：抬Z到起点安全高度
MOVEABS(start_x, start_safe_z)

第二段：安全高度间圆弧插补
IF distance >= 5 THEN
    MOVECIRC2ABS(mid_x, arc_top_height, end_x, end_safe_z)
ELSE
    MOVEABS(end_x, end_safe_z)
ENDIF

第三段：Z下降到目标位置
MOVEABS(end_x, end_z)
```

=== DoScrew() - 简化的打螺丝函数 ===
```basic
功能：三段插补完成后直接控制电批，Z轴不动
流程：
1. 确认Z轴已在正确位置
2. OP(1, ON) 开启电批
3. DELAY(2000) 锁紧2秒
4. OP(1, OFF) 关闭电批
5. Z轴保持在工作位置

优势：
- 简化流程，提高效率
- 减少Z轴运动，提高精度
- 避免重复定位误差
```

【轨迹精度优势】

=== 到达目标位置的精度 ===
```
传统方式：
三段插补 → 到达安全高度 → Z轴下降 → 打螺丝
问题：Z轴下降可能有定位误差

新方式：
三段插补 → 直接到达工作位置 → 打螺丝
优势：一次定位到位，精度更高
```

=== 轨迹一致性 ===
```
强制标准三段轨迹：
- 所有螺丝使用相同的轨迹类型
- 相同的安全高度设置
- 相同的圆弧参数
- 轨迹完全一致，质量稳定
```

【性能分析】

=== 运动时间优化 ===
```
传统打螺丝流程：
三段插补(0.20秒) + Z下降(0.05秒) + 电批(2.00秒) + Z抬升(0.05秒)
总时间：2.30秒

新打螺丝流程：
三段插补(0.20秒) + 电批(2.00秒)
总时间：2.20秒
节省：0.10秒/螺丝，16个螺丝节省1.6秒
```

=== 精度提升 ===
```
Z轴定位精度：
- 减少一次Z轴运动，减少累积误差
- 三段插补直接到位，精度更高
- 避免重复定位的不一致性

电批锁紧精度：
- Z轴位置固定，电批力度更稳定
- 减少Z轴振动对锁紧的影响
```

【参数设置指南】

=== 安全高度设置原则 ===
```basic
pick_safe_height设置：
- 最小值：2mm（确保能抬离取螺丝器）
- 推荐值：5-10mm（根据取螺丝器高度）
- 当前值：8mm

work_safe_height设置：
- 最小值：工件最高点+5mm
- 推荐值：工件最高点+10-15mm
- 当前值：25mm（适合30mm工作深度）
```

=== 圆弧最高点设置 ===
```basic
arc_top_height = 20             ' 当前设置

调整原则：
- 必须高于所有安全高度
- 当前设置20mm > max(8,25)mm ❌ 需要调整！
- 建议调整为30mm，确保高于work_safe_height
```

=== 工作深度设置 ===
```basic
screw_work_height = 30          ' 打螺丝工作深度

说明：
- 这是三段插补的最终目标位置
- 电批在此位置直接锁紧
- 无需额外的Z轴运动
```

【系统验证】

=== 安全高度验证 ===
```basic
系统启动时自动验证：
IF pick_safe_height <= 0 THEN
    PRINT "错误：取螺丝安全高度必须>0"
ENDIF

IF work_safe_height <= 0 THEN
    PRINT "错误：工件安全高度必须>0"
ENDIF

IF arc_top_height <= pick_safe_height OR arc_top_height <= work_safe_height THEN
    PRINT "警告：圆弧最高点应高于所有安全高度"
ENDIF
```

=== 运动轨迹验证 ===
```basic
测试要点：
1. 确认三段插补到达正确的工作位置
2. 验证电批在正确位置锁紧
3. 检查Z轴在打螺丝过程中不移动
4. 确认所有螺丝使用相同轨迹
```

【调试建议】

=== 参数优化建议 ===
```basic
建议调整：
arc_top_height = 30             ' 从20mm调整为30mm
pick_safe_height = 8            ' 保持8mm
work_safe_height = 25           ' 保持25mm

理由：
- 确保圆弧最高点高于所有安全高度
- 提供足够的安全间隙
- 保持轨迹的安全性
```

=== 测试步骤 ===
```basic
1. 低速测试：
   SPEED = 500, 1000, 1000, 250

2. 观察轨迹：
   - 检查三段轨迹是否平滑连续
   - 确认到达工作位置的精度
   - 验证电批锁紧时Z轴不动

3. 精度测试：
   - 测量最终位置精度
   - 检查16个螺丝的一致性
   - 验证电批锁紧效果
```

【总结】

标准三段轨迹系统特点：
✅ **强制三段轨迹**：安全高度必须>0，确保轨迹一致性
✅ **简化打螺丝**：Z轴不动，只控制电批，提高精度
✅ **一次定位**：三段插补直接到工作位置，减少误差
✅ **提高效率**：减少不必要的Z轴运动，节省时间
✅ **安全可靠**：强制安全高度，确保运动安全

这套标准三段轨迹系统为螺丝机提供了最高的安全性、一致性和精度，
是一个经过优化的、可靠的自动化螺丝锁紧解决方案。

===============================================================================
