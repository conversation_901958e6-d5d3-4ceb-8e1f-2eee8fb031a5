===============================================================================
                        修正CORNER_MODE解决90度角减速问题说明 - 精准定位问题根源
===============================================================================

【问题根源精准分析】

=== 核心问题：CORNER_MODE逻辑冲突 ===
```
🚨 问题根源（专家级分析）：
虽然设置了MERGE = ON，但CORNER_MODE = 32 + 2存在严重逻辑冲突：

CORNER_MODE = 32 + 2 = 34：
- 位32：自动倒角
- 位2：自动拐角减速

位2的自动拐角减速规则：
- DECEL_ANGLE = 15°（默认）：开始减速角度
- STOP_ANGLE = 45°（默认）：完全停止角度
- 大角度必须完全停下（速度降到LSPEED，LSPEED默认为0）

三段轨迹的角度分析：
- 抬Z → 圆弧：90°拐角
- 圆弧 → 下Z：90°拐角
- 90° > 45°（STOP_ANGLE）
- 控制器被迫降速到0，再重新加速
- 导致"衔接处速度变0"
```

=== SP指令的额外干扰 ===
```
使用MOVEABSSP/MOVECIRC2ABSSP的问题：
- 只要带SP后缀，就会启用SP速度逻辑
- 如果之前有人给ENDMOVE_SPEED/STARTMOVE_SPEED留过值
- 也会强行以它们为准，造成意外限速

RAM中的SP速度残留：
- 某次调试留在RAM里的SP速度
- 可能让拐角减速逻辑失效或意外限速
- 需要主动清除这些残留值
```

【解决方案】

=== 方案A：完全不停（最快，推荐） ===
```basic
目标：完全不停，最快速度
做法：只保留倒角，取消自动减速

CORNER_MODE = 32                ' 只倒角，不减速
ZSMOOTH = 5                     ' 倒角半径，根据机台刚性微调

原理：
- 位32只会在拐角插入自动倒角
- 不再按角度减速
- 速度由S曲线和插补自身平滑
- 90度拐角通过倒角平滑过渡，速度不降到0
```

=== 方案B：允许减速但不停死 ===
```basic
目标：拐角允许减速，但不能降到0
做法：调大减速角度上限

CORNER_MODE = 2 + 32            ' 需要减速又要倒角
DECEL_ANGLE = 30 * (PI/180)    ' 开始减速角度30度
STOP_ANGLE = 80 * (PI/180)     ' 最低速角度80度

原理：
- 90° > DECEL_ANGLE（30°），开始减速
- 90° < STOP_ANGLE（80°），不会停死
- 只按比例减速，保持一定速度
```

=== 核心修正措施 ===
```basic
1. 修正CORNER_MODE：
   CORNER_MODE = 32             ' 只倒角，不自动减速

2. 清除SP速度残留：
   RAPIDSTOP(2)                 ' 清除缓冲区
   WAIT IDLE                    ' 等待清除完成
   STARTMOVE_SPEED = 1000       ' 给足够大的值
   ENDMOVE_SPEED = 1000         ' 避免沿用上次设置

3. 使用普通插补指令：
   MOVEABS(...)                 ' 不用MOVEABSSP
   MOVECIRC2ABS(...)            ' 不用MOVECIRC2ABSSP

4. 设置合适的倒角半径：
   ZSMOOTH = 5                  ' 根据机台刚性调整
```

【技术细节深度解析】

=== CORNER_MODE各位的作用 ===
```basic
CORNER_MODE的位定义：
位1（值1）：按ACCEL、DECEL加减速度
位2（值2）：自动拐角减速
位5（值32）：自动倒角设置

常用组合：
CORNER_MODE = 0：手动控制，不自动处理
CORNER_MODE = 2：自动拐角减速，按角度减速
CORNER_MODE = 32：自动倒角，插入圆弧过渡
CORNER_MODE = 34：倒角+减速，容易冲突

问题分析：
CORNER_MODE = 34时：
- 系统既要插入倒角（位32）
- 又要按角度减速（位2）
- 90度角触发STOP_ANGLE规则
- 强制减速到0，破坏连续性
```

=== 自动拐角减速的角度规则 ===
```basic
默认角度阈值：
DECEL_ANGLE = 15°               ' 开始减速角度
STOP_ANGLE = 45°                ' 完全停止角度

减速规则：
- 角度 < DECEL_ANGLE：不减速
- DECEL_ANGLE ≤ 角度 < STOP_ANGLE：按比例减速
- 角度 ≥ STOP_ANGLE：完全停止（速度降到LSPEED=0）

三段轨迹的角度：
- 抬Z → 圆弧：90°
- 圆弧 → 下Z：90°
- 90° > 45°（STOP_ANGLE）
- 触发完全停止规则
- 速度被强制降到0
```

=== ZSMOOTH倒角半径的作用 ===
```basic
ZSMOOTH = 5的效果：
- 在拐角处插入半径5mm的圆弧
- 平滑过渡，避免尖锐转折
- 保持速度连续性
- 减少机械冲击

倒角半径选择：
ZSMOOTH = 3：较小倒角，精度高，平滑性一般
ZSMOOTH = 5：适中倒角，平衡精度和平滑性（推荐）
ZSMOOTH = 8：较大倒角，平滑性好，精度略降

根据机台刚性调整：
- 刚性好：可用较小倒角半径
- 刚性一般：建议用适中倒角半径
- 刚性差：需要较大倒角半径
```

=== SP指令的清除方法 ===
```basic
为什么要清除SP速度：
- SP速度设置会保留在RAM中
- 影响后续的插补规划
- 可能导致意外的速度限制
- 需要主动清除避免干扰

清除方法：
RAPIDSTOP(2)                    ' 清除插补缓冲区
WAIT IDLE                       ' 等待清除完成
STARTMOVE_SPEED = 1000          ' 设置足够大的值
ENDMOVE_SPEED = 1000            ' 避免沿用旧值

使用普通指令：
MOVEABS(...)                    ' 代替MOVEABSSP
MOVECIRC2ABS(...)               ' 代替MOVECIRC2ABSSP
```

【修正前后对比】

=== 修正前（90度角减速到0） ===
```basic
CORNER_MODE = 32 + 2            ' 倒角+自动减速，逻辑冲突
MOVEABSSP(start_x, start_safe_z)
MOVECIRC2ABSSP(...)             ' SP指令可能有残留速度
MOVEABSSP(end_x, end_z)

问题：
❌ 90度角 > 45度（STOP_ANGLE）
❌ 触发完全停止规则
❌ 速度被强制降到0
❌ SP指令可能有残留干扰
❌ 破坏连续插补效果
```

=== 修正后（90度角倒角平滑） ===
```basic
RAPIDSTOP(2)                    ' 清除缓冲区
WAIT IDLE                       ' 等待清除
CORNER_MODE = 32                ' 只倒角，不自动减速
ZSMOOTH = 5                     ' 倒角半径5mm
STARTMOVE_SPEED = 1000          ' 清除SP速度残留
ENDMOVE_SPEED = 1000

MOVEABS(start_x, start_safe_z)  ' 普通插补指令
MOVECIRC2ABS(...)               ' 普通插补指令
MOVEABS(end_x, end_z)

优势：
✅ 90度角通过倒角平滑过渡
✅ 速度不会降到0
✅ 清除SP速度干扰
✅ 真正实现连续插补
✅ 机械冲击最小
```

【预期效果】

=== 速度曲线特征 ===
```
修正后的连续速度曲线：
速度
 ^
 |      /~~~~~~~~\
 |     /          \
 |    /            \
 |   /              \
 |  /                \
 | /                  \
 |/                    \
 +-----|-----|-----|-----|---> 时间
      倒角   倒角   倒角
      平滑   平滑   平滑

特点：
✅ 90度拐角通过倒角平滑过渡
✅ 速度在拐角处略有下降但不为0
✅ 整体保持连续性
✅ 无中间停车现象
✅ 机械冲击最小
```

=== 运动质量提升 ===
```
连续性：
✅ 三段轨迹真正连续
✅ 90度拐角不再停车
✅ 倒角提供平滑过渡

平滑性：
✅ SS曲线+倒角双重平滑
✅ 机械冲击显著减少
✅ 振动和噪音降低

效率：
✅ 无中间停车，时间最短
✅ 速度保持连续，效率最高
✅ 适合高频作业场合
```

【验证方法】

=== 示波器验证（推荐） ===
```
使用示波器观察MSPEED(0)和MSPEED(3)：
1. 调整刻度，清晰显示速度变化
2. 确认拐角处速度不降到0
3. 检查整体曲线的连续性
4. 对比修正前后的差异

预期结果：
✅ 拐角处速度保持在一定值以上
✅ 无速度突降到0的现象
✅ 整体曲线平滑连续
✅ 比程序打印更直观准确
```

=== 机械观察验证 ===
```
听觉验证：
- 运动声音应该连续平滑
- 无明显的停顿或冲击声
- 整体噪音水平降低

视觉验证：
- 观察运动轨迹的连续性
- 检查是否有明显的停顿
- 验证倒角效果是否明显

精度验证：
- 测试最终定位精度
- 检查重复性
- 验证长期稳定性
```

【参数调整建议】

=== 如需更高速度 ===
```basic
FORCE_SPEED = 120               ' 提高目标速度
ZSMOOTH = 3                     ' 减小倒角半径
SRAMP = 80, 80                  ' 适当减少S曲线时间
```

=== 如需更高精度 ===
```basic
FORCE_SPEED = 60                ' 降低目标速度
ZSMOOTH = 8                     ' 增大倒角半径
SRAMP = 120, 120                ' 增加S曲线时间
```

=== 如需允许适度减速 ===
```basic
CORNER_MODE = 2 + 32            ' 倒角+减速
DECEL_ANGLE = 30 * (PI/180)    ' 30度开始减速
STOP_ANGLE = 80 * (PI/180)     ' 80度才停止
FORCE_SPEED = 80                ' 参考速度
```

【故障排除】

=== 如果仍有停车现象 ===
```
1. 检查CORNER_MODE是否正确设置为32
2. 确认RAPIDSTOP(2)是否执行
3. 验证SP速度是否清除
4. 检查ZSMOOTH半径是否合适
5. 确认使用的是MOVEABS而不是MOVEABSSP
```

=== 如果倒角效果不理想 ===
```
1. 调整ZSMOOTH半径大小
2. 检查机台刚性是否足够
3. 验证轴参数设置是否合理
4. 考虑调整SRAMP时间
5. 检查ACCEL和DECEL设置
```

【总结】

修正CORNER_MODE解决90度角减速问题的关键：
✅ **精准定位问题**：CORNER_MODE=34的逻辑冲突，90度>45度触发停车
✅ **正确设置**：CORNER_MODE=32只倒角不减速，避免角度触发停车
✅ **清除干扰**：RAPIDSTOP+清除SP速度残留，避免意外限速
✅ **使用普通指令**：MOVEABS代替MOVEABSSP，避免SP逻辑干扰
✅ **合适倒角半径**：ZSMOOTH=5提供平滑过渡，保持连续性
✅ **示波器验证**：直观确认速度不降到0，比程序打印更准确

这个修正彻底解决了三段连续插补中90度拐角被强制减速到0的问题，
实现了真正的连续插补，为螺丝机提供了高效、平滑的运动性能。

===============================================================================
