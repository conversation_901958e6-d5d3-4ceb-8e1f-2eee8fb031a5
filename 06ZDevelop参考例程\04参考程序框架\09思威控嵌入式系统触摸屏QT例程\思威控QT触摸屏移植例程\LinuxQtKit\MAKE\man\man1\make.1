.TH MAKE 1 "22 August 1989" "GNU" "LOCAL USER COMMANDS"
.SH NAME
make \- GNU make utility to maintain groups of programs
.SH SYNOPSIS
.B "make "
[
.B \-f
.I makefile
] [ options ] ... [ targets ] ...
.SH WARNING
This man page is an extract of the documentation of GNU
.IR make .
It is updated only occasionally, because the GNU project does not use nroff.
For complete, current documentation, refer to the Info file
.B make.info
which is made from the Texinfo source file
.BR make.texi .
.SH DESCRIPTION
.LP
The purpose of the
.I make
utility is to determine automatically which
pieces of a large program need to be recompiled, and issue the commands to
recompile them.
The manual describes the GNU implementation of
.IR make ,
which was written by <PERSON> and <PERSON>, and is
currently maintained by <PERSON>.
Our examples show C programs, since they are most common, but you can use
.I make
with any programming language whose compiler can be run with a
shell command.
In fact,
.I make
is not limited to programs.
You can use it to describe any task where some files must be
updated automatically from others whenever the others change.
.LP
To prepare to use
.IR make ,
you must write a file called the
.I makefile
that describes the relationships among files in your program, and the
states the commands for updating each file.
In a program, typically the executable file is updated from object
files, which are in turn made by compiling source files.
.LP
Once a suitable makefile exists, each time you change some source files,
this simple shell command:
.sp 1
.RS
.B make
.RE
.sp 1
suffices to perform all necessary recompilations.
The
.I make
program uses the makefile data base and the last-modification times
of the files to decide which of the files need to be updated.
For each of those files, it issues the commands recorded in the data base.
.LP
.I make
executes commands in the
.I makefile
to update
one or more target
.IR names ,
where
.I name
is typically a program.
If no
.B \-f
option is present,
.I make
will look for the makefiles
.IR GNUmakefile ,
.IR makefile ,
and
.IR Makefile ,
in that order.
.LP
Normally you should call your makefile either
.I makefile
or
.IR Makefile .
(We recommend
.I Makefile
because it appears prominently near the beginning of a directory
listing, right near other important files such as
.IR  README .)
The first name checked,
.IR GNUmakefile ,
is not recommended for most makefiles.
You should use this name if you have a makefile that is specific to GNU
.IR make ,
and will not be understood by other versions of
.IR make .
If
.I makefile
is `\-', the standard input is read.
.LP
.I make
updates a target if it depends on prerequisite files
that have been modified since the target was last modified,
or if the target does not exist.
.SH OPTIONS
.sp 1
.TP 0.5i
.BR \-b , " \-m"
These options are ignored for compatibility with other versions of
.IR make .
.TP 0.5i
.BR \-B , " \-\-always\-make"
Unconditionally make all targets.
.TP 0.5i
\fB\-C\fR \fIdir\fR, \fB\-\-directory\fR=\fIdir\fR
Change to directory
.I dir
before reading the makefiles or doing anything else.
If multiple
.B \-C
options are specified, each is interpreted relative to the
previous one:
.BR "\-C " /
.BR "\-C " etc
is equivalent to
.BR "\-C " /etc.
This is typically used with recursive invocations of
.IR make .
.TP 0.5i
.B \-d
Print debugging information in addition to normal processing.
The debugging information says which files are being considered for
remaking, which file-times are being compared and with what results,
which files actually need to be remade, which implicit rules are
considered and which are applied---everything interesting about how
.I make
decides what to do.
.TP 0.5i
.BI \-\-debug "[=FLAGS]"
Print debugging information in addition to normal processing.
If the
.I FLAGS
are omitted, then the behavior is the same as if
.B \-d
was specified.
.I FLAGS
may be
.I a
for all debugging output (same as using
.BR \-d ),
.I b
for basic debugging,
.I v
for more verbose basic debugging,
.I i
for showing implicit rules,
.I j
for details on invocation of commands, and
.I m
for debugging while remaking makefiles.
.TP 0.5i
.BR \-e , " \-\-environment\-overrides"
Give variables taken from the environment precedence
over variables from makefiles.
.TP 0.5i
+\fB\-f\fR \fIfile\fR, \fB\-\-file\fR=\fIfile\fR, \fB\-\-makefile\fR=\fIFILE\fR
Use
.I file
as a makefile.
.TP 0.5i
.BR \-i , " \-\-ignore\-errors"
Ignore all errors in commands executed to remake files.
.TP 0.5i
\fB\-I\fR \fIdir\fR, \fB\-\-include\-dir\fR=\fIdir\fR
Specifies a directory
.I dir
to search for included makefiles.
If several
.B \-I
options are used to specify several directories, the directories are
searched in the order specified.
Unlike the arguments to other flags of
.IR make ,
directories given with
.B \-I
flags may come directly after the flag:
.BI \-I dir
is allowed, as well as
.BI "\-I " dir.
This syntax is allowed for compatibility with the C
preprocessor's
.B \-I
flag.
.TP 0.5i
\fB\-j\fR [\fIjobs\fR], \fB\-\-jobs\fR[=\fIjobs\fR]
Specifies the number of
.I jobs
(commands) to run simultaneously.
If there is more than one
.B \-j
option, the last one is effective.
If the
.B \-j
option is given without an argument,
.IR make
will not limit the number of jobs that can run simultaneously.
.TP 0.5i
.BR \-k , " \-\-keep\-going"
Continue as much as possible after an error.
While the target that failed, and those that depend on it, cannot
be remade, the other dependencies of these targets can be processed
all the same.
.TP 0.5i
\fB\-l\fR [\fIload\fR], \fB\-\-load\-average\fR[=\fIload\fR]
Specifies that no new jobs (commands) should be started if there are
others jobs running and the load average is at least
.I load
(a floating-point number).
With no argument, removes a previous load limit.
.TP 0.5i
.BR \-L , " \-\-check\-symlink\-times"
Use the latest mtime between symlinks and target.
.TP 0.5i
.BR \-n , " \-\-just\-print" , " \-\-dry\-run" , " \-\-recon"
Print the commands that would be executed, but do not execute them.
.TP 0.5i
\fB\-o\fR \fIfile\fR, \fB\-\-old\-file\fR=\fIfile\fR, \fB\-\-assume\-old\fR=\fIfile\fR
Do not remake the file
.I file
even if it is older than its dependencies, and do not remake anything
on account of changes in
.IR file .
Essentially the file is treated as very old and its rules are ignored.
.TP 0.5i
.BR \-p , " \-\-print\-data\-base"
Print the data base (rules and variable values) that results from
reading the makefiles; then execute as usual or as otherwise
specified.
This also prints the version information given by the
.B \-v
switch (see below).
To print the data base without trying to remake any files, use
.B make
.B \-p
.BI \-f /dev/null.
.TP 0.5i
.BR \-q , " \-\-question"
``Question mode''.
Do not run any commands, or print anything; just return an exit status
that is zero if the specified targets are already up to date, nonzero
otherwise.
.TP 0.5i
.BR \-r , " \-\-no\-builtin\-rules"
Eliminate use of the built\-in implicit rules.
Also clear out the default list of suffixes for suffix rules.
.TP 0.5i
.BR \-R , " \-\-no\-builtin\-variables"
Don't define any built\-in variables.
.TP 0.5i
.BR \-s , " \-\-silent" , " \-\-quiet"
Silent operation; do not print the commands as they are executed.
.TP 0.5i
.BR \-S , " \-\-no\-keep\-going" , " \-\-stop"
Cancel the effect of the
.B \-k
option.
This is never necessary except in a recursive
.I make
where
.B \-k
might be inherited from the top-level
.I make
via MAKEFLAGS or if you set
.B \-k
in MAKEFLAGS in your environment.
.TP 0.5i
.BR \-t , " \-\-touch"
Touch files (mark them up to date without really changing them)
instead of running their commands.
This is used to pretend that the commands were done, in order to fool
future invocations of
.IR make .
.TP 0.5i
.BR \-v , " \-\-version"
Print the version of the
.I make
program plus a copyright, a list of authors and a notice that there
is no warranty.
.TP 0.5i
.BR \-w , " \-\-print\-directory"
Print a message containing the working directory
before and after other processing.
This may be useful for tracking down errors from complicated nests of
recursive
.I make
commands.
.TP 0.5i
.B \-\-no\-print\-directory
Turn off
.BR \-w ,
even if it was turned on implicitly.
.TP 0.5i
\fB\-W\fR \fIfile\fR, \fB\-\-what\-if\fR=\fIfile\fR, \fB\-\-new\-file\fR=\fIfile\fR, \fB\-\-assume\-new\fR=\fIfile\fR
Pretend that the target
.I file
has just been modified.
When used with the
.B \-n
flag, this shows you what would happen if you were to modify that file.
Without
.BR \-n ,
it is almost the same as running a
.I touch
command on the given file before running
.IR make ,
except that the modification time is changed only in the imagination of
.IR make .
.TP 0.5i
.B \-\-warn\-undefined\-variables
Warn when an undefined variable is referenced.
.SH "EXIT STATUS"
GNU
.I make
exits with a status of zero if all makefiles were successfully parsed
and no targets that were built failed.  A status of one will be returned
if the
.B \-q
flag was used and
.I make
determines that a target needs to be rebuilt.  A status of two will be
returned if any errors were encountered.
.SH "SEE ALSO"
.I "The GNU Make Manual"
.SH BUGS
See the chapter `Problems and Bugs' in
.IR "The GNU Make Manual" .
.SH AUTHOR
This manual page contributed by Dennis Morse of Stanford University.
It has been reworked by Roland McGrath.  Further updates contributed by
Mike Frysinger.
.SH "COPYRIGHT"
Copyright (C) 1992, 1993, 1996, 1999 Free Software Foundation, Inc.
This file is part of GNU
.IR make .
.LP
GNU
.I make
is free software; you can redistribute it and/or modify it under the
terms of the GNU General Public License as published by the Free
Software Foundation; either version 2, or (at your option) any later
version.
.LP
GNU
.I make
is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or
FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General Public License
for more details.
.LP
You should have received a copy of the GNU General Public License
along with GNU
.IR make ;
see the file COPYING.  If not, write to the Free Software Foundation,
Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301, USA.
