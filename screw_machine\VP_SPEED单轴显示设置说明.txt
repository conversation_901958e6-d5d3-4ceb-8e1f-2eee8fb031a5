===============================================================================
                        VP_SPEED单轴显示设置说明 - 基于官方手册
===============================================================================

【VP_SPEED工作机制】

=== 官方手册说明 ===
```
VP_SPEED -- 当前运动速度
类型：轴状态
描述：返回轴当前运动的速度，单位为 units/s

默认行为：
- 当多轴运动时，主轴返回的是插补运动的速度，不是主轴的分速度
- 非主轴返回的是相应的分速度，与 MSPEED 效果一致
- VP_SPEED 在默认情况下是为显示多轴合成速度设计的，是没有负值的

单轴显示设置：
- 除非把 SYSTEM_ZSET 的 bit0 的值设置为 0
- 就可以用来显示单轴的命令速度，可正可负
```

=== 关键设置代码 ===
```basic
'设置VP_SPEED显示单轴速度（关键）
SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)   ' 清除bit0

'或者使用位操作
SYSTEM_ZSET = SYSTEM_ZSET AND &HFFFFFFFE  ' 清除bit0

'验证设置
PRINT "SYSTEM_ZSET=", SYSTEM_ZSET       ' 应该是偶数（bit0=0）
```

【官方例程分析】

=== 官方手册示例 ===
```basic
BASE(0,1)
DPOS=0 ,0                               '坐标清 0
UNITS=100,100                           '脉冲当量
SPEED =100,100                          '主轴速度 100units/s
ACCEL=1000,1000                         '加速度 1000units/s/s
DECEL=1000,1000                         '减速度 1000units/s/s
TRIGGER                                 '自动触发示波器
MOVE(100,100)                           '两轴各运动 100units

速度曲线说明：
- 主轴 VP_SPEED 是插补合成运动的速度
- 非主轴 VP_SPEED 是轴当前分速度，与 MSPEED 一致
- VP_SPEED(0)垂直刻度 100，无偏移
- VP_SPEED(1)垂直刻度 100，无偏移
- MSPEED(0)垂直刻度 100，偏移-20
- MSPEED(1)垂直刻度 100，偏移-40
```

=== 设置前后对比 ===
```
设置前（SYSTEM_ZSET bit0=1，默认）：
BASE(0,1) 时：
VP_SPEED(0) = 插补合成速度 = √(X轴速度² + Y轴速度²)
VP_SPEED(1) = Y轴分速度

设置后（SYSTEM_ZSET bit0=0）：
BASE(0,1) 时：
VP_SPEED(0) = X轴单轴命令速度（可正可负）
VP_SPEED(1) = Y轴单轴命令速度（可正可负）
```

【程序修正要点】

=== 关键修正 ===
```
1. SYSTEM_ZSET设置位置：
   ✅ 必须在BASE()之前设置
   ✅ 必须在任何MOVE指令之前设置
   ✅ 建议在程序初始化阶段设置

2. 输入检测方式：
   ❌ 之前：复杂的状态记录和比较
   ✅ 现在：使用官方推荐的IN_SCAN()和IN_EVENT()

3. 程序结构：
   ❌ 之前：复杂的SUB函数嵌套
   ✅ 现在：线性程序结构，参考官方例程

4. 等待方式：
   ❌ 之前：WAIT IDLE(0) + WAIT IDLE(1)
   ✅ 现在：WAIT IDLE（等待所有轴）
```

=== 输入检测优化 ===
```basic
'官方推荐的输入检测方式
WHILE 1
    IF IN_SCAN(0, 7) THEN               ' 只有IO有变动才进循环
        FOR i = 0 TO 7
            IF IN_EVENT(i) > 0 THEN     ' 检测上升沿
                '执行相应操作
            ENDIF
        NEXT i
    ENDIF
    DELAY 100
WEND

优势：
✅ 效率高：只在IO变动时才检测
✅ 可靠性好：使用官方函数，稳定性高
✅ 代码简洁：无需手动记录状态
```

【示波器监控设置】

=== 推荐监控配置 ===
```
设置SYSTEM_ZSET bit0=0后：

主要监控信号：
VP_SPEED(0) - X轴单轴速度（可正可负）
VP_SPEED(1) - Y轴单轴速度（可正可负）

辅助验证信号：
MSPEED(0) - X轴分速度（应与VP_SPEED(0)基本一致）
MSPEED(1) - Y轴分速度（应与VP_SPEED(1)基本一致）

显示设置：
VP_SPEED(0)：垂直刻度100，偏移0
VP_SPEED(1)：垂直刻度100，偏移-60
MSPEED(0)：垂直刻度100，偏移-120
MSPEED(1)：垂直刻度100，偏移-180
```

=== 观察要点 ===
```
直线插补（45度）：
✅ VP_SPEED(0)和VP_SPEED(1)应该相等
✅ 两轴同时启动，同时停止
✅ 速度曲线对称，无突变

圆弧插补：
✅ VP_SPEED(0)和VP_SPEED(1)按圆弧轨迹变化
✅ 速度曲线平滑，无尖峰
✅ 可以显示正负值，反映方向变化

连续插补：
✅ 段间无速度断点
✅ 拐角处速度平滑过渡
✅ 整体曲线连续
```

【故障排除】

=== 常见问题 ===
```
问题1：VP_SPEED仍显示合成速度
原因：SYSTEM_ZSET设置无效或时机错误
解决：确保在BASE()之前设置，检查bit0是否为0

问题2：输入电平无响应
原因：IN_SCAN()或IN_EVENT()使用错误
解决：参考官方例程，确认输入信号正确

问题3：程序执行一次就停止
原因：缺少WHILE循环或END语句
解决：确保有WHILE 1...WEND结构

问题4：示波器无信号
原因：TRIGGER未执行或信号连接错误
解决：确认TRIGGER调用，检查示波器连接
```

=== 调试方法 ===
```
1. 验证SYSTEM_ZSET设置：
   PRINT "SYSTEM_ZSET=", SYSTEM_ZSET
   '应该是偶数（bit0=0）

2. 测试输入检测：
   手动给输入引脚施加信号，观察程序响应

3. 验证VP_SPEED显示：
   执行简单运动，观察VP_SPEED(0)和VP_SPEED(1)

4. 对比MSPEED：
   VP_SPEED和MSPEED应该基本一致
```

【程序使用建议】

=== 推荐测试流程 ===
```
1. 先使用"简单输入测试.bas"：
   - 验证输入检测功能
   - 确认VP_SPEED显示正确
   - 测试基本插补功能

2. 再使用"两轴插补测试正确版.bas"：
   - 执行完整的插补测试
   - 对比不同插补模式效果
   - 分析速度曲线特性

3. 参数调整和优化：
   - 根据测试结果调整参数
   - 优化插补质量
   - 记录最佳参数组合
```

=== 硬件连接确认 ===
```
输入信号要求：
- 电压：24V DC（或5V，根据控制器规格）
- 信号类型：数字开关信号
- 触发方式：上升沿（0V→24V）
- 信号宽度：≥100ms

连接方式：
IN0+ ← 24V信号
IN0- ← 0V (GND)
IN1+ ← 24V信号
IN1- ← 0V (GND)
...以此类推
```

【总结】

VP_SPEED单轴显示的关键要点：
✅ **正确设置SYSTEM_ZSET**：bit0=0，在BASE()之前设置
✅ **使用官方推荐的输入检测**：IN_SCAN()和IN_EVENT()
✅ **简化程序结构**：参考官方例程，避免复杂嵌套
✅ **合理的示波器监控**：VP_SPEED+MSPEED对比验证
✅ **完整的测试流程**：从简单到复杂，逐步验证

通过正确设置SYSTEM_ZSET和使用官方推荐的编程方式，
VP_SPEED现在可以准确显示各轴的单轴速度，
为插补运动分析提供更直观的监控手段。

===============================================================================
