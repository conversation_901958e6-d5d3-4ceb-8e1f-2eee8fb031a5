智能电批一体机通讯说明
1.描述：该说明为第三代闭环步进一体机(智能电批)通讯说明。电机接口为电批专用控制形式，通讯口为基于RS485的MODBUS_RTU形式。
2.RS485通讯设置
波特率一般默认为115200，不同版本的程序设置会有所不同；数据位8位；停止位1位；无奇偶校验位。
3.支持的协议命令
遵循MODBUS_RTU协议，支持功能码03/06/16；
同时兼容简洁的自定义短字节协议；
默认设备地址为10地址范围为1-127）。
4.注意事项
1)与有些人机界面配套使用时，人机界面的寄存器地址=电机的寄存器地址+1；
2)本设备存储介质为FLASH，不可持续对参数进行“写”操作，写入间隔>200ms；
3)不同功能的电机可通过上位机升级程序，进行功能的转换，首次转换程序功能或者进行版本升级后，需点击命令行的“恢复出厂值”按钮进行参数恢复，再点击“一键查询”按钮，进行恢复后的参数查看。
5.电机寄存器地址概述：（表内数据和地址均为十进制表示）
序号	名称	地址范围	定义
1	参数	8192-9215	存于FLASH中，可保存，不可频繁写入
2	位命令	9216-9471	位命令，虚拟地址，可频繁写入
3	命令	9472-9727	命令，虚拟地址，可频繁写入
4	状态	9728-9983	状态，只读

6.电机寄存器地址具体定义如下：（表内数据和地址均为十进制表示）
通用参数(0-15)：
序号	地址	名称	数据范围	描述
0	8192	电机方向	0-1	改变电机运行方向
2	8194	负载系数	10-200	根据实际负载调整
3	8195	每圈脉冲数	0-51200	每走一圈需要的脉冲数
25	8217	堵转时间	0-10000	单位为ms，堵转判定时间,=0不判断堵转
66	8258	加速度	0-30000	每100ms增加的速度
67	8259	减速斜率	0-10000	数值越大，减速越快
68	8480	入牙斜率	0-1000	数值越大，加速越快

内置位置(128-159)：
序号	地址	名称	数据范围	描述
128	8320	锁紧行程0	-32768-32767	行程0的脉冲数
160	8352	锁紧属性0	0x2800	相对位置值，堵转后跳入下一行程
192	8384	锁紧速度0	0-8000	设定速度
224	8416	锁紧力度0	0-1000	设定力度
				
129	8321	锁紧行程1	-32768-32767	行程1的脉冲数
161	8353	锁紧属性1	0x2800	相对位置值，堵转后跳入下一行程
193	8385	锁紧速度1	0-8000	设定速度
225	8417	锁紧力度1	0-1000	设定力度
				
130	8322	锁紧行程2	-32768-32767	行程2的脉冲数
162	8354	锁紧属性2	0x2800	相对位置值，堵转后跳入下一行程
194	8386	锁紧速度2	0-8000	设定速度
226	8418	锁紧力度2	0-1000	设定力度
				
131	8323	锁紧时间	-32768-32767	行程3的延时时间，单位ms
163	8355	延时属性	0x4a80	延时完成后释放电机
195	8387	延时速度	0-8000	设定速度
227	8419	延时力度	0-1000	设定力度
				
132	8324	附加行程	-32768-32767	行程4的脉冲数
164	8356	附加属性	0x2800	相对位置值，堵转后跳入下一行程
196	8388	附加速度	0-8000	设定速度
228	8420	附加力度	0-1000	设定力度
				
133	8325	松开行程	-32768-32767	行程5的脉冲数
165	8357	松开属性	0x2800	相对位置值，堵转后跳入下一行程
197	8389	松开速度	0-8000	设定速度
229	8421	松开力度	0-1000	设定力度
位命令表和命令表内的地址均为虚拟地址，可高频写入，断电不保存。
位命令表如下：
序号	地址	名称	位数	描述
0	9216	动作命令	位0	找零	0
			位1	JOG+，由上位机发送0x00进行清除	2
			位2	JOG-，由上位机发送0x00进行清除	4
			位3	正转, 由上位机发送0x00进行清除	8
			位4	反转, 由上位机发送0x00进行清除	16
			位5	扭力清零	32
			位6	当前位置值清零	64
			位7	当前位置值回归单圈值范围内	128
			位8	当前位置值保存为零点	256
			位9	当前位置值保存为当前位置号的值	512
			位10	消磁(释放电机)	1024
			位11	使能(取消消磁,电机不回消磁前的位置)	2048
			位12	系统重启	4096
			位13	电机重启，清除故障	8192
			位14	恢复出厂值	16384
			位15	急停	32768
6	9222	组合命令	位0	执行组合0，螺丝锁紧写常数1   (写16为任务一.32为任务二.32为任务三）
			位1	执行组合1，螺丝锁紧附加行程
			位2	执行组合2，螺丝松开         写常数4
			位3	执行组合3，运行停止



状态表如下：(只读)
序号	地址	名称	数据范围	描述
0	9728	状态标志	0-3	当前位置指令是否完成
0：未完成，
1：完成
2：堵转(为2时刹车信号即扭力到达)
3：错误
1	9729	当前位置	-32768-32767	当前位置, 单位为脉冲数
2	9730	当前速度	0-65535	即时速度值（转/分） 
3	9731	编码器单圈值	0-16383	编码器的单圈值
4	9732	编码器圈数	-32768-32767	找零后的编码器积累圈数
5	9733	目标位置	-32768-32767	目标位置值，参考4108设置
6	9734	动作完成时间	0-65535	单位为ms
7	9735	当前位置号	0-31	当前所在位置的编号
8	9736	跟随误差	-32768-32767	目标位置跟当前位置的差值
9	9737	准备好标志	0-1	0：有消磁、急停等异常状态
1：无消磁、急停等异常状态
10	9738	数据完整性检查	0-1	1：完整，0：不完整，返厂维修
11	9739	找零标志	0-3	0：未找零
1：找零中
2：找零完成
3：找零失败
12	9740	位置环模式		内部参数
13				
14	9742	速度环模式		内部参数
15	9743	完成标志	0-1	0：未完成，1：完成
16	9744	堵转标志	0-1	0：未堵转，1：堵转
17	9745	故障标志	0-1	0：未故障，1：故障
18	9746	脉冲方式	0-3	0：关闭
1：脉冲+方向输入
2：双脉冲输入
3：正交编码输入
19	9747	相位校验	0-65535	内部参数
20	9748	编码值校验	0-65535	内部参数
新增。	9755 	9756		外部正反转线控制状态。

IO功能描述
1表示有效电平，0表示无效电平，根据接口不同，单端信号接口为低电平有效，差分信号接口共阳接法为低电平有效，共阴接法为高电平有效：
IN1电平0->1：螺丝锁紧；
IN1电平1->0：螺丝锁紧附加动作；
IN2电平0->1：螺丝松开

OUT0功能：输出完成信号，有效时表示处于完成状态；
备注：直接使用本体电批时，不具备扭力反馈功能，具体的扭力，速度，圈数设定时为数据范围值内设定.数值越大效果越强。如需要做到对标实测数值，使用者可以根据自配屏或上维软件的比例换算进行调整。例如：把每圈脉冲数8195设100，给锁紧行程8320写10000就是电批运转100圈。
