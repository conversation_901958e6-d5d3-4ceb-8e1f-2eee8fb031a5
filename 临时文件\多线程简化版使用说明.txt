===============================================================================
                螺丝机多线程简化版 - 解决函数名冲突的最终方案
===============================================================================

【问题解决】
❌ 函数名冲突：多任务间全局函数名重复定义
✅ 简化架构：所有共享函数放在主控制任务中，子任务仅包含核心逻辑

【简化后的多任务架构】
任务0：主控制任务（螺丝机多线程简化版.bas）
- 持续扫描输入信号（50ms周期）
- 队列管理和状态控制
- 双Y轴滑轨控制
- 任务调度和状态监控
- 所有共享函数（避免函数名冲突）⭐ 关键优化

任务1：简化回零任务（简化任务1_回零.bas）
- 仅调用主控制任务中的SimpleHomeTask()
- 极简代码，无函数冲突

任务2：简化打螺丝任务（简化任务2_打螺丝.bas）
- 仅调用主控制任务中的SimpleScrewTask()
- 极简代码，无函数冲突

【文件列表】
1. 螺丝机多线程简化版.bas      - 主控制任务（任务0）⭐ 包含所有函数
2. 简化任务1_回零.bas          - 回零任务（仅3行代码）
3. 简化任务2_打螺丝.bas        - 打螺丝任务（仅3行代码）
4. 多线程简化版使用说明.txt    - 本说明文件

【启动方法】
1. 确保所有任务文件都已下载到控制器
2. 运行主控制任务：
   STOP
   RUN "螺丝机多线程简化版.bas"

【架构优势】

=== 解决函数名冲突 ===
✅ 所有GLOBAL SUB函数都在主控制任务中定义
✅ 子任务文件极简，仅包含任务调用
✅ 彻底避免"Label is already defined as sub"错误
✅ 多任务间共享函数无冲突

=== 代码结构清晰 ===
```
主控制任务（663行）：
- 系统初始化和配置
- 输入扫描和状态管理
- 所有共享函数
- 任务调度逻辑

子任务文件（3行）：
- 仅包含任务调用
- 无函数定义
- 无冲突风险
```

=== 维护简单 ===
✅ 所有逻辑集中在主控制任务
✅ 修改功能只需编辑一个文件
✅ 子任务文件无需修改
✅ 调试更加方便

【工作原理】

=== 任务启动流程 ===
1. 主控制任务启动，包含所有函数定义
2. 自动启动打螺丝任务：RUNTASK 2, SimpleScrewTask
3. 按需启动回零任务：RUNTASK 1, SimpleHomeTask
4. 子任务调用主控制任务中的函数

=== 函数调用关系 ===
```
主控制任务定义：
- SimpleHomeTask()      → 回零逻辑
- SimpleScrewTask()     → 打螺丝监控逻辑
- ExecuteLeftScrew()    → 左侧打螺丝执行
- ExecuteRightScrew()   → 右侧打螺丝执行
- WorkScrew()           → 统一打螺丝流程
- 所有运动控制函数

子任务调用：
- 任务1：CALL SimpleHomeTask()
- 任务2：CALL SimpleScrewTask()
```

【典型使用场景】

场景1：系统启动
1. 运行主控制任务 → 所有函数加载到内存
2. 自动启动打螺丝任务 → 开始监控队列
3. 主控制任务开始扫描输入 → 等待用户操作

场景2：按键响应
1. 用户按下IN0 → 主控制任务立即响应
2. 左Y轴移动到工作位置 → 左侧任务加入队列
3. 打螺丝任务检测到队列 → 开始执行左侧作业
4. 主控制任务继续扫描 → 可响应其他按键

场景3：回零操作
1. 用户按下IN2 → 主控制任务立即响应
2. 启动回零任务 → RUNTASK 1, SimpleHomeTask
3. 回零任务独立运行 → 不阻塞主控制任务
4. 回零完成后任务自动结束

【输入信号响应】
所有输入信号都由主控制任务处理：
- IN0：左侧开始（随时响应）
- IN1：右侧开始（随时响应）
- IN2：系统回零（随时响应）
- IN3：急停（随时响应）
- IN4：手动左Y轴到用户侧
- IN5：手动右Y轴到用户侧

【调试功能】
查看系统状态：
CALL ShowStatus()

显示内容包括：
- 系统状态和当前螺丝
- 双Y轴滑轨状态
- 队列状态和电批状态
- 任务运行状态
- 轴回零状态

手动控制：
CALL LeftSlideToWork()    ' 左Y轴到工作位置
CALL LeftSlideToUser()    ' 左Y轴到用户位置
CALL RightSlideToWork()   ' 右Y轴到工作位置
CALL RightSlideToUser()   ' 右Y轴到用户位置

【安全特性】

=== 急停处理 ===
1. 硬件急停：RAPIDSTOP(2)
2. 软件停止：screw_task_stop = 1
3. 任务停止：STOPTASK 1（回零任务）
4. 状态清除：清除所有队列和状态
5. 任务重启：自动重启打螺丝任务

=== 异常恢复 ===
1. 任务异常检测：PROC_STATUS()监控
2. 自动重启：异常任务自动重启
3. 状态恢复：全局变量状态恢复
4. 队列保护：异常时清除队列状态

【性能参数】
- 主控制任务扫描周期：50ms
- 打螺丝任务检查周期：100ms
- 按键响应时间：<100ms
- 任务启动时间：<50ms
- 状态更新频率：20Hz
- 支持最大并发任务：3个

【注意事项】
1. 必须先运行主控制任务（螺丝机多线程简化版.bas）
2. 所有任务文件必须在同一目录下
3. 子任务文件名不能修改
4. 所有函数都在主控制任务中定义
5. 子任务文件极简，无需修改

【故障排除】
1. 函数名冲突错误
   - 确保只运行主控制任务
   - 检查是否有重复的函数定义
   - 子任务文件应该极简

2. 按键无响应
   - 检查主控制任务是否正在运行
   - 确认输入信号连接正确
   - 查看扫描周期是否正常

3. 任务启动失败
   - 检查子任务文件是否存在
   - 确认文件名是否正确
   - 查看RUNTASK命令是否成功

【版本对比】
多线程优化版 → 多线程简化版：
+ 彻底解决函数名冲突问题
+ 所有共享函数集中管理
+ 子任务文件极简（仅3行代码）
+ 更易维护和调试
+ 架构更加清晰
+ 无函数重复定义风险

===============================================================================
