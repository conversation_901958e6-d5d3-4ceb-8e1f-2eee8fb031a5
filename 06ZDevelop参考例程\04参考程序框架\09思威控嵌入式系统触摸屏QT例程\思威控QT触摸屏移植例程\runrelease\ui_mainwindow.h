/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 5.5.0
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QAction>
#include <QtWidgets/QApplication>
#include <QtWidgets/QButtonGroup>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHeaderView>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QToolBar>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralWidget;
    QComboBox *comboBox_IpList;
    QPushButton *pushButton_OpenEth;
    QPushButton *pushButton_CloseEth;
    QLabel *label;
    QLabel *label_2;
    QLabel *label_3;
    QLineEdit *lineEdit_ValName;
    QLineEdit *lineEdit_SetName;
    QLineEdit *lineEdit_GetVal;
    QPushButton *pushButton_SetVal;
    QPushButton *pushButton_GetVal;
    QLabel *label_4;
    QGroupBox *groupBox;
    QGroupBox *groupBox_2;
    QMenuBar *menuBar;
    QToolBar *mainToolBar;
    QStatusBar *statusBar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName(QStringLiteral("MainWindow"));
        MainWindow->resize(491, 375);
        centralWidget = new QWidget(MainWindow);
        centralWidget->setObjectName(QStringLiteral("centralWidget"));
        comboBox_IpList = new QComboBox(centralWidget);
        comboBox_IpList->setObjectName(QStringLiteral("comboBox_IpList"));
        comboBox_IpList->setGeometry(QRect(80, 40, 151, 31));
        pushButton_OpenEth = new QPushButton(centralWidget);
        pushButton_OpenEth->setObjectName(QStringLiteral("pushButton_OpenEth"));
        pushButton_OpenEth->setGeometry(QRect(253, 40, 98, 27));
        pushButton_CloseEth = new QPushButton(centralWidget);
        pushButton_CloseEth->setObjectName(QStringLiteral("pushButton_CloseEth"));
        pushButton_CloseEth->setGeometry(QRect(367, 40, 98, 27));
        label = new QLabel(centralWidget);
        label->setObjectName(QStringLiteral("label"));
        label->setGeometry(QRect(25, 163, 91, 20));
        label_2 = new QLabel(centralWidget);
        label_2->setObjectName(QStringLiteral("label_2"));
        label_2->setGeometry(QRect(45, 202, 71, 20));
        label_3 = new QLabel(centralWidget);
        label_3->setObjectName(QStringLiteral("label_3"));
        label_3->setGeometry(QRect(45, 245, 71, 20));
        lineEdit_ValName = new QLineEdit(centralWidget);
        lineEdit_ValName->setObjectName(QStringLiteral("lineEdit_ValName"));
        lineEdit_ValName->setGeometry(QRect(140, 160, 113, 27));
        lineEdit_SetName = new QLineEdit(centralWidget);
        lineEdit_SetName->setObjectName(QStringLiteral("lineEdit_SetName"));
        lineEdit_SetName->setGeometry(QRect(140, 200, 113, 27));
        lineEdit_GetVal = new QLineEdit(centralWidget);
        lineEdit_GetVal->setObjectName(QStringLiteral("lineEdit_GetVal"));
        lineEdit_GetVal->setGeometry(QRect(140, 243, 113, 27));
        pushButton_SetVal = new QPushButton(centralWidget);
        pushButton_SetVal->setObjectName(QStringLiteral("pushButton_SetVal"));
        pushButton_SetVal->setGeometry(QRect(290, 197, 98, 27));
        pushButton_GetVal = new QPushButton(centralWidget);
        pushButton_GetVal->setObjectName(QStringLiteral("pushButton_GetVal"));
        pushButton_GetVal->setGeometry(QRect(290, 243, 98, 27));
        label_4 = new QLabel(centralWidget);
        label_4->setObjectName(QStringLiteral("label_4"));
        label_4->setGeometry(QRect(50, 50, 21, 16));
        groupBox = new QGroupBox(centralWidget);
        groupBox->setObjectName(QStringLiteral("groupBox"));
        groupBox->setGeometry(QRect(20, 10, 501, 61));
        groupBox_2 = new QGroupBox(centralWidget);
        groupBox_2->setObjectName(QStringLiteral("groupBox_2"));
        groupBox_2->setGeometry(QRect(20, 130, 491, 171));
        MainWindow->setCentralWidget(centralWidget);
        groupBox_2->raise();
        groupBox->raise();
        comboBox_IpList->raise();
        pushButton_OpenEth->raise();
        pushButton_CloseEth->raise();
        label->raise();
        label_2->raise();
        label_3->raise();
        lineEdit_ValName->raise();
        lineEdit_SetName->raise();
        lineEdit_GetVal->raise();
        pushButton_SetVal->raise();
        pushButton_GetVal->raise();
        label_4->raise();
        menuBar = new QMenuBar(MainWindow);
        menuBar->setObjectName(QStringLiteral("menuBar"));
        menuBar->setGeometry(QRect(0, 0, 491, 25));
        MainWindow->setMenuBar(menuBar);
        mainToolBar = new QToolBar(MainWindow);
        mainToolBar->setObjectName(QStringLiteral("mainToolBar"));
        MainWindow->addToolBar(Qt::TopToolBarArea, mainToolBar);
        statusBar = new QStatusBar(MainWindow);
        statusBar->setObjectName(QStringLiteral("statusBar"));
        MainWindow->setStatusBar(statusBar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QApplication::translate("MainWindow", "MainWindow", 0));
        comboBox_IpList->clear();
        comboBox_IpList->insertItems(0, QStringList()
         << QApplication::translate("MainWindow", "************", 0)
        );
        pushButton_OpenEth->setText(QApplication::translate("MainWindow", "Open", 0));
        pushButton_CloseEth->setText(QApplication::translate("MainWindow", "Close", 0));
        label->setText(QApplication::translate("MainWindow", "Value Name:", 0));
        label_2->setText(QApplication::translate("MainWindow", "Set Value:", 0));
        label_3->setText(QApplication::translate("MainWindow", "Get Value:", 0));
        lineEdit_ValName->setText(QApplication::translate("MainWindow", "g_fnum", 0));
        lineEdit_SetName->setText(QApplication::translate("MainWindow", "66", 0));
        pushButton_SetVal->setText(QApplication::translate("MainWindow", "SetVal", 0));
        pushButton_GetVal->setText(QApplication::translate("MainWindow", "GetVal", 0));
        label_4->setText(QApplication::translate("MainWindow", "IP:", 0));
        groupBox->setTitle(QApplication::translate("MainWindow", "IP Settings:", 0));
        groupBox_2->setTitle(QApplication::translate("MainWindow", "Operation:", 0));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
