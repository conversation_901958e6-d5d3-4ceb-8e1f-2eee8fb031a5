<!-- manual page source format generated by PolyglotMan v3.2, -->
<!-- available at http://polyglotman.sourceforge.net/ -->

<html>
<head>
<title>MAKE(1) manual page</title>
</head>
<body bgcolor='white'>
<a href='#toc'>Table of Contents</a><p>

<h2><a name='sect0' href='#toc0'>Name</a></h2>
make - GNU make utility to maintain groups of programs 
<h2><a name='sect1' href='#toc1'>Synopsis</a></h2>
<b>make
</b> [ <b>-f</b> <i>makefile</i> ] [ options ] ... [ targets ] ... 
<h2><a name='sect2' href='#toc2'>Warning</a></h2>
This man page is an extract
of the documentation of GNU <i>make</i>. It is updated only occasionally, because
the GNU project does not use nroff. For complete, current documentation,
refer to the Info file <b>make.info</b> which is made from the Texinfo source file
<b>make.texi</b>. 
<h2><a name='sect3' href='#toc3'>Description</a></h2>
<p>
The purpose of the <i>make</i> utility is to determine automatically
which pieces of a large program need to be recompiled, and issue the commands
to recompile them. The manual describes the GNU implementation of <i>make</i>,
which was written by <PERSON> Stallman and Roland McGrath, and is currently
maintained by <PERSON> <PERSON>. Our examples show C programs, since they are most
common, but you can use <i>make</i> with any programming language whose compiler
can be run with a shell command. In fact, <i>make</i> is not limited to programs.
You can use it to describe any task where some files must be updated automatically
from others whenever the others change. <p>
To prepare to use <i>make</i>, you must
write a file called the <i>makefile</i> that describes the relationships among
files in your program, and the states the commands for updating each file.
In a program, typically the executable file is updated from object files,
which are in turn made by compiling source files. <p>
Once a suitable makefile
exists, each time you change some source files, this simple shell command:
<p>
<blockquote><b>make</b> </blockquote>
<p>
suffices to perform all necessary recompilations. The <i>make</i> program
uses the makefile data base and the last-modification times of the files
to decide which of the files need to be updated. For each of those files,
it issues the commands recorded in the data base. <p>
<i>make</i> executes commands
in the <i>makefile</i> to update one or more target <i>names</i>, where <i>name</i> is typically
a program. If no <b>-f</b> option is present, <i>make</i> will look for the makefiles <i>GNUmakefile</i>,
<i>makefile</i>, and <i>Makefile</i>, in that order. <p>
Normally you should call your makefile
either <i>makefile</i> or <i>Makefile</i>. (We recommend <i>Makefile</i> because it appears prominently
near the beginning of a directory listing, right near other important files
such as <i>README</i>.) The first name checked, <i>GNUmakefile</i>, is not recommended
for most makefiles. You should use this name if you have a makefile that
is specific to GNU <i>make</i>, and will not be understood by other versions of
<i>make</i>. If <i>makefile</i> is &lsquo;-&rsquo;, the standard input is read. <p>
<i>make</i> updates a target
if it depends on prerequisite files that have been modified since the target
was last modified, or if the target does not exist. 
<h2><a name='sect4' href='#toc4'>Options</a></h2>
<p>

<dl>

<dt><b>-b</b>,<b> -m</b> </dt>
<dd>These options
are ignored for compatibility with other versions of <i>make</i>. </dd>

<dt><b>-B</b>,<b> --always-make</b>
</dt>
<dd>Unconditionally make all targets. </dd>

<dt><b>-C</b> <i>dir</i>, <b>--directory</b>=<i>dir</i> </dt>
<dd>Change to directory
<i>dir</i> before reading the makefiles or doing anything else. If multiple <b>-C</b> options
are specified, each is interpreted relative to the previous one: <b>-C </b>/ <b>-C
</b>etc is equivalent to <b>-C </b>/etc. This is typically used with recursive invocations
of <i>make</i>. </dd>

<dt><b>-d</b> </dt>
<dd>Print debugging information in addition to normal processing.
The debugging information says which files are being considered for remaking,
which file-times are being compared and with what results, which files actually
need to be remade, which implicit rules are considered and which are applied---everything
interesting about how <i>make</i> decides what to do. </dd>

<dt><b>--debug</b><i>[=FLAGS]</i> </dt>
<dd>Print debugging
information in addition to normal processing. If the <i>FLAGS</i> are omitted,
then the behavior is the same as if <b>-d</b> was specified. <i>FLAGS</i> may be <i>a</i> for
all debugging output (same as using <b>-d</b>), <i>b</i> for basic debugging, <i>v</i> for more
verbose basic debugging, <i>i</i> for showing implicit rules, <i>j</i> for details on
invocation of commands, and <i>m</i> for debugging while remaking makefiles. </dd>

<dt><b>-e</b>,<b>
--environment-overrides</b> </dt>
<dd>Give variables taken from the environment precedence
over variables from makefiles. </dd>

<dt>+<b>-f</b> <i>file</i>, <b>--file</b>=<i>file</i>, <b>--makefile</b>=<i>FILE</i> </dt>
<dd>Use <i>file</i>
as a makefile. </dd>

<dt><b>-i</b>,<b> --ignore-errors</b> </dt>
<dd>Ignore all errors in commands executed to
remake files. </dd>

<dt><b>-I</b> <i>dir</i>, <b>--include-dir</b>=<i>dir</i> </dt>
<dd>Specifies a directory <i>dir</i> to search
for included makefiles. If several <b>-I</b> options are used to specify several
directories, the directories are searched in the order specified. Unlike
the arguments to other flags of <i>make</i>, directories given with <b>-I</b> flags may
come directly after the flag: <b>-I</b><i>dir</i> is allowed, as well as <b>-I </b><i>dir.</i> This syntax
is allowed for compatibility with the C preprocessor&rsquo;s <b>-I</b> flag. </dd>

<dt><b>-j</b> [<i>jobs</i>],
<b>--jobs</b>[=<i>jobs</i>] </dt>
<dd>Specifies the number of <i>jobs</i> (commands) to run simultaneously.
If there is more than one <b>-j</b> option, the last one is effective. If the <b>-j</b>
option is given without an argument, <i>make</i> will not limit the number of
jobs that can run simultaneously. </dd>

<dt><b>-k</b>,<b> --keep-going</b> </dt>
<dd>Continue as much as possible
after an error. While the target that failed, and those that depend on it,
cannot be remade, the other dependencies of these targets can be processed
all the same. </dd>

<dt><b>-l</b> [<i>load</i>], <b>--load-average</b>[=<i>load</i>] </dt>
<dd>Specifies that no new jobs (commands)
should be started if there are others jobs running and the load average
is at least <i>load</i> (a floating-point number). With no argument, removes a previous
load limit. </dd>

<dt><b>-L</b>,<b> --check-symlink-times</b> </dt>
<dd>Use the latest mtime between symlinks and
target. </dd>

<dt><b>-n</b>,<b> --just-print</b>,<b> --dry-run</b>,<b> --recon</b> </dt>
<dd>Print the commands that would be executed,
but do not execute them. </dd>

<dt><b>-o</b> <i>file</i>, <b>--old-file</b>=<i>file</i>, <b>--assume-old</b>=<i>file</i> </dt>
<dd>Do not remake
the file <i>file</i> even if it is older than its dependencies, and do not remake
anything on account of changes in <i>file</i>. Essentially the file is treated
as very old and its rules are ignored. </dd>

<dt><b>-p</b>,<b> --print-data-base</b> </dt>
<dd>Print the data base
(rules and variable values) that results from reading the makefiles; then
execute as usual or as otherwise specified. This also prints the version
information given by the <b>-v</b> switch (see below). To print the data base without
trying to remake any files, use <b>make</b> <b>-p</b> <b>-f</b><i>/dev/null.</i> </dd>

<dt><b>-q</b>,<b> --question</b> </dt>
<dd>&lsquo;&lsquo;Question
mode&rsquo;&rsquo;. Do not run any commands, or print anything; just return an exit status
that is zero if the specified targets are already up to date, nonzero otherwise.
</dd>

<dt><b>-r</b>,<b> --no-builtin-rules</b> </dt>
<dd>Eliminate use of the built-in implicit rules. Also clear
out the default list of suffixes for suffix rules. </dd>

<dt><b>-R</b>,<b> --no-builtin-variables</b>
</dt>
<dd>Don&rsquo;t define any built-in variables. </dd>

<dt><b>-s</b>,<b> --silent</b>,<b> --quiet</b> </dt>
<dd>Silent operation; do
not print the commands as they are executed. </dd>

<dt><b>-S</b>,<b> --no-keep-going</b>,<b> --stop</b> </dt>
<dd>Cancel
the effect of the <b>-k</b> option. This is never necessary except in a recursive
<i>make</i> where <b>-k</b> might be inherited from the top-level <i>make</i> via MAKEFLAGS or
if you set <b>-k</b> in MAKEFLAGS in your environment. </dd>

<dt><b>-t</b>,<b> --touch</b> </dt>
<dd>Touch files (mark
them up to date without really changing them) instead of running their
commands. This is used to pretend that the commands were done, in order
to fool future invocations of <i>make</i>. </dd>

<dt><b>-v</b>,<b> --version</b> </dt>
<dd>Print the version of the
<i>make</i> program plus a copyright, a list of authors and a notice that there
is no warranty. </dd>

<dt><b>-w</b>,<b> --print-directory</b> </dt>
<dd>Print a message containing the working
directory before and after other processing. This may be useful for tracking
down errors from complicated nests of recursive <i>make</i> commands. </dd>

<dt><b>--no-print-directory</b>
</dt>
<dd>Turn off <b>-w</b>, even if it was turned on implicitly. </dd>

<dt><b>-W</b> <i>file</i>, <b>--what-if</b>=<i>file</i>, <b>--new-file</b>=<i>file</i>,
<b>--assume-new</b>=<i>file</i> </dt>
<dd>Pretend that the target <i>file</i> has just been modified. When
used with the <b>-n</b> flag, this shows you what would happen if you were to modify
that file. Without <b>-n</b>, it is almost the same as running a <i>touch</i> command on
the given file before running <i>make</i>, except that the modification time is
changed only in the imagination of <i>make</i>. </dd>

<dt><b>--warn-undefined-variables</b> </dt>
<dd>Warn when
an undefined variable is referenced. </dd>
</dl>

<h2><a name='sect5' href='#toc5'>Exit Status</a></h2>
GNU <i>make</i> exits with a status
of zero if all makefiles were successfully parsed and no targets that were
built failed.  A status of one will be returned if the <b>-q</b> flag was used and
<i>make</i> determines that a target needs to be rebuilt.  A status of two will
be returned if any errors were encountered. 
<h2><a name='sect6' href='#toc6'>See Also</a></h2>
<i>The GNU Make Manual</i>

<h2><a name='sect7' href='#toc7'>Bugs</a></h2>
See the chapter &lsquo;Problems and Bugs&rsquo; in <i>The GNU Make Manual</i>. 
<h2><a name='sect8' href='#toc8'>Author</a></h2>
This
manual page contributed by Dennis Morse of Stanford University. It has been
reworked by Roland McGrath.  Further updates contributed by Mike Frysinger.

<h2><a name='sect9' href='#toc9'>Copyright</a></h2>
Copyright (C) 1992, 1993, 1996, 1999 Free Software Foundation,
Inc. This file is part of GNU <i>make</i>. <p>
GNU <i>make</i> is free software; you can redistribute
it and/or modify it under the terms of the GNU General Public License as
published by the Free Software Foundation; either version 2, or (at your
option) any later version. <p>
GNU <i>make</i> is distributed in the hope that it will
be useful, but WITHOUT ANY WARRANTY; without even the implied warranty
of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU General
Public License for more details. <p>
You should have received a copy of the
GNU General Public License along with GNU <i>make</i>; see the file COPYING.  If
not, write to the Free Software Foundation, Inc., 51 Franklin St, Fifth
Floor, Boston, MA 02110-1301, USA. <p>

<hr><p>
<a name='toc'><b>Table of Contents</b></a><p>
<ul>
<li><a name='toc0' href='#sect0'>Name</a></li>
<li><a name='toc1' href='#sect1'>Synopsis</a></li>
<li><a name='toc2' href='#sect2'>Warning</a></li>
<li><a name='toc3' href='#sect3'>Description</a></li>
<li><a name='toc4' href='#sect4'>Options</a></li>
<li><a name='toc5' href='#sect5'>Exit Status</a></li>
<li><a name='toc6' href='#sect6'>See Also</a></li>
<li><a name='toc7' href='#sect7'>Bugs</a></li>
<li><a name='toc8' href='#sect8'>Author</a></li>
<li><a name='toc9' href='#sect9'>Copyright</a></li>
</ul>
</body>
</html>
