﻿注：本编译是编译QT5.5在linux下的编译环境，支持思威控、国佳，goukia品牌的屏。

编译环境修改，编译步骤如下：
1. 打开修改文件compile.bat
2. 修改M:/takepagage/linuxbuild路径为你文件夹解压的路径，如你文件解压到F盘根目录下，M:/takepagage/linuxbuild为F:/linuxbuild; 总共修改的地方有5处；
3. 示例程序默认工程名称为：easyhmi， 如需要修改工程名称，则修改@set PRO_NAME=easyhmi 即可
4. 示例程序默认源代码文件夹名称为：runproject，如需要修改，则修改@set PRO_QTSRC_LINUXPATH=M:/takepagage/linuxbuild/runproject即可
5. 双击compile.bat编译，编译成功后，linux可执行文件会出现在runrelease文件夹下名为easyhmi

更新可执行文件到屏：
1. 打开makedownload文件夹，双击makedownfile.exe运行，点击“浏览”选择编译生成的easyhmi，点击生成按钮
   当前文件夹下出现down文件夹，里面生成的文件为更新文件
2. 插上USB下载线连接到屏和电脑，电脑U盘位置将出现个/HMIData的盘符；
3. 将upt_ehapps.md5和upt_ehapps.zip文件拷贝到HMIData盘的根目录下
4. 断电重启屏，完成更新
5. 参照示例工程中的ehapps文件夹和upt_ehapps.zip文件