# 螺丝机控制系统

## 系统概述

本系统是基于正运动控制器开发的四轴螺丝机控制程序，支持步进电机开环脉冲控制和电批Modbus通讯。

### 主要功能
- 四轴运动控制（X轴、左Y轴、右Y轴、Z轴）
- 电批RS485 Modbus RTU通讯控制
- 圆弧插补运动轨迹
- 自动回零功能
- 螺丝位置参数化设置
- 左右两侧独立作业控制

## 硬件配置

### 轴配置
- **轴0 (X轴)**：横向移动轴
- **轴1 (Y1轴)**：左侧纵向移动轴
- **轴2 (Y2轴)**：右侧纵向移动轴  
- **轴3 (Z轴)**：垂直升降轴

### 输入信号
- **IN0**：左侧开始按键
- **IN1**：右侧开始按键
- **IN2**：系统回零按键
- **IN3**：急停按键
- **IN8-IN11**：正向限位开关
- **IN12-IN15**：负向限位开关
- **IN16-IN19**：原点开关

### 输出信号
- **OP0**：吸螺丝控制（低电平有效）

### 通讯接口
- **RS485**：电批Modbus RTU通讯（默认地址10）

## 文件说明

### 1. 螺丝机主程序.bas ⭐ **推荐使用**
独立主控制程序，包含所有核心功能：
- 系统初始化和轴参数配置
- Modbus通讯设置
- 四轴回零控制
- 左右两侧打螺丝作业流程
- 圆弧插补运动
- 电批控制和状态监控
- 无需依赖其他文件，可直接运行

### 2. 螺丝机控制程序.bas
原始主控制程序（需要修复后使用）

### 3. 螺丝机参数配置.bas
参数配置程序（需要修复后使用）

### 4. 螺丝机操作界面.bas
操作界面程序（需要修复后使用）

### 5. 螺丝机测试程序.bas
系统测试程序（需要修复后使用）

## 使用流程

### 1. 系统启动（推荐方式）
```basic
' 运行独立主程序（推荐）
RUN "螺丝机主程序.bas"
```

### 2. 参数配置
```basic
' 在主程序运行后，可以通过函数设置参数
CALL SetScrewPosition(1, 1, 100, 80, 0)    ' 设置左侧第1个螺丝位置
CALL ShowSystemStatus()                     ' 显示系统状态
```

### 3. 系统回零
- 按下IN2回零按键
- 或通过程序调用：`RUN "HomeTask", 2`

### 4. 开始作业
- 按下IN0开始左侧打螺丝
- 按下IN1开始右侧打螺丝

## 参数设置

### 螺丝位置设置
```basic
' 设置螺丝位置
CALL SetScrewPosition(side, screw_num, pos_x, pos_y, pos_z)
' side: 1-左侧, 2-右侧
' screw_num: 螺丝编号(1开始)
' pos_x, pos_y, pos_z: 位置坐标
```

### 螺丝数量设置
```basic
' 设置螺丝数量
CALL SetScrewCount(side, count)
' side: 1-左侧, 2-右侧
' count: 螺丝数量(最多33个)
```

### 吸螺丝位置设置
```basic
' 设置吸螺丝位置
CALL SetPickPosition(pos_x, pos_y, pos_z)
```

### 批量位置设置
```basic
' 设置矩形阵列螺丝位置
CALL SetScrewArray(side, start_x, start_y, start_z, rows, cols, spacing_x, spacing_y)
```

## 运动控制

### 圆弧插补运动
系统使用三点圆弧插补实现平滑的运动轨迹：
- 从吸螺丝位置到螺丝孔位：弧形轨迹
- 从螺丝孔位回到吸螺丝位置：弧形轨迹
- 避免直线运动可能的碰撞

### 运动参数
```basic
SPEED = 150, 150, 150, 80       ' 各轴速度 mm/s
ACCEL = 1500, 1500, 1500, 800   ' 各轴加速度 mm/s²
MERGE = ON                      ' 连续插补
CORNER_MODE = 2 + 8             ' 拐角减速 + 曲率限速
```

## 电批控制

### Modbus通讯参数
- 波特率：115200
- 数据位：8
- 停止位：1
- 校验位：无
- 设备地址：10（可配置）

### 控制命令
```basic
' 锁紧螺丝
CALL ScrewDriverControl(1)

' 松开螺丝  
CALL ScrewDriverControl(2)

' 检查状态
status = CheckScrewDriverStatus()
```

## 安全功能

### 急停保护
- 硬件急停：IN3输入
- 软件急停：`RAPIDSTOP(2)`
- 急停后自动关闭吸螺丝输出

### 限位保护
- 各轴配置正负限位开关
- 限位触发时自动停止运动

### 回零保护
- 系统启动后必须先回零
- 未回零状态禁止作业操作

## 测试功能

### 单项测试
```basic
CALL TestPickScrew()           ' 测试吸螺丝
CALL TestScrewDriver()         ' 测试电批
CALL TestMovement(x, y, z)     ' 测试运动
```

### 单个螺丝测试
```basic
CALL TestSingleScrew(side, screw_num)
```

## 故障排除

### 常见问题

1. **系统无法启动**
   - 检查控制器连接
   - 检查程序下载是否成功

2. **回零失败**
   - 检查原点开关连接
   - 检查限位开关状态
   - 检查INVERT_IN设置

3. **电批通讯失败**
   - 检查RS485连接
   - 检查电批地址设置
   - 检查波特率配置

4. **运动异常**
   - 检查轴参数设置
   - 检查UNITS脉冲当量
   - 检查限位开关

### 状态查询
```basic
CALL ShowSystemStatus()        ' 显示系统状态
CALL ValidateConfiguration()   ' 验证配置
```

## 扩展功能

### HMI接口
系统预留了Modbus寄存器接口，可连接HMI触摸屏：
- 控制寄存器：MODBUS_REG(1000-1015)
- 状态寄存器：MODBUS_REG(0-50)
- 位置寄存器：MODBUS_IEEE(20-50)

### 数据保存
配置参数可保存到FLASH：
```basic
CALL SaveConfiguration()       ' 保存配置
CALL LoadConfiguration()       ' 加载配置
```

## 技术支持

如有问题请联系正运动技术支持：
- 官网：www.zmotion.com.cn
- 技术论坛：bbs.zmotion.com.cn

## 版本历史

- v1.0 (2025-07-30)：初始版本
  - 四轴运动控制
  - 电批Modbus通讯
  - 圆弧插补运动
  - 参数化配置
