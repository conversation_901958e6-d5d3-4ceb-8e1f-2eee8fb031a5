===============================================================================
                        两轴插补测试程序 - 输入电平控制使用说明
===============================================================================

【输入电平控制概述】

程序已修改为通过输入电平触发各个测试，更符合实际应用场景。
采用上升沿触发方式，避免重复执行，适合与外部控制系统集成。

【输入引脚分配】

=== 默认引脚分配（input_pin_base = 0）===
```
IN0  - 两轴直线插补测试（Test1_Linear）
IN1  - 两轴圆弧插补测试（Test2_Circular）
IN2  - 非连续插补测试（Test3_NonContinuous）
IN3  - 连续插补测试（Test4_Continuous）
IN4  - 前瞻拐角减速测试（Test5_CornerDecel）
IN5  - 自动倒角测试（Test6_AutoChamfer）
IN6  - 组合前瞻测试（Test7_Combined）
IN7  - 复杂轨迹测试（Test8_Complex）
```

=== 自定义引脚分配 ===
```
如需使用其他输入引脚，可在程序启动前设置：
CALL SetInputPinBase(8)              ' 使用IN8-IN15
CALL SetInputPinBase(16)             ' 使用IN16-IN23

或直接修改程序中的：
GLOBAL input_pin_base = 8            ' 改为所需的起始引脚号
```

【硬件连接】

=== 输入信号要求 ===
```
信号类型：数字输入信号
电压等级：24V或5V（根据控制器规格）
触发方式：上升沿触发（0→1）
信号宽度：建议≥100ms（避免抖动）
信号源：PLC、按钮、传感器等
```

=== 典型连接方式 ===
```
方式1：PLC控制
PLC输出 → 控制器数字输入
优势：可编程控制，逻辑灵活

方式2：按钮控制
按钮开关 → 控制器数字输入
优势：手动控制，操作直观

方式3：传感器触发
传感器输出 → 控制器数字输入
优势：自动触发，无人值守

连接示例：
IN0+ ← 24V信号
IN0- ← 0V（GND）
```

【程序运行模式】

=== 模式1：输入电平控制模式（默认）===
```
启动方式：
程序自动启动MainLoop()

特点：
- 持续监控输入电平变化
- 检测到上升沿自动执行对应测试
- 自动设置示波器监控
- 适合自动化应用

使用流程：
1. 程序启动后自动进入监控模式
2. 外部信号触发IN0-IN7任一引脚
3. 程序检测上升沿并执行对应测试
4. 测试完成后继续监控
```

=== 模式2：手动测试模式 ===
```
启动方式：
注释掉CALL MainLoop()
改为CALL ManualTest()

特点：
- 不监控输入电平
- 手动调用测试函数
- 适合调试和单独测试

使用方法：
CALL Test1_Linear()              ' 手动执行测试1
CALL Test2_Circular()            ' 手动执行测试2
...
```

【使用步骤】

=== 第一步：程序加载 ===
```
1. 将修正版程序加载到控制器
2. 程序自动启动并显示引脚分配信息
3. 进入输入电平监控模式
```

=== 第二步：硬件连接 ===
```
1. 根据引脚分配连接输入信号
2. 确认信号电压等级匹配
3. 测试信号连通性
```

=== 第三步：示波器设置 ===
```
1. 连接示波器到VP_SPEED(0)和VP_SPEED(1)
2. 设置合适的时间和电压刻度
3. 程序会在测试前自动触发示波器
```

=== 第四步：执行测试 ===
```
1. 给对应输入引脚施加高电平信号
2. 程序检测上升沿并执行测试
3. 观察示波器速度曲线变化
4. 测试完成后信号可以撤销
```

【测试执行流程】

=== 上升沿检测机制 ===
```
检测逻辑：
current_state = IN(pin)              ' 读取当前状态
rising_edge = (current_state = 1) AND (last_input_state = 0)

触发条件：
- 当前状态为高电平（1）
- 上次状态为低电平（0）
- 即检测到0→1的上升沿

避免重复：
- 记录上次状态
- 只在状态变化时触发
- 高电平保持期间不重复执行
```

=== 自动执行流程 ===
```
1. 检测到上升沿
2. 打印触发信息
3. 自动设置示波器（CALL SetupScope()）
4. 执行对应测试函数
5. 打印完成信息
6. 继续监控其他输入
```

【测试项目说明】

=== IN0 - 两轴直线插补测试 ===
```
功能：验证基本直线插补原理
轨迹：(0,0) → (100,100)
观察：两轴速度协调，理论计算验证
建议：作为第一个测试项目
```

=== IN1 - 两轴圆弧插补测试 ===
```
功能：验证圆弧插补轨迹生成
轨迹：三点圆弧，逆时针
观察：圆弧轨迹平滑性
```

=== IN2 - 非连续插补测试 ===
```
功能：展示MERGE=OFF的问题
轨迹：矩形轨迹，四段直线
观察：段间停顿，效率低下
```

=== IN3 - 连续插补测试 ===
```
功能：展示MERGE=ON的改善
轨迹：矩形轨迹，连续执行
观察：段间连续，但拐角冲击
```

=== IN4 - 前瞻拐角减速测试 ===
```
功能：CORNER_MODE=2效果
特点：拐角处自动减速
观察：减速效果，冲击减少
```

=== IN5 - 自动倒角测试 ===
```
功能：CORNER_MODE=32效果
特点：拐角处轨迹倒角
观察：轨迹变化，速度保持
```

=== IN6 - 组合前瞻测试 ===
```
功能：CORNER_MODE=2+32效果
特点：倒角+减速组合
观察：综合优化效果
```

=== IN7 - 复杂轨迹测试 ===
```
功能：直线+圆弧组合轨迹
特点：复杂路径连续插补
观察：整体连续性
```

【监控和分析】

=== 示波器监控 ===
```
自动设置：
程序在每次测试前自动调用SetupScope()
自动触发示波器并显示监控说明

监控信号：
VP_SPEED(0) - X轴单轴速度
VP_SPEED(1) - Y轴单轴速度
MSPEED(0)   - X轴分速度（验证用）
MSPEED(1)   - Y轴分速度（验证用）

显示设置：
蓝色：VP_SPEED(0)，刻度100，偏移0
红色：VP_SPEED(1)，刻度100，偏移-60
绿色：MSPEED(0)，刻度100，偏移-120
黄色：MSPEED(1)，刻度100，偏移-180
```

=== 分析要点 ===
```
直线插补：
✅ 两轴速度比例正确
✅ 速度曲线对称
✅ 同时启动和停止

圆弧插补：
✅ 轨迹平滑连续
✅ 速度按圆弧变化
✅ 无突变和尖峰

连续插补：
✅ 段间无断点
✅ 整体连续性
✅ 前瞻功能效果
```

【参数配置】

=== 可调参数 ===
```
输入引脚配置：
input_pin_base = 0               ' 起始引脚号
enable_input_control = 1         ' 启用输入控制

运动参数：
SPEED = 100, 100                 ' 轴速度
ACCEL = 1000, 1000               ' 加速度
DECEL = 1000, 1000               ' 减速度
FORCE_SPEED = 100                ' 插补速度

平滑参数：
SRAMP = 50, 50                   ' S曲线时间
VP_MODE = 7, 7                   ' SS曲线

前瞻参数：
CORNER_MODE = 32                 ' 前瞻模式
ZSMOOTH = 10                     ' 倒角半径
DECEL_ANGLE = 30 * (PI/180)      ' 减速角度
STOP_ANGLE = 90 * (PI/180)       ' 停止角度

检测参数：
DELAY 50                         ' 检测间隔50ms
```

=== 参数修改方法 ===
```
1. 修改程序中的全局变量
2. 重新加载程序
3. 或在运行时动态修改：
   CALL SetInputPinBase(8)       ' 改变起始引脚
```

【故障排除】

=== 常见问题 ===
```
问题1：输入信号无响应
检查：信号连接、电压等级、引脚配置
解决：确认硬件连接正确，信号电平匹配

问题2：重复执行测试
原因：信号抖动或持续高电平
解决：增加信号滤波，确保干净的上升沿

问题3：示波器无信号
检查：示波器连接、触发设置
解决：确认信号线连接，检查触发条件

问题4：测试执行异常
检查：轴参数设置、机械连接
解决：验证运动参数，检查机械系统
```

=== 调试方法 ===
```
1. 手动测试模式验证功能
2. 检查输入状态：PRINT IN(0), IN(1), ...
3. 监控程序输出信息
4. 使用示波器分析信号质量
```

【应用场景】

=== 自动化集成 ===
```
PLC控制：
- PLC程序控制测试执行
- 根据工艺流程自动触发
- 实现无人值守测试

传感器触发：
- 工件到位传感器触发测试
- 按工艺节拍自动执行
- 提高测试效率

人机界面：
- 触摸屏按钮触发测试
- 操作员选择测试项目
- 直观的操作界面
```

=== 测试验证 ===
```
产品测试：
- 批量产品性能测试
- 自动化测试流程
- 数据记录和分析

研发验证：
- 算法效果验证
- 参数优化测试
- 性能对比分析

教学演示：
- 插补原理演示
- 学生实验操作
- 理论与实践结合
```

【总结】

输入电平控制的优势：
✅ **自动化程度高**：外部信号自动触发测试
✅ **集成性好**：易于与PLC、传感器等集成
✅ **操作简便**：无需手动输入命令
✅ **可靠性高**：上升沿触发避免重复执行
✅ **实时性好**：50ms检测间隔，响应迅速
✅ **灵活配置**：可自定义输入引脚分配

通过输入电平控制，测试程序更适合实际应用场景，
可以方便地集成到自动化系统中，实现高效的测试验证。

===============================================================================
