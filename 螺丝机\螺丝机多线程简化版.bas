
'=============================================================================
' 螺丝机多线程简化版（主控制任务）
' 简化的多任务架构，避免函数名冲突：
' 任务0：主控制任务（本文件，包含所有共享函数）
' 任务1：回零任务（仅包含回零逻辑）
' 任务2：打螺丝任务（仅包含打螺丝逻辑）
'=============================================================================

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL axis_home(5)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败（索引0-4）
GLOBAL left_screw_num              ' 左侧螺丝点位数量（默认8个，最多64个）
GLOBAL right_screw_num             ' 右侧螺丝点位数量（默认8个，最多64个）
GLOBAL max_screw_num               ' 单侧最大螺丝数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'双Y轴滑轨控制变量
GLOBAL left_user_pos               ' 左侧用户位置（靠近用户侧）
GLOBAL right_user_pos              ' 右侧用户位置（靠近用户侧）

GLOBAL left_slide_status           ' 左侧滑轨状态：0-用户侧，1-工作侧，2-移动中
GLOBAL right_slide_stat             ' 右侧滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue                   ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue                  ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy             ' 电批忙碌状态：0-空闲，1-忙碌

'任务状态变量
GLOBAL task_home_running            ' 回零任务运行状态
GLOBAL task_screw_run               ' 打螺丝任务运行状态

'打螺丝任务控制变量
GLOBAL screw_task_stop              ' 打螺丝任务停止标志：0-继续运行，1-停止

'螺丝位置数据存储（每个螺丝3个数据：X,Y,Z）
GLOBAL left_start                   ' 左侧螺丝位置数据起始地址（0-191）
GLOBAL right_start                  ' 右侧螺丝位置数据起始地址（200-391）
'数据布局：64个螺丝 × 3个坐标 = 192个数据位置

'吸螺丝位置（固定位置，与Y轴无关）
GLOBAL pick_x                       ' 吸螺丝位置X
GLOBAL pick_z                       ' 吸螺丝位置Z（往下为正）

'Z轴高度设置（注意：安全高度必须大于0，系统强制使用三段插补）
GLOBAL screw_work_height            ' 打螺丝工作高度30mm
GLOBAL pick_safe_height             ' 吸螺丝位置安全高度8mm（必须>0）
GLOBAL work_safe_height             ' 工件位置安全高度25mm（必须>0）
GLOBAL arc_top_height               ' 圆弧插补最高点Z轴高度20mm

'主程序（任务0）
CALL InitSystem()
CALL SetupAxis()
CALL SetupData()

PRINT "=== 螺丝机多线程简化版启动 ==="
PRINT "简化的多任务架构："
PRINT "任务0：主控制（输入扫描、状态管理、所有共享函数）"
PRINT "任务1：回零任务（仅回零逻辑）"
PRINT "任务2：打螺丝任务（仅打螺丝逻辑）"
PRINT ""
PRINT "操作说明（所有按钮均为低电平触发）："
PRINT "IN0 - 左侧开始（可在任何时候按下）"
PRINT "IN1 - 右侧开始（可在任何时候按下）"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动左Y轴到用户侧"
PRINT "IN5 - 手动右Y轴到用户侧"

'启动打螺丝任务（持续运行）
PRINT "启动打螺丝任务..."
screw_task_stop = 0
RUNTASK 2, SimpleScrewTask
task_screw_run = 1

'主循环（任务0持续运行）
WHILE 1
    CALL ScanInput()                ' 扫描输入信号
    CALL UpdateTaskStatus()         ' 更新任务状态
    CALL UpdateStatus()             ' 更新系统状态
    DELAY(50)                       ' 50ms扫描周期
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 8              ' 默认左侧8个螺丝
    right_screw_num = 8             ' 默认右侧8个螺丝
    max_screw_num = 64              ' 单侧最大螺丝数量
    cur_screw = 0
    left_user_pos = 5               ' 左侧用户位置
    right_user_pos = 5              ' 右侧用户位置
    left_slide_status = 0           ' 左侧滑轨状态
    right_slide_stat = 0            ' 右侧滑轨状态

    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0

    '初始化任务状态
    task_home_running = 0
    task_screw_run = 0
    screw_task_stop = 0

    '初始化螺丝位置数据起始地址
    left_start = 0                  ' 左侧螺丝位置数据起始地址（0-191）
    right_start = 200               ' 右侧螺丝位置数据起始地址（200-391）

    '初始化吸螺丝位置
    pick_x = 50                     ' 吸螺丝位置X
    pick_z = 30                     ' 吸螺丝位置Z（往下为正）

    '初始化Z轴高度设置
    screw_work_height = 30          ' 打螺丝工作高度30mm
    pick_safe_height = 25           ' 吸螺丝位置安全高度25mm（必须>0）
    work_safe_height = 25           ' 工件位置安全高度25mm（必须>0）
    arc_top_height = 20             ' 圆弧插补最高点Z轴高度20mm

    '初始化并行运动控制变量
    next_y_target = 0               ' 下一个目标Y位置
    next_y_axis = 0                 ' 下一个目标Y轴编号
    y_move_started = 0              ' Y轴移动是否已启动
    is_last_screw = 0               ' 是否是最后一个螺丝
    user_pos_target = 0             ' 用户位置目标值

    '初始化回零状态
    FOR i = 0 TO 3
        axis_home(i) = 0
    NEXT

    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB SetupAxis()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0, 1, 2, 3)
    ATYPE = 1, 1, 1, 1              ' 步进电机开环脉冲控制
    UNITS = 1000, 100, 100, 1000    ' Y轴脉冲当量100脉冲/mm（10mm螺距）
    SPEED = 1000, 1000, 1000, 500   ' 运动速度1000mm/s (1m/s)，Z轴500mm/s
    ACCEL = 1000, 1000, 1000, 1000  ' 加速度1000mm/s² (1m/s²)
    DECEL = 1000, 1000, 1000, 1000  ' 减速度1000mm/s² (1m/s²)
    CREEP = 10, 10, 10, 5           ' 回零爬行速度

    '打印轴编号对应关系
    PRINT "=== 轴编号对应关系 ==="
    PRINT "X轴：编号0"
    PRINT "Y1轴（左侧）：编号1"
    PRINT "Y2轴（右侧）：编号2"
    PRINT "Z轴：编号3"
    PRINT "====================="
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11           ' 正限位 IN8-IN11
    REV_IN = 12, 13, 14, 15         ' 负限位 IN12-IN15
    DATUM_IN = 16, 17, 18, 19       ' 原点开关 IN16-IN19

    '信号反转(根据实际硬件调整)
    '所有输入信号都是低电平有效，不需要反转
    FOR i = 0 TO 19
        INVERT_IN(i, OFF)           ' 所有输入信号不反转，检测低电平有效
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    '注意：LOOKAHEAD和BLEND_TOL可能不是所有控制器都支持

    'S型曲线设置（平滑运动曲线）
    SRAMP = 100                     ' S型曲线平滑时间100ms（0-250ms范围）

    '圆弧插补设置（如果控制器支持）
    '注意：ARC_MODE和ARC_RADIUS可能不是所有控制器都支持
    'ARC_MODE = 1                    ' 启用圆弧插补模式
    'ARC_RADIUS = 5                  ' 默认圆弧半径5mm（可根据需要调整）

    '说明：所有运动都将使用XZ平面圆弧插补，Y轴独立移动
    '圆弧最高点固定在Z=20mm，实现平滑的弧形轨迹
    
    PRINT "轴参数设置完成（Y轴适配10mm螺距，运动速度1m/s，S型曲线100ms，标准三段轨迹）"
    PRINT "安全高度设置：取螺丝=", pick_safe_height, "mm，工件=", work_safe_height, "mm（必须>0）"
    PRINT "信号配置：所有输入信号均为低电平有效"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    '左侧螺丝位置数据(4x2阵列，8个螺丝) - 直接使用实际Y坐标，Y轴跨度大
    '第一行螺丝（Y = 50mm）
    TABLE(0) = 100                  ' 螺丝1：X=100
    TABLE(1) = 50                   ' 螺丝1：Y=50mm（第一行Y坐标）
    TABLE(2) = screw_work_height    ' 螺丝1：Z=30（打螺丝高度）
    TABLE(3) = 200                  ' 螺丝2：X=200
    TABLE(4) = 50                   ' 螺丝2：Y=50mm
    TABLE(5) = screw_work_height    ' 螺丝2：Z=30（打螺丝高度）

    '第二行螺丝（Y = 100mm）
    TABLE(6) = 100                  ' 螺丝3：X=100
    TABLE(7) = 100                  ' 螺丝3：Y=100mm（第二行Y坐标）
    TABLE(8) = screw_work_height    ' 螺丝3：Z=30（打螺丝高度）
    TABLE(9) = 200                  ' 螺丝4：X=200
    TABLE(10) = 100                 ' 螺丝4：Y=100mm
    TABLE(11) = screw_work_height   ' 螺丝4：Z=30（打螺丝高度）

    '第三行螺丝（Y = 150mm）
    TABLE(12) = 100                 ' 螺丝5：X=100
    TABLE(13) = 150                 ' 螺丝5：Y=150mm（第三行Y坐标）
    TABLE(14) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
    TABLE(15) = 200                 ' 螺丝6：X=200
    TABLE(16) = 150                 ' 螺丝6：Y=150mm
    TABLE(17) = screw_work_height   ' 螺丝6：Z=30（打螺丝高度）

    '第四行螺丝（Y = 200mm）
    TABLE(18) = 100                 ' 螺丝7：X=100
    TABLE(19) = 200                 ' 螺丝7：Y=200mm（第四行Y坐标）
    TABLE(20) = screw_work_height   ' 螺丝7：Z=30（打螺丝高度）
    TABLE(21) = 200                 ' 螺丝8：X=200
    TABLE(22) = 200                 ' 螺丝8：Y=200mm
    TABLE(23) = screw_work_height   ' 螺丝8：Z=30（打螺丝高度）

    '右侧螺丝位置数据(4x2阵列，8个螺丝) - 直接使用实际Y坐标，Y轴跨度大
    '第一行螺丝（Y = 250mm）
    TABLE(200) = 100                ' 螺丝1：X=100
    TABLE(201) = 250                 ' 螺丝1：Y=250mm（第一行Y坐标）
    TABLE(202) = screw_work_height   ' 螺丝1：Z=30（打螺丝高度）
    TABLE(203) = 200                ' 螺丝2：X=200
    TABLE(204) = 250                 ' 螺丝2：Y=250mm
    TABLE(205) = screw_work_height   ' 螺丝2：Z=30（打螺丝高度）

    '第二行螺丝（Y = 300mm）
    TABLE(206) = 100                ' 螺丝3：X=100
    TABLE(207) = 300                 ' 螺丝3：Y=300mm（第二行Y坐标）
    TABLE(208) = screw_work_height   ' 螺丝3：Z=30（打螺丝高度）
    TABLE(209) = 200                ' 螺丝4：X=200
    TABLE(210) = 300                 ' 螺丝4：Y=300mm
    TABLE(211) = screw_work_height   ' 螺丝4：Z=30（打螺丝高度）

    '第三行螺丝（Y = 350mm）
    TABLE(212) = 100                ' 螺丝5：X=100
    TABLE(213) = 350                 ' 螺丝5：Y=350mm（第三行Y坐标）
    TABLE(214) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
    TABLE(215) = 200                ' 螺丝6：X=200
    TABLE(216) = 350                 ' 螺丝6：Y=350mm
    TABLE(217) = screw_work_height   ' 螺丝6：Z=30（打螺丝高度）

    '第四行螺丝（Y = 400mm）
    TABLE(218) = 100                ' 螺丝7：X=100
    TABLE(219) = 400                 ' 螺丝7：Y=400mm（第四行Y坐标）
    TABLE(220) = screw_work_height   ' 螺丝7：Z=30（打螺丝高度）
    TABLE(221) = 200                ' 螺丝8：X=200
    TABLE(222) = 400                 ' 螺丝8：Y=400mm
    TABLE(223) = screw_work_height   ' 螺丝8：Z=30（打螺丝高度）
    
    PRINT "数据设置完成（4x2阵列，Y轴跨度大）"
    PRINT "左侧螺丝数量：", left_screw_num, "（4行2列阵列）"
    PRINT "右侧螺丝数量：", right_screw_num, "（4行2列阵列）"
    PRINT "最大支持螺丝数量：", max_screw_num, "个/侧"
    PRINT "左侧Y坐标分布：", TABLE(1), "mm,", TABLE(7), "mm,", TABLE(13), "mm,", TABLE(19), "mm（跨度", TABLE(19) - TABLE(1), "mm）"
    PRINT "右侧Y坐标分布：", TABLE(201), "mm,", TABLE(207), "mm,", TABLE(213), "mm,", TABLE(219), "mm（跨度", TABLE(219) - TABLE(201), "mm）"
    PRINT "左侧用户位置：", left_user_pos, "mm，右侧用户位置：", right_user_pos, "mm"
END SUB

'================ 设置螺丝数量 ================
GLOBAL SUB SetScrewCount(left_count, right_count)
    '验证螺丝数量范围
    IF left_count < 1 OR left_count > max_screw_num THEN
        PRINT "错误：左侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", left_count
        RETURN
    ENDIF

    IF right_count < 1 OR right_count > max_screw_num THEN
        PRINT "错误：右侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", right_count
        RETURN
    ENDIF

    '设置螺丝数量
    left_screw_num = left_count
    right_screw_num = right_count

    PRINT "螺丝数量设置完成："
    PRINT "左侧：", left_screw_num, "个螺丝"
    PRINT "右侧：", right_screw_num, "个螺丝"
    PRINT "总计：", left_screw_num + right_screw_num, "个螺丝"
END SUB

'================ 并行运动控制全局变量 ================
GLOBAL next_y_target                ' 下一个目标Y位置
GLOBAL next_y_axis                  ' 下一个目标Y轴编号
GLOBAL y_move_started               ' Y轴移动是否已启动
GLOBAL is_last_screw                ' 是否是最后一个螺丝
GLOBAL user_pos_target              ' 用户位置目标值

'================ 启动Y轴并行移动 ================
GLOBAL SUB StartYMove(y_axis, target_y, last_flag)
    next_y_target = target_y
    next_y_axis = y_axis
    is_last_screw = last_flag
    y_move_started = 1

    IF last_flag = 1 THEN
        '最后一个螺丝，移动到用户位置
        IF y_axis = 1 THEN
            user_pos_target = left_user_pos
        ELSE
            user_pos_target = right_user_pos
        ENDIF
        PRINT "启动Y轴并行移动到用户位置：", user_pos_target, "mm"
    ELSE
        PRINT "启动Y轴并行移动到下一个孔位：", target_y, "mm"
    ENDIF

    BASE(y_axis)
    IF last_flag = 1 THEN
        MOVEABS(user_pos_target) AXIS(y_axis)
    ELSE
        MOVEABS(target_y) AXIS(y_axis)
    ENDIF
END SUB

'================ 等待Y轴移动完成 ================
GLOBAL SUB WaitForYMove()
    IF y_move_started = 1 THEN
        WAIT IDLE(next_y_axis)
        IF is_last_screw = 1 THEN
            PRINT "Y轴已到达用户位置：", user_pos_target, "mm"
        ELSE
            PRINT "Y轴已到达下一个孔位：", next_y_target, "mm"
        ENDIF
        y_move_started = 0
    ENDIF
END SUB

'================ 输入扫描（任务0持续执行）================
GLOBAL SUB ScanInput()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        CALL CheckHome()
        IF RETURN = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1          ' 设置为等待状态
                PRINT "左侧任务加入队列"
                CALL LeftSlideToWork()  ' 左Y轴移动到工作位置
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键
    IF SCAN_EVENT(IN(1)) > 0 THEN
        CALL CheckHome()
        IF RETURN = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1         ' 设置为等待状态
                PRINT "右侧任务加入队列"
                CALL RightSlideToWork() ' 右Y轴移动到工作位置
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF task_home_running = 0 THEN
            PRINT "开始系统回零"
            STOPTASK 1                  ' 停止可能存在的回零任务
            RUNTASK 1, SimpleHomeTask   ' 启动回零任务
            task_home_running = 1
        ELSE
            PRINT "回零任务正在运行中"
        ENDIF
    ENDIF
    
    '手动左Y轴到用户侧
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF left_queue = 0 THEN
            PRINT "手动左Y轴到用户侧"
            CALL LeftSlideToUser()
        ELSE
            PRINT "左侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '手动右Y轴到用户侧
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF right_queue = 0 THEN
            PRINT "手动右Y轴到用户侧"
            CALL RightSlideToUser()
        ELSE
            PRINT "右侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        
        '停止所有任务
        STOPTASK 1                  ' 停止回零任务
        screw_task_stop = 1         ' 通知打螺丝任务停止
        
        '清除所有状态
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        task_home_running = 0
        
        OP(0, OFF)                  ' 关闭吸螺丝
        PRINT "所有任务已停止"
        
        '重新启动打螺丝任务
        DELAY(1000)
        screw_task_stop = 0
        STOPTASK 2
        RUNTASK 2, SimpleScrewTask
        task_screw_run = 1
        PRINT "打螺丝任务已重启"
    ENDIF
END SUB

'================ 更新任务状态 ================
GLOBAL SUB UpdateTaskStatus()
    '检查回零任务状态
    IF task_home_running = 1 THEN
        IF PROC_STATUS(1) = 0 THEN      ' 任务1已停止
            PRINT "回零任务完成"
            task_home_running = 0
            sys_status = 0              ' 回到待机状态
        ENDIF
    ENDIF

    '检查打螺丝任务状态
    IF task_screw_run = 1 THEN
        IF PROC_STATUS(2) = 0 THEN      ' 任务2已停止
            PRINT "打螺丝任务异常停止，重新启动"
            screw_task_stop = 0
            RUNTASK 2, SimpleScrewTask
            task_screw_run = 1
        ENDIF
    ENDIF
END SUB

'================ 双Y轴滑轨控制 ================
'强制移动到用户位置（找零点后使用，忽略状态检查）
GLOBAL SUB ForceLeftToUser()
    PRINT "左Y轴强制移动到用户位置..."
    left_slide_status = 2       ' 设置为移动中

    'Y轴直线移动到用户位置
    BASE(1)
    MOVEABS(left_user_pos) AXIS(1)
    WAIT IDLE(1)

    left_slide_status = 0       ' 设置为用户侧
    PRINT "左Y轴已强制到达用户位置：", left_user_pos, "mm，实际位置：", DPOS(1), "mm"
END SUB

GLOBAL SUB ForceRightToUser()
    PRINT "右Y轴强制移动到用户位置..."
    right_slide_stat = 2        ' 设置为移动中

    'Y轴直线移动到用户位置
    BASE(2)
    MOVEABS(right_user_pos) AXIS(2)
    WAIT IDLE(2)

    right_slide_stat = 0        ' 设置为用户侧
    PRINT "右Y轴已强制到达用户位置：", right_user_pos, "mm，实际位置：", DPOS(2), "mm"
END SUB
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(1)
        MOVEABS(left_user_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 0       ' 设置为用户侧
        PRINT "左Y轴已到达用户位置：", left_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB LeftSlideToWork()
    IF left_slide_status <> 1 THEN
        PRINT "左Y轴移动到第一个螺丝位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到第一个螺丝的实际Y坐标
        DIM first_screw_y
        first_screw_y = TABLE(left_start + 1)   ' 获取第一个螺丝的实际Y坐标
        BASE(1)
        MOVEABS(first_screw_y) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 1       ' 设置为工作侧
        PRINT "左Y轴已到达第一个螺丝位置：", first_screw_y, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToUser()
    IF right_slide_stat <> 0 THEN
        PRINT "右Y轴移动到用户位置..."
        right_slide_stat = 2        ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(2)
        MOVEABS(right_user_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_stat = 0        ' 设置为用户侧
        PRINT "右Y轴已到达用户位置：", right_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToWork()
    IF right_slide_stat <> 1 THEN
        PRINT "右Y轴移动到第一个螺丝位置..."
        right_slide_stat = 2        ' 设置为移动中

        'Y轴直线移动到第一个螺丝的实际Y坐标
        DIM first_screw_y
        first_screw_y = TABLE(right_start + 1)  ' 获取第一个螺丝的实际Y坐标
        BASE(2)
        MOVEABS(first_screw_y) AXIS(2)
        WAIT IDLE(2)

        right_slide_stat = 1        ' 设置为工作侧
        PRINT "右Y轴已到达第一个螺丝位置：", first_screw_y, "mm"
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO 3
        IF axis_home(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = sys_status
    MODBUS_REG(1) = left_screw_num
    MODBUS_REG(2) = right_screw_num
    MODBUS_REG(3) = cur_screw
    MODBUS_REG(4) = left_slide_status
    MODBUS_REG(5) = right_slide_stat
    MODBUS_REG(6) = left_queue
    MODBUS_REG(7) = right_queue
    MODBUS_REG(8) = screwdriver_busy
    MODBUS_REG(9) = task_home_running
    MODBUS_REG(10) = task_screw_run
    MODBUS_REG(11) = screw_task_stop

    '更新各轴回零状态
    FOR i = 0 TO 3
        MODBUS_REG(20 + i) = axis_home(i)
    NEXT

    '更新各轴当前位置
    FOR i = 0 TO 3
        MODBUS_IEEE(30 + i) = DPOS(i)
    NEXT
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", sys_status, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num

    PRINT "=== 双Y轴滑轨状态 ==="
    PRINT "左Y轴状态：", left_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "左Y轴位置：", DPOS(1), " mm"
    PRINT "右Y轴状态：", right_slide_stat, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "右Y轴位置：", DPOS(2), " mm"

    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "右侧队列：", right_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "电批状态：", screwdriver_busy, " (0-空闲,1-忙碌)"

    PRINT "=== 任务运行状态 ==="
    PRINT "回零任务：", task_home_running, " (0-停止,1-运行)"
    PRINT "打螺丝任务：", task_screw_run, " (0-停止,1-运行)"
    PRINT "任务停止标志：", screw_task_stop, " (0-继续,1-停止)"

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", axis_home(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT
END SUB

'================ 简化回零任务（任务1执行）================
GLOBAL SUB SimpleHomeTask()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始四轴回零..."

    '简化回零流程，逐轴回零（测试版本）
    PRINT "注意：这是测试版本，将模拟回零成功"

    '轴3 (Z轴) 回零
    PRINT "开始轴3 (Z轴) 回零"
    axis_home(3) = 1
    BASE(3)
    DATUM(0) AXIS(3)
    DELAY(100)

    '检查是否有实际硬件
    DIM axis_status
    axis_status = AXISSTATUS(3)
    PRINT "轴3状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        '有硬件，执行真实回零
        DATUM(3) AXIS(3)
        WAIT IDLE(3)
        IF AXISSTATUS(3) = 0 THEN
            axis_home(3) = 2
            PRINT "轴3回零成功"
        ELSE
            axis_home(3) = 3
            PRINT "轴3回零失败，状态：", HEX(AXISSTATUS(3))
            PRINT "继续模拟回零..."
            axis_home(3) = 2
            PRINT "轴3模拟回零成功"
        ENDIF
    ELSE
        '无硬件，模拟回零成功
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(3) = 2
        PRINT "轴3模拟回零成功"
    ENDIF
    DELAY(10)

    '轴2 (Y2轴) 回零
    PRINT "开始轴2 (Y2轴) 回零"
    axis_home(2) = 1
    BASE(2)
    DATUM(0) AXIS(2)
    DELAY(10)

    axis_status = AXISSTATUS(2)
    PRINT "轴2状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(2)
        WAIT IDLE(2)
        IF AXISSTATUS(2) = 0 THEN
            axis_home(2) = 2
            PRINT "轴2回零成功"
        ELSE
            axis_home(2) = 3
            PRINT "轴2回零失败，状态：", HEX(AXISSTATUS(2))
            PRINT "继续模拟回零..."
            axis_home(2) = 2
            PRINT "轴2模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(2) = 2
        PRINT "轴2模拟回零成功"
    ENDIF
    DELAY(10)

    '轴1 (Y1轴) 回零
    PRINT "开始轴1 (Y1轴) 回零"
    axis_home(1) = 1
    BASE(1)
    DATUM(0) AXIS(1)
    DELAY(10)

    axis_status = AXISSTATUS(1)
    PRINT "轴1状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(1)
        WAIT IDLE(1)
        IF AXISSTATUS(1) = 0 THEN
            axis_home(1) = 2
            PRINT "轴1回零成功"
        ELSE
            axis_home(1) = 3
            PRINT "轴1回零失败，状态：", HEX(AXISSTATUS(1))
            PRINT "继续模拟回零..."
            axis_home(1) = 2
            PRINT "轴1模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(1) = 2
        PRINT "轴1模拟回零成功"
    ENDIF
    DELAY(10)

    '轴0 (X轴) 回零
    PRINT "开始轴0 (X轴) 回零"
    axis_home(0) = 1
    BASE(0)
    DATUM(0) AXIS(0)
    DELAY(10)

    axis_status = AXISSTATUS(0)
    PRINT "轴0状态：", HEX(axis_status)

    IF axis_status = 0 THEN
        DATUM(3) AXIS(0)
        WAIT IDLE(0)
        IF AXISSTATUS(0) = 0 THEN
            axis_home(0) = 2
            PRINT "轴0回零成功"
        ELSE
            axis_home(0) = 3
            PRINT "轴0回零失败，状态：", HEX(AXISSTATUS(0))
            PRINT "继续模拟回零..."
            axis_home(0) = 2
            PRINT "轴0模拟回零成功"
        ENDIF
    ELSE
        PRINT "检测到无硬件连接，模拟回零成功"
        axis_home(0) = 2
        PRINT "轴0模拟回零成功"
    ENDIF
    DELAY(10)

    '回零完成后，移动到初始位置
    PRINT "回零完成，移动到初始位置..."

    '双Y轴强制移动到用户位置（找零点后）
    PRINT "双Y轴强制移动到用户位置..."
    CALL ForceLeftToUser()
    CALL ForceRightToUser()

    'X轴移动到吸螺丝位置
    PRINT "X轴移动到吸螺丝位置：", pick_x, "mm"
    BASE(0)                         ' X轴
    MOVEABS(pick_x) AXIS(0)
    WAIT IDLE(0)

    'Z轴移动到吸螺丝安全高度
    PRINT "Z轴移动到吸螺丝安全高度：", pick_safe_height, "mm"
    BASE(3)                         ' Z轴
    MOVEABS(pick_safe_height) AXIS(3)
    WAIT IDLE(3)

    PRINT "所有轴回零完成，已移动到初始工作位置"
    PRINT "当前实际位置：X=", DPOS(0), "mm, Y1=", DPOS(1), "mm, Y2=", DPOS(2), "mm, Z=", DPOS(3), "mm"
    PRINT "目标用户位置：Y1=", left_user_pos, "mm, Y2=", right_user_pos, "mm"
    '任务结束，sys_status将在UpdateTaskStatus中设置为0
END SUB

'================ 简化打螺丝任务（任务2执行）================
GLOBAL SUB SimpleScrewTask()
    PRINT "打螺丝任务启动，开始监控左右两侧队列"

    '持续监控左右两侧队列
    WHILE screw_task_stop = 0
        '检查电批是否空闲
        IF screwdriver_busy = 0 THEN
            '电批空闲，检查是否有等待的任务
            IF left_queue = 1 AND left_slide_status = 1 THEN
                '左侧任务等待中且左Y轴在工作位置
                PRINT "开始执行左侧打螺丝"
                left_queue = 2          ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteLeftScrew()

            ELSEIF right_queue = 1 AND right_slide_stat = 1 THEN
                '右侧任务等待中且右Y轴在工作位置
                PRINT "开始执行右侧打螺丝"
                right_queue = 2         ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteRightScrew()
            ENDIF
        ENDIF

        DELAY(10)                      ' 10ms检查周期
    WEND

    PRINT "打螺丝任务停止"
END SUB

'================ 执行左侧打螺丝 ================
GLOBAL SUB ExecuteLeftScrew()
    PRINT "执行左侧打螺丝任务"

    '获取第一个螺丝的实际Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(left_start + 1)       ' 第一个螺丝的实际Y坐标
    PRINT "左Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：", first_screw_y, "mm"
    BASE(1)                                     ' Y1轴
    MOVEABS(first_screw_y) AXIS(1)
    WAIT IDLE(1)

    FOR screw_idx = 0 TO left_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，左侧任务中断"
            GOTO left_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)

        '执行打螺丝流程（直接在调用中计算下一个位置和最后标志）
        IF screw_idx < left_screw_num - 1 THEN
            '不是最后一个螺丝，获取下一个螺丝的Y位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 1, TABLE(left_start + (screw_idx + 1) * 3 + 1), 0)
        ELSE
            '是最后一个螺丝，移动到用户位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 1, left_user_pos, 1)
        ENDIF

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    left_task_end:
    PRINT "左侧打螺丝任务完成"

    '任务完成后的处理
    left_queue = 0                  ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '左Y轴回到用户位置
    CALL LeftSlideToUser()
    PRINT "左侧任务完成，左Y轴已回到用户位置"
END SUB

'================ 执行右侧打螺丝 ================
GLOBAL SUB ExecuteRightScrew()
    PRINT "执行右侧打螺丝任务"

    '获取第一个螺丝的实际Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(right_start + 1)      ' 第一个螺丝的实际Y坐标
    PRINT "右Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：", first_screw_y, "mm"
    BASE(2)                                     ' Y2轴
    MOVEABS(first_screw_y) AXIS(2)
    WAIT IDLE(2)

    FOR screw_idx = 0 TO right_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，右侧任务中断"
            GOTO right_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(right_start + screw_idx * 3)
        screw_y = TABLE(right_start + screw_idx * 3 + 1)
        screw_z = TABLE(right_start + screw_idx * 3 + 2)

        '执行打螺丝流程（直接在调用中计算下一个位置和最后标志）
        IF screw_idx < right_screw_num - 1 THEN
            '不是最后一个螺丝，获取下一个螺丝的Y位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 2, TABLE(right_start + (screw_idx + 1) * 3 + 1), 0)
        ELSE
            '是最后一个螺丝，移动到用户位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 2, right_user_pos, 1)
        ENDIF

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    right_task_end:
    PRINT "右侧打螺丝任务完成"

    '任务完成后的处理
    right_queue = 0                 ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '右Y轴回到用户位置
    CALL RightSlideToUser()
    PRINT "右侧任务完成，右Y轴已回到用户位置"
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis, next_y, last_flag)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 确保在取螺丝位置（统一的取螺丝位置）
    PRINT "确保在取螺丝位置"
    CALL EnsureAtPick(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 三段连续轨迹到螺丝孔位（中间不停）
    PRINT "三段连续轨迹到螺丝孔位（中间不停）"
    CALL MoveToTarget(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 三段连续轨迹回到取螺丝位置（中间不停）
    PRINT "三段连续轨迹回到取螺丝位置（中间不停）"
    CALL MoveBackToPick(y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    '7. 安全移动Y轴到下一个位置（三段插补完成后）
    PRINT "安全移动Y轴到下一个位置"
    CALL StartYMove(y_axis, next_y, last_flag)
    CALL WaitForYMove()             ' 等待Y轴移动完成

    PRINT "螺丝完成"
END SUB

'================ 确保在取螺丝位置 ================
GLOBAL SUB EnsureAtPick(y_axis)
    PRINT "确保在取螺丝位置（吸螺丝器固定位置，不控制Y轴）"

    '注意：吸螺丝器在固定位置，不需要移动Y轴
    '批头固定在Z轴上，Z轴装在X轴上，Y轴移动的是工件位置

    '检查当前XZ位置，如果不在取螺丝位置则移动过去
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)

    '如果不在取螺丝位置，使用智能轨迹移动过去
    IF ABS(current_x - pick_x) > 1 OR ABS(current_z - pick_z) > 1 THEN
        PRINT "当前位置(", current_x, ",", current_z, ")，需要移动到取螺丝位置"

        '使用标准三段轨迹
        DIM start_safe, end_safe
        start_safe = work_safe_height
        end_safe = pick_safe_height

        CALL ThreeSegMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)
    ELSE
        PRINT "已在取螺丝位置：X=", pick_x, " Z=", pick_z, "（吸螺丝器固定位置）"
    ENDIF
END SUB

'================ 智能轨迹到目标位置（中间不停）================
GLOBAL SUB MoveToTarget(target_x, target_y, target_z, y_axis)
    PRINT "智能轨迹到螺丝孔位（从取螺丝位置出发，中间不停）"

    '检查Y轴是否需要移动到目标位置
    DIM current_y
    current_y = DPOS(y_axis)
    IF ABS(current_y - target_y) > 1 THEN
        '需要移动Y轴到目标位置
        BASE(y_axis)
        MOVEABS(target_y) AXIS(y_axis)
        WAIT IDLE(y_axis)
        PRINT "Y轴移动到目标位置：", target_y, "mm"
    ELSE
        PRINT "Y轴已在目标位置：", target_y, "mm，无需移动"
    ENDIF

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = pick_safe_height
    end_safe = work_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从取螺丝位置到螺丝孔位
    PRINT "从取螺丝位置(", pick_x, ",", pick_z, ")到螺丝孔位(", target_x, ",", target_z, ")"
    CALL ThreeSegMove(pick_x, pick_z, target_x, target_z, start_safe, end_safe)

    PRINT "到达螺丝孔位：X=", target_x, " Y=", target_y, " Z=", target_z
END SUB

'================ 智能轨迹回到取螺丝位置（中间不停）================
GLOBAL SUB MoveBackToPick(y_axis)
    PRINT "智能轨迹回到取螺丝位置（从螺丝孔位出发，中间不停）"

    '注意：Y轴移动已经在StartYMove中启动，这里不再处理Y轴
    '只处理XZ轴的三段轨迹

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = work_safe_height
    end_safe = pick_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从螺丝孔位回到取螺丝位置
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)
    PRINT "从螺丝孔位(", current_x, ",", current_z, ")回到取螺丝位置(", pick_x, ",", pick_z, ")"
    CALL ThreeSegMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)

    PRINT "回到取螺丝位置：X=", pick_x, " Z=", pick_z, "（吸螺丝器固定位置）"
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '三段插补完成后已经到达可以打螺丝的位置，直接控制电批即可
    PRINT "开始打螺丝（Z轴已在正确位置：", target_z, "mm）"

    '电批锁紧
    PRINT "电批开始锁紧"
    OP(1, ON)                       ' 开启电批
    DELAY(2000)                     ' 锁紧2秒
    OP(1, OFF)                      ' 关闭电批
    PRINT "电批锁紧完成"

    PRINT "打螺丝完成，Z轴保持在工作位置"
END SUB

'================ 标准三段轨迹核心函数 ================
'标准三段轨迹：抬Z → 圆弧 → Z下降，安全高度必须大于0
GLOBAL SUB ThreeSegMove(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe)
    DIM move_dist_x, move_total
    DIM move_mid_x

    move_dist_x = end_x - start_x
    move_total = ABS(move_dist_x)

    PRINT "标准三段轨迹：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"
    PRINT "起点安全高度：", start_z_safe, "mm，终点安全高度：", end_z_safe, "mm"

    '验证安全高度必须大于0
    IF start_z_safe <= 0 OR end_z_safe <= 0 THEN
        PRINT "错误：安全高度必须大于0！起点=", start_z_safe, "，终点=", end_z_safe
        RETURN
    ENDIF

    '计算圆弧中间点
    move_mid_x = (start_x + end_x) / 2   ' X轴中点

    '执行标准三段轨迹（使用新的连续轨迹方式）
    CALL StdThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, move_mid_x, move_total)

    '不等待，让后续指令可以连续融合
    PRINT "标准三段轨迹已排入队列"
END SUB



'================ 连续轨迹预设（只在批次开始时做一次）================
GLOBAL SUB BeginContPath()
    '预设连续轨迹参数，整个批次保持连续插补
    BASE(0, 3)                      ' XZ两轴插补，X轴为主轴

    '设置VP_SPEED显示单轴速度，便于监控各轴分速度
    DIM original_zset
    original_zset = SYSTEM_ZSET     ' 保存原始设置
    SYSTEM_ZSET = original_zset AND (NOT 1)  ' 清除bit0，VP_SPEED使用单轴速度
    PRINT "设置VP_SPEED显示单轴速度，便于监控X轴和Z轴分速度"

    MERGE = ON                      ' 整个批次都保持连续插补
    CORNER_MODE = 32                ' 只倒角，不自动减速
    ZSMOOTH = 10                    ' 倒角半径放大到10mm，更平滑
    VP_MODE = 7, 7                  ' SS曲线，最平滑的曲线类型
    SRAMP = 150, 150                ' S曲线时间加长到150ms，更柔和
    FORCE_SPEED = 80                ' 统一行进速度80mm/s
    PRINT "开启连续轨迹批处理模式，整个批次无间断"
END SUB

'================ 结束连续轨迹（批次结束时调用）================
GLOBAL SUB EndContPath()
    '批次全部发送完毕后等待完成
    WAIT IDLE(0)                    ' 等待X轴完成
    WAIT IDLE(3)                    ' 等待Z轴完成
    MERGE = OFF                     ' 关闭连续插补

    '恢复VP_SPEED为默认的插补速度显示
    DIM current_zset
    current_zset = SYSTEM_ZSET
    SYSTEM_ZSET = current_zset OR 1  ' 设置bit0，恢复VP_SPEED插补速度显示
    PRINT "恢复VP_SPEED为插补速度显示"

    PRINT "连续轨迹批处理完成，整个批次平滑无间断"
END SUB

'================ 纯粹排指令的三段轨迹（调用多次，不等待）================
GLOBAL SUB PushThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    '纯粹排指令，不做任何等待或参数修改，让控制器自行融合
    PRINT "排入三段轨迹：", start_x, ",", start_z, " → ", end_x, ",", end_z
    PRINT "三段轨迹详细："
    PRINT "  第一段：垂直抬Z (", start_x, ",", start_z, ") → (", start_x, ",", start_z_safe, ")"
    PRINT "  第二段：水平移动 (", start_x, ",", start_z_safe, ") → (", end_x, ",", end_z_safe, ")"
    PRINT "  第三段：垂直下降 (", end_x, ",", end_z_safe, ") → (", end_x, ",", end_z, ")"

    '第一段：垂直抬Z到起点安全高度（现在是真正的垂直运动）
    MOVEABS(start_x, start_z_safe)          ' X不变（已经在start_x），只抬Z

    '第二段：在安全高度之间移动（圆弧或直线）
    IF std_dist >= 5 THEN
        '距离较大时使用圆弧插补，更平滑
        MOVECIRC2ABS(std_mid_x, arc_top_height, end_x, end_z_safe)
    ELSE
        '距离较小时使用直线插补
        MOVEABS(end_x, end_z_safe)
    ENDIF

    '第三段：垂直下降Z到目标位置（X不变，只降Z）
    MOVEABS(end_x, end_z)                   ' X不变（已经在end_x），只降Z

    '不做任何等待，让控制器自行与下一条轨迹融合
END SUB

'================ 速度监控设置 ================
GLOBAL SUB SetupSpeedMon()
    '设置示波器监控VP_SPEED和MSPEED，用于分析连续插补效果
    TRIGGER                         ' 自动触发示波器
    PRINT "示波器监控设置："
    PRINT "VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）"
    PRINT "VP_SPEED(3) - Z轴单轴速度（红色，刻度100，偏移-60）"
    PRINT "MSPEED(0)  - X轴分速度（绿色，刻度100，偏移-120）"
    PRINT "MSPEED(3)  - Z轴分速度（黄色，刻度100，偏移-180）"
    PRINT "连续插补成功标志：各轴速度在衔接处不降到0，整体平滑连续"
END SUB

'================ 兼容旧接口的标准三段轨迹 ================
GLOBAL SUB StdThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    '兼容旧接口，内部使用新的连续轨迹方式
    CALL BeginContPath()            ' 开始连续轨迹
    CALL PushThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    CALL EndContPath()              ' 结束连续轨迹
END SUB





'================ 备用直线移动函数 ================
GLOBAL SUB LinearXZMove(start_x, start_z, end_x, end_z)
    PRINT "直线XZ移动：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"

    BASE(0, 3)
    MOVEABS(end_x) AXIS(0)
    MOVEABS(end_z) AXIS(3)
    WAIT IDLE(0)
    WAIT IDLE(3)

    PRINT "直线XZ移动完成"
END SUB


