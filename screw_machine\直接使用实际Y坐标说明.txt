===============================================================================
                        直接使用实际Y坐标说明 - 不再依赖work_pos变量
===============================================================================

【改进概述】

=== 改进目标 ===
```
原来：通过left_work_pos和right_work_pos变量间接设置Y坐标
现在：直接在TABLE数组中使用螺丝的实际Y坐标值

优势：
✅ 更直观：直接看到每个螺丝的实际Y坐标
✅ 更灵活：每个螺丝可以有不同的Y坐标
✅ 更准确：避免通过中间变量的间接计算
✅ 更易维护：修改螺丝位置时直接修改对应的坐标值
```

=== 改进前后对比 ===
```
改进前：
left_work_pos = 80
TABLE(1) = left_work_pos        ' 螺丝1：Y=工作位置
TABLE(4) = left_work_pos        ' 螺丝2：Y=工作位置
TABLE(13) = left_work_pos + 40  ' 螺丝5：Y=工作位置+40

改进后：
TABLE(1) = 80                   ' 螺丝1：Y=80mm（实际Y坐标）
TABLE(4) = 80                   ' 螺丝2：Y=80mm
TABLE(13) = 120                 ' 螺丝5：Y=120mm（实际Y坐标）
```

【技术实现】

=== 左侧螺丝位置数据 ===
```basic
'左侧螺丝位置数据(2x4阵列，8个螺丝) - 直接使用实际Y坐标
'第一行螺丝（Y = 80mm）
TABLE(0) = 100                  ' 螺丝1：X=100
TABLE(1) = 80                   ' 螺丝1：Y=80mm（第一个螺丝的实际Y坐标）
TABLE(2) = screw_work_height    ' 螺丝1：Z=30（打螺丝高度）
TABLE(3) = 150                  ' 螺丝2：X=150
TABLE(4) = 80                   ' 螺丝2：Y=80mm
TABLE(5) = screw_work_height    ' 螺丝2：Z=30（打螺丝高度）
...

'第二行螺丝（Y = 120mm）
TABLE(12) = 100                 ' 螺丝5：X=100
TABLE(13) = 120                 ' 螺丝5：Y=120mm（第二行螺丝的实际Y坐标）
TABLE(14) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
...
```

=== 右侧螺丝位置数据 ===
```basic
'右侧螺丝位置数据(2x4阵列，8个螺丝) - 直接使用实际Y坐标
'第一行螺丝（Y = 220mm）
TABLE(200) = 100                ' 螺丝1：X=100
TABLE(201) = 220                 ' 螺丝1：Y=220mm（第一个螺丝的实际Y坐标）
TABLE(202) = screw_work_height   ' 螺丝1：Z=30（打螺丝高度）
TABLE(203) = 150                ' 螺丝2：X=150
TABLE(204) = 220                 ' 螺丝2：Y=220mm
TABLE(205) = screw_work_height   ' 螺丝2：Z=30（打螺丝高度）
...

'第二行螺丝（Y = 260mm）
TABLE(212) = 100                ' 螺丝5：X=100
TABLE(213) = 260                 ' 螺丝5：Y=260mm（第二行螺丝的实际Y坐标）
TABLE(214) = screw_work_height   ' 螺丝5：Z=30（打螺丝高度）
...
```

=== Y轴移动逻辑 ===
```basic
任务开始时直接移动到第一个螺丝的实际Y坐标：

左侧任务：
first_screw_y = TABLE(left_start + 1)       ' 获取TABLE(1) = 80mm
MOVEABS(first_screw_y) AXIS(1)              ' 移动到80mm

右侧任务：
first_screw_y = TABLE(right_start + 1)      ' 获取TABLE(201) = 220mm
MOVEABS(first_screw_y) AXIS(2)              ' 移动到220mm
```

【坐标系统】

=== 左侧螺丝布局 ===
```
左侧工件布局（2行4列）：
第一行（Y=80mm）：  螺丝1(100,80)  螺丝2(150,80)  螺丝3(200,80)  螺丝4(250,80)
第二行（Y=120mm）： 螺丝5(100,120) 螺丝6(150,120) 螺丝7(200,120) 螺丝8(250,120)

用户位置：Y=50mm（用户侧，便于放置和取走工件）
```

=== 右侧螺丝布局 ===
```
右侧工件布局（2行4列）：
第一行（Y=220mm）： 螺丝1(100,220) 螺丝2(150,220) 螺丝3(200,220) 螺丝4(250,220)
第二行（Y=260mm）： 螺丝5(100,260) 螺丝6(150,260) 螺丝7(200,260) 螺丝8(250,260)

用户位置：Y=50mm（用户侧，便于放置和取走工件）
```

=== 坐标系说明 ===
```
X轴：0-300mm（螺丝机工作范围）
Y轴：0-300mm（双Y轴滑轨范围）
Z轴：0-50mm（往下为正，0为机械原点）

关键位置：
- 取螺丝位置：(50, 150, 10)
- 左侧用户位置：Y=50mm
- 右侧用户位置：Y=50mm
- 左侧第一个螺丝：Y=80mm
- 右侧第一个螺丝：Y=220mm
```

【显示信息】

=== 数据设置完成时的显示 ===
```
"数据设置完成（直接使用实际Y坐标）"
"左侧螺丝数量：1（2x4阵列）"
"右侧螺丝数量：1（2x4阵列）"
"最大支持螺丝数量：64个/侧"
"左侧第一个螺丝Y坐标：80mm，第二行Y坐标：120mm"
"右侧第一个螺丝Y坐标：220mm，第二行Y坐标：260mm"
"左侧用户位置：50mm，右侧用户位置：50mm"
```

=== 任务开始时的显示 ===
```
左侧任务：
"开始左侧打螺丝任务，螺丝数量：1"
"左Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：80mm"
"执行左侧打螺丝任务"

右侧任务：
"开始右侧打螺丝任务，螺丝数量：1"
"右Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：220mm"
"执行右侧打螺丝任务"
```

【配置和调整】

=== 修改螺丝位置 ===
```basic
如果需要修改螺丝位置，直接修改TABLE数组中的对应值：

例如，修改左侧第一个螺丝的Y坐标从80mm改为85mm：
TABLE(1) = 85                   ' 螺丝1：Y=85mm

例如，修改右侧第二行螺丝的Y坐标从260mm改为270mm：
TABLE(213) = 270                ' 螺丝5：Y=270mm
TABLE(216) = 270                ' 螺丝6：Y=270mm
TABLE(219) = 270                ' 螺丝7：Y=270mm
TABLE(222) = 270                ' 螺丝8：Y=270mm
```

=== 添加新的螺丝行 ===
```basic
如果需要添加第三行螺丝（例如Y=160mm）：

左侧第三行：
TABLE(24) = 100                 ' 螺丝9：X=100
TABLE(25) = 160                 ' 螺丝9：Y=160mm
TABLE(26) = screw_work_height   ' 螺丝9：Z=30
TABLE(27) = 150                 ' 螺丝10：X=150
TABLE(28) = 160                 ' 螺丝10：Y=160mm
TABLE(29) = screw_work_height   ' 螺丝10：Z=30
...

同时需要调整螺丝数量：
CALL SetScrewCount(12, 12)      ' 设置为3行4列=12个螺丝
```

=== 不同工件的适配 ===
```basic
对于不同尺寸的工件，只需修改对应的Y坐标：

小工件（行间距30mm）：
第一行：Y=80mm
第二行：Y=110mm

大工件（行间距50mm）：
第一行：Y=80mm
第二行：Y=130mm

特殊布局（不规则间距）：
第一行：Y=75mm
第二行：Y=125mm
第三行：Y=180mm
```

【优势分析】

=== 直观性提升 ===
```
✅ 坐标一目了然：直接看到每个螺丝的实际Y坐标
✅ 布局清晰：容易理解螺丝的空间分布
✅ 修改简单：需要调整位置时直接修改对应数值
✅ 验证容易：可以直接对比实际测量值
```

=== 灵活性提升 ===
```
✅ 个性化定制：每个螺丝可以有独特的Y坐标
✅ 不规则布局：支持非等间距的螺丝布局
✅ 多种工件：同一套代码适配不同工件
✅ 快速调整：修改工件布局时无需重新计算
```

=== 可维护性提升 ===
```
✅ 减少变量：不再需要work_pos等中间变量
✅ 减少计算：避免运行时的坐标计算
✅ 减少错误：直接使用实际值，避免计算错误
✅ 便于调试：问题定位更直接
```

【兼容性说明】

=== 保留的变量 ===
```basic
以下变量仍然保留，用于其他功能：
GLOBAL left_user_pos = 50       ' 左侧用户位置
GLOBAL right_user_pos = 50      ' 右侧用户位置
GLOBAL left_work_pos = 80       ' 保留，但不再用于螺丝位置计算
GLOBAL right_work_pos = 80      ' 保留，但不再用于螺丝位置计算

注意：work_pos变量保留是为了兼容其他可能使用这些变量的函数
```

=== 功能不变 ===
```
✅ Y轴移动逻辑：完全不变，只是数据来源改为直接读取
✅ 并行运动优化：完全不变，所有优化功能保持
✅ 安全移动方案：完全不变，安全性保持
✅ 三段插补：完全不变，运动精度保持
```

【测试验证】

=== 验证要点 ===
```
1. 数据正确性：
   - 确认TABLE数组中的Y坐标值正确
   - 验证左右两侧的坐标设置

2. 移动准确性：
   - 测试Y轴是否移动到正确的实际坐标
   - 验证第一个螺丝的Y坐标是否准确

3. 多螺丝测试：
   - 如果设置多个螺丝，验证不同行的Y坐标
   - 确认Y轴在不同行之间的移动

4. 显示信息：
   - 检查启动时显示的Y坐标值
   - 确认显示的坐标与实际移动一致
```

=== 测试方法 ===
```basic
1. 查看数据设置：
   运行程序，观察数据设置完成时的显示信息

2. 测试Y轴移动：
   按下IN0或IN1，观察Y轴移动到的实际位置

3. 手动验证：
   用尺子测量Y轴的实际位置，对比显示的坐标值

4. 多螺丝测试：
   设置多个螺丝：CALL SetScrewCount(8, 8)
   观察Y轴在不同螺丝间的移动
```

【总结】

直接使用实际Y坐标的改进特点：
✅ **更直观**：直接看到每个螺丝的实际Y坐标
✅ **更灵活**：支持任意的螺丝布局和间距
✅ **更准确**：避免中间变量的计算误差
✅ **更易维护**：修改位置时直接修改对应数值
✅ **完全兼容**：所有现有功能保持不变
✅ **便于调试**：问题定位更直接简单

这个改进让螺丝机的位置配置更加直观和灵活，
为不同工件的适配提供了更好的支持。

===============================================================================
