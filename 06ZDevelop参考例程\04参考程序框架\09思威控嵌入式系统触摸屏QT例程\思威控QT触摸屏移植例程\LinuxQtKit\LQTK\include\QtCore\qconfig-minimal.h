/****************************************************************************
**
** Copyright (C) 2015 The Qt Company Ltd.
** Contact: http://www.qt.io/licensing/
**
** This file is part of the QtCore module of the Qt Toolkit.
**
** $QT_BEGIN_LICENSE:LGPL21$
** Commercial License Usage
** Licensees holding valid commercial Qt licenses may use this file in
** accordance with the commercial license agreement provided with the
** Software or, alternatively, in accordance with the terms contained in
** a written agreement between you and The Qt Company. For licensing terms
** and conditions see http://www.qt.io/terms-conditions. For further
** information use the contact form at http://www.qt.io/contact-us.
**
** GNU Lesser General Public License Usage
** Alternatively, this file may be used under the terms of the GNU Lesser
** General Public License version 2.1 or version 3 as published by the Free
** Software Foundation and appearing in the file LICENSE.LGPLv21 and
** LICENSE.LGPLv3 included in the packaging of this file. Please review the
** following information to ensure the GNU Lesser General Public License
** requirements will be met: https://www.gnu.org/licenses/lgpl.html and
** http://www.gnu.org/licenses/old-licenses/lgpl-2.1.html.
**
** As a special exception, The Qt Company gives you certain additional
** rights. These rights are described in The Qt Company LGPL Exception
** version 1.1, included in the file LGPL_EXCEPTION.txt in this package.
**
** $QT_END_LICENSE$
**
****************************************************************************/

/* Data structures */
#ifndef QT_NO_TEXTDATE
#  define QT_NO_TEXTDATE
#endif
#ifndef QT_NO_DATESTRING
#  define QT_NO_DATESTRING
#endif

/* Dialogs */
#ifndef QT_NO_COLORDIALOG
#  define QT_NO_COLORDIALOG
#endif
#ifndef QT_NO_ERRORMESSAGE
#  define QT_NO_ERRORMESSAGE
#endif
#ifndef QT_NO_FILEDIALOG
#  define QT_NO_FILEDIALOG
#endif
#ifndef QT_NO_FONTDIALOG
#  define QT_NO_FONTDIALOG
#endif
#ifndef QT_NO_INPUTDIALOG
#  define QT_NO_INPUTDIALOG
#endif
#ifndef QT_NO_MESSAGEBOX
#  define QT_NO_MESSAGEBOX
#endif
#ifndef QT_NO_PRINTDIALOG
#  define QT_NO_PRINTDIALOG
#endif
#ifndef QT_NO_PRINTPREVIEWDIALOG
#  define QT_NO_PRINTPREVIEWDIALOG
#endif
#ifndef QT_NO_PROGRESSDIALOG
#  define QT_NO_PROGRESSDIALOG
#endif
#ifndef QT_NO_WIZARD
#  define QT_NO_WIZARD
#endif

/* File I/O */
#ifndef QT_NO_DOM
#  define QT_NO_DOM
#endif
#ifndef QT_NO_FILESYSTEMWATCHER
#  define QT_NO_FILESYSTEMWATCHER
#endif
#ifndef QT_NO_FILESYSTEMMODEL
#  define QT_NO_FILESYSTEMMODEL
#endif
#ifndef QT_NO_PROCESS
#  define QT_NO_PROCESS
#endif
#ifndef QT_NO_TEMPORARYFILE
#  define QT_NO_TEMPORARYFILE
#endif
#ifndef QT_NO_SETTINGS
#  define QT_NO_SETTINGS
#endif
#ifndef QT_NO_LIBRARY
#  define QT_NO_LIBRARY
#endif

/* Fonts */
#ifndef QT_NO_FREETYPE
#  define QT_NO_FREETYPE
#endif

/* Images */
#ifndef QT_NO_IMAGEFORMATPLUGIN
#  define QT_NO_IMAGEFORMATPLUGIN
#endif
#ifndef QT_NO_IMAGEFORMAT_BMP
#  define QT_NO_IMAGEFORMAT_BMP
#endif
#ifndef QT_NO_IMAGEFORMAT_JPEG
#  define QT_NO_IMAGEFORMAT_JPEG
#endif
#ifndef QT_NO_IMAGEFORMAT_PNG
#  define QT_NO_IMAGEFORMAT_PNG
#endif
#ifndef QT_NO_IMAGEFORMAT_PPM
#  define QT_NO_IMAGEFORMAT_PPM
#endif
#ifndef QT_NO_IMAGEFORMAT_XBM
#  define QT_NO_IMAGEFORMAT_XBM
#endif
#ifndef QT_NO_IMAGEFORMAT_XPM
#  define QT_NO_IMAGEFORMAT_XPM
#endif
#ifndef QT_NO_IMAGE_HEURISTIC_MASK
#  define QT_NO_IMAGE_HEURISTIC_MASK
#endif
#ifndef QT_NO_MOVIE
#  define QT_NO_MOVIE
#endif

/* Internationalization */
#ifndef QT_NO_BIG_CODECS
#  define QT_NO_BIG_CODECS
#endif
#ifndef QT_NO_TEXTCODEC
#  define QT_NO_TEXTCODEC
#endif
#ifndef QT_NO_CODECS
#  define QT_NO_CODECS
#endif
#ifndef QT_NO_TRANSLATION
#  define QT_NO_TRANSLATION
#endif

/* ItemViews */
#ifndef QT_NO_ITEMVIEWS
#  define QT_NO_ITEMVIEWS
#endif
#ifndef QT_NO_DATAWIDGETMAPPER
#  define QT_NO_DATAWIDGETMAPPER
#endif
#ifndef QT_NO_DIRMODEL
#  define QT_NO_DIRMODEL
#endif
#ifndef QT_NO_LISTVIEW
#  define QT_NO_LISTVIEW
#endif
#ifndef QT_NO_COLUMNVIEW
#  define QT_NO_COLUMNVIEW
#endif
#ifndef QT_NO_PROXYMODEL
#  define QT_NO_PROXYMODEL
#endif
#ifndef QT_NO_SORTFILTERPROXYMODEL
#  define QT_NO_SORTFILTERPROXYMODEL
#endif
#ifndef QT_NO_STANDARDITEMMODEL
#  define QT_NO_STANDARDITEMMODEL
#endif
#ifndef QT_NO_STRINGLISTMODEL
#  define QT_NO_STRINGLISTMODEL
#endif
#ifndef QT_NO_TABLEVIEW
#  define QT_NO_TABLEVIEW
#endif
#ifndef QT_NO_TREEVIEW
#  define QT_NO_TREEVIEW
#endif

/* Kernel */
#ifndef QT_NO_ACTION
#  define QT_NO_ACTION
#endif
#ifndef QT_NO_CLIPBOARD
#  define QT_NO_CLIPBOARD
#endif
#ifndef QT_NO_CSSPARSER
#  define QT_NO_CSSPARSER
#endif
#ifndef QT_NO_CURSOR
#  define QT_NO_CURSOR
#endif
#ifndef QT_NO_DRAGANDDROP
#  define QT_NO_DRAGANDDROP
#endif
#ifndef QT_NO_EFFECTS
#  define QT_NO_EFFECTS
#endif
#ifndef QT_NO_PROPERTIES
#  define QT_NO_PROPERTIES
#endif
#ifndef QT_NO_SESSIONMANAGER
#  define QT_NO_SESSIONMANAGER
#endif
#ifndef QT_NO_SHAREDMEMORY
#  define QT_NO_SHAREDMEMORY
#endif
#ifndef QT_NO_SHORTCUT
#  define QT_NO_SHORTCUT
#endif
#ifndef QT_NO_SYSTEMSEMAPHORE
#  define QT_NO_SYSTEMSEMAPHORE
#endif
#ifndef QT_NO_TABLETEVENT
#  define QT_NO_TABLETEVENT
#endif
#ifndef QT_NO_TEXTHTMLPARSER
#  define QT_NO_TEXTHTMLPARSER
#endif
#ifndef QT_NO_CONCURRENT
#  define QT_NO_CONCURRENT
#endif
#ifndef QT_NO_WHEELEVENT
#  define QT_NO_WHEELEVENT
#endif
#ifndef QT_NO_XMLSTREAM
#  define QT_NO_XMLSTREAM
#endif
#ifndef QT_NO_XMLSTREAMREADER
#  define QT_NO_XMLSTREAMREADER
#endif
#ifndef QT_NO_XMLSTREAMWRITER
#  define QT_NO_XMLSTREAMWRITER
#endif

/* Networking */
#ifndef QT_NO_HTTP
#  define QT_NO_HTTP
#endif
#ifndef QT_NO_NETWORKPROXY
#  define QT_NO_NETWORKPROXY
#endif
#ifndef QT_NO_SOCKS5
#  define QT_NO_SOCKS5
#endif
#ifndef QT_NO_UDPSOCKET
#  define QT_NO_UDPSOCKET
#endif
#ifndef QT_NO_FTP
#  define QT_NO_FTP
#endif

/* Painting */
#ifndef QT_NO_COLORNAMES
#  define QT_NO_COLORNAMES
#endif
#ifndef QT_NO_PAINT_DEBUG
#  define QT_NO_PAINT_DEBUG
#endif
#ifndef QT_NO_PICTURE
#  define QT_NO_PICTURE
#endif
#ifndef QT_NO_PRINTER
#  define QT_NO_PRINTER
#endif
#ifndef QT_NO_CUPS
#  define QT_NO_CUPS
#endif

/* Styles */
#ifndef QT_NO_STYLE_FUSION
#  define QT_NO_STYLE_FUSION
#endif
#ifndef QT_NO_STYLE_STYLESHEET
#  define QT_NO_STYLE_STYLESHEET
#endif
#ifndef QT_NO_STYLE_WINDOWSCE
#  define QT_NO_STYLE_WINDOWSCE
#endif
#ifndef QT_NO_STYLE_WINDOWSMOBILE
#  define QT_NO_STYLE_WINDOWSMOBILE
#endif
#ifndef QT_NO_STYLE_WINDOWSVISTA
#  define QT_NO_STYLE_WINDOWSVISTA
#endif
#ifndef QT_NO_STYLE_WINDOWSXP
#  define QT_NO_STYLE_WINDOWSXP
#endif

/* Utilities */
#ifndef QT_NO_ACCESSIBILITY
#  define QT_NO_ACCESSIBILITY
#endif
#ifndef QT_NO_COMPLETER
#  define QT_NO_COMPLETER
#endif
#ifndef QT_NO_DESKTOPSERVICES
#  define QT_NO_DESKTOPSERVICES
#endif
#ifndef QT_NO_MIMETYPE
#  define QT_NO_MIMETYPE
#endif
#ifndef QT_NO_SYSTEMTRAYICON
#  define QT_NO_SYSTEMTRAYICON
#endif
#ifndef QT_NO_UNDOCOMMAND
#  define QT_NO_UNDOCOMMAND
#endif
#ifndef QT_NO_UNDOGROUP
#  define QT_NO_UNDOGROUP
#endif
#ifndef QT_NO_UNDOSTACK
#  define QT_NO_UNDOSTACK
#endif
#ifndef QT_NO_UNDOVIEW
#  define QT_NO_UNDOVIEW
#endif
#ifndef QT_NO_GESTURES
#  define QT_NO_GESTURES
#endif

/* Widgets */
#ifndef QT_NO_GROUPBOX
#  define QT_NO_GROUPBOX
#endif
#ifndef QT_NO_BUTTONGROUP
#  define QT_NO_BUTTONGROUP
#endif
#ifndef QT_NO_LCDNUMBER
#  define QT_NO_LCDNUMBER
#endif
#ifndef QT_NO_LINEEDIT
#  define QT_NO_LINEEDIT
#endif
#ifndef QT_NO_COMBOBOX
#  define QT_NO_COMBOBOX
#endif
#ifndef QT_NO_FONTCOMBOBOX
#  define QT_NO_FONTCOMBOBOX
#endif
#ifndef QT_NO_SPINBOX
#  define QT_NO_SPINBOX
#endif
#ifndef QT_NO_CALENDARWIDGET
#  define QT_NO_CALENDARWIDGET
#endif
#ifndef QT_NO_DATETIMEEDIT
#  define QT_NO_DATETIMEEDIT
#endif
#ifndef QT_NO_LISTWIDGET
#  define QT_NO_LISTWIDGET
#endif
#ifndef QT_NO_MENU
#  define QT_NO_MENU
#endif
#ifndef QT_NO_CONTEXTMENU
#  define QT_NO_CONTEXTMENU
#endif
#ifndef QT_NO_MAINWINDOW
#  define QT_NO_MAINWINDOW
#endif
#ifndef QT_NO_DOCKWIDGET
#  define QT_NO_DOCKWIDGET
#endif
#ifndef QT_NO_TOOLBAR
#  define QT_NO_TOOLBAR
#endif
#ifndef QT_NO_MENUBAR
#  define QT_NO_MENUBAR
#endif
#ifndef QT_NO_PROGRESSBAR
#  define QT_NO_PROGRESSBAR
#endif
#ifndef QT_NO_RESIZEHANDLER
#  define QT_NO_RESIZEHANDLER
#endif
#ifndef QT_NO_RUBBERBAND
#  define QT_NO_RUBBERBAND
#endif
#ifndef QT_NO_SPLITTER
#  define QT_NO_SPLITTER
#endif
#ifndef QT_NO_SIZEGRIP
#  define QT_NO_SIZEGRIP
#endif
#ifndef QT_NO_SLIDER
#  define QT_NO_SLIDER
#endif
#ifndef QT_NO_DIAL
#  define QT_NO_DIAL
#endif
#ifndef QT_NO_SCROLLBAR
#  define QT_NO_SCROLLBAR
#endif
#ifndef QT_NO_SCROLLAREA
#  define QT_NO_SCROLLAREA
#endif
#ifndef QT_NO_GRAPHICSVIEW
#  define QT_NO_GRAPHICSVIEW
#endif
#ifndef QT_NO_PRINTPREVIEWWIDGET
#  define QT_NO_PRINTPREVIEWWIDGET
#endif
#ifndef QT_NO_MDIAREA
#  define QT_NO_MDIAREA
#endif
#ifndef QT_NO_TEXTEDIT
#  define QT_NO_TEXTEDIT
#endif
#ifndef QT_NO_SYNTAXHIGHLIGHTER
#  define QT_NO_SYNTAXHIGHLIGHTER
#endif
#ifndef QT_NO_TEXTBROWSER
#  define QT_NO_TEXTBROWSER
#endif
#ifndef QT_NO_SPINWIDGET
#  define QT_NO_SPINWIDGET
#endif
#ifndef QT_NO_SPLASHSCREEN
#  define QT_NO_SPLASHSCREEN
#endif
#ifndef QT_NO_STACKEDWIDGET
#  define QT_NO_STACKEDWIDGET
#endif
#ifndef QT_NO_TABWIDGET
#  define QT_NO_TABWIDGET
#endif
#ifndef QT_NO_STATUSBAR
#  define QT_NO_STATUSBAR
#endif
#ifndef QT_NO_STATUSTIP
#  define QT_NO_STATUSTIP
#endif
#ifndef QT_NO_TABLEWIDGET
#  define QT_NO_TABLEWIDGET
#endif
#ifndef QT_NO_TOOLBUTTON
#  define QT_NO_TOOLBUTTON
#endif
#ifndef QT_NO_TABBAR
#  define QT_NO_TABBAR
#endif
#ifndef QT_NO_TOOLBOX
#  define QT_NO_TOOLBOX
#endif
#ifndef QT_NO_WHATSTHIS
#  define QT_NO_WHATSTHIS
#endif
#ifndef QT_NO_TOOLTIP
#  define QT_NO_TOOLTIP
#endif
#ifndef QT_NO_TREEWIDGET
#  define QT_NO_TREEWIDGET
#endif
#ifndef QT_NO_VALIDATOR
#  define QT_NO_VALIDATOR
#endif
