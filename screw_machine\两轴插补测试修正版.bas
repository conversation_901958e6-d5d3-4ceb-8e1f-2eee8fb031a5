'===============================================================================
'                        两轴插补测试程序 - RTBasic语法修正版
'                        根据官方手册5.2插补运动章节编写
'===============================================================================

'================ 全局变量定义 ================
GLOBAL test_mode                        ' 测试模式选择
GLOBAL original_zset                     ' 保存原始SYSTEM_ZSET设置
GLOBAL dummy                             ' 临时变量

'================ 系统初始化 ================
GLOBAL SUB SystemInit()
    PRINT "==============================================================================="
    PRINT "                        两轴插补测试程序初始化"
    PRINT "==============================================================================="
    
    '设置VP_SPEED显示单轴速度，便于监控
    original_zset = SYSTEM_ZSET
    SYSTEM_ZSET = original_zset AND (NOT 1)  ' 清除bit0，VP_SPEED使用单轴速度
    PRINT "设置VP_SPEED显示单轴速度，便于监控各轴分速度"
    
    '设置基础轴组：X轴(0)和Y轴(1)
    BASE(0, 1)                          ' X轴为主轴，Y轴为从轴
    PRINT "设置轴组：BASE(0, 1) - X轴主轴，Y轴从轴"
    
    '清零坐标
    DPOS = 0, 0                         ' 坐标清零
    PRINT "坐标清零：DPOS = 0, 0"
    
    '设置脉冲当量
    UNITS = 100, 100                    ' 脉冲当量100
    PRINT "脉冲当量：UNITS = 100, 100"
    
    '设置运动参数
    SPEED = 100, 100                    ' 主轴速度100units/s
    ACCEL = 1000, 1000                  ' 加速度1000units/s²
    DECEL = 1000, 1000                  ' 减速度1000units/s²
    PRINT "运动参数：SPEED=100, ACCEL=1000, DECEL=1000"
    
    '设置平滑参数
    SRAMP = 50, 50                      ' S曲线时间50ms
    VP_MODE = 7, 7                      ' SS曲线，最平滑
    PRINT "平滑参数：SRAMP=50, VP_MODE=7"
    
    PRINT "系统初始化完成"
    PRINT ""
END SUB

'================ 恢复系统设置 ================
GLOBAL SUB SystemRestore()
    '恢复VP_SPEED为默认设置
    SYSTEM_ZSET = original_zset OR 1    ' 设置bit0，恢复插补速度显示
    PRINT "恢复VP_SPEED为插补速度显示"
    PRINT "系统设置已恢复"
END SUB

'================ 示波器监控设置 ================
GLOBAL SUB SetupScope()
    TRIGGER                             ' 自动触发示波器
    PRINT "示波器监控设置："
    PRINT "VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）"
    PRINT "VP_SPEED(1) - Y轴单轴速度（红色，刻度100，偏移-60）"
    PRINT "MSPEED(0)  - X轴分速度（绿色，刻度100，偏移-120）"
    PRINT "MSPEED(1)  - Y轴分速度（黄色，刻度100，偏移-180）"
    PRINT "观察要点：插补运动时各轴速度协调变化，连续插补时无中间停顿"
    PRINT ""
END SUB

'================ 测试1：两轴直线插补 ================
GLOBAL SUB Test1_Linear()
    PRINT "==============================================================================="
    PRINT "                        测试1：两轴直线插补"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.2.4节：二轴直线插补"
    PRINT "从A点(0,0)运动到B点(100,100)"
    PRINT "插补合成运动距离：√(100²+100²) = 141.42 units"
    PRINT "X轴实际速度：100×100/141.42 = 70.71 units/s"
    PRINT "Y轴实际速度：100×100/141.42 = 70.71 units/s"
    PRINT ""
    
    '回到原点
    DPOS = 0, 0
    PRINT "当前位置：(0, 0)"
    
    '设置插补速度
    FORCE_SPEED = 100                   ' 插补主轴速度100units/s
    PRINT "设置插补速度：FORCE_SPEED = 100"
    
    '执行直线插补
    PRINT "开始执行直线插补：MOVE(100, 100)"
    MOVE(100, 100)                      ' 两轴各运动100units
    
    '等待运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "直线插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "测试1完成"
    PRINT ""
END SUB

'================ 测试2：两轴圆弧插补 ================
GLOBAL SUB Test2_Circular()
    PRINT "==============================================================================="
    PRINT "                        测试2：两轴圆弧插补"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.2.2节：圆弧插补原理"
    PRINT "在XY平面第一象限走一段逆时针圆弧"
    PRINT "从点(100,100)经过中间点(50,150)到终点(0,100)"
    PRINT ""
    
    '确保起始位置
    MOVEABS(100, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(100, 100)"
    
    '设置插补速度
    FORCE_SPEED = 80                    ' 圆弧插补速度80units/s
    PRINT "设置插补速度：FORCE_SPEED = 80"
    
    '执行圆弧插补（三点圆弧）
    PRINT "开始执行圆弧插补：MOVECIRC2ABS(50, 150, 0, 100)"
    MOVECIRC2ABS(50, 150, 0, 100)      ' 三点圆弧插补
    
    '等待运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "圆弧插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "测试2完成"
    PRINT ""
END SUB

'================ 测试3：非连续插补 ================
GLOBAL SUB Test3_NonContinuous()
    PRINT "==============================================================================="
    PRINT "                        测试3：非连续插补（MERGE=OFF）"
    PRINT "==============================================================================="
    PRINT "根据官方手册连续插补章节：不开启MERGE的情况"
    PRINT "执行四段直线插补组成矩形轨迹，观察每段之间的停顿"
    PRINT ""
    
    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"
    
    '确保MERGE关闭
    MERGE = OFF
    CORNER_MODE = 0                     ' 关闭前瞻功能
    FORCE_SPEED = 100
    PRINT "设置：MERGE=OFF, CORNER_MODE=0, FORCE_SPEED=100"
    PRINT "开始执行矩形轨迹（四段直线插补）"
    
    '第一段：(0,0) → (100,0)
    PRINT "第一段：(0,0) → (100,0)"
    MOVE(100, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '第二段：(100,0) → (100,100)
    PRINT "第二段：(100,0) → (100,100)"
    MOVE(0, 100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '第三段：(100,100) → (0,100)
    PRINT "第三段：(100,100) → (0,100)"
    MOVE(-100, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '第四段：(0,100) → (0,0)
    PRINT "第四段：(0,100) → (0,0)"
    MOVE(0, -100)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    PRINT "非连续插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：每段之间都有明显停顿，速度曲线有断点"
    PRINT "测试3完成"
    PRINT ""
END SUB

'================ 测试4：连续插补 ================
GLOBAL SUB Test4_Continuous()
    PRINT "==============================================================================="
    PRINT "                        测试4：连续插补（MERGE=ON）"
    PRINT "==============================================================================="
    PRINT "根据官方手册连续插补章节：开启MERGE的情况"
    PRINT "执行四段直线插补组成矩形轨迹，观察连续性"
    PRINT ""
    
    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"
    
    '开启连续插补
    MERGE = ON                          ' 开启连续插补
    CORNER_MODE = 0                     ' 暂不使用前瞻功能
    FORCE_SPEED = 100
    PRINT "设置：MERGE=ON, CORNER_MODE=0, FORCE_SPEED=100"
    PRINT "开始执行矩形轨迹（四段连续插补）"
    
    '连续发送四段插补指令
    PRINT "连续发送四段插补指令："
    PRINT "第一段：(0,0) → (100,0)"
    MOVE(100, 0)
    
    PRINT "第二段：(100,0) → (100,100)"
    MOVE(0, 100)
    
    PRINT "第三段：(100,100) → (0,100)"
    MOVE(-100, 0)
    
    PRINT "第四段：(0,100) → (0,0)"
    MOVE(0, -100)
    
    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '关闭连续插补
    MERGE = OFF
    
    PRINT "连续插补完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：各段之间连续，但拐角处仍有冲击"
    PRINT "测试4完成"
    PRINT ""
END SUB

'================ 测试5：前瞻拐角减速 ================
GLOBAL SUB Test5_CornerDecel()
    PRINT "==============================================================================="
    PRINT "                        测试5：前瞻拐角减速（CORNER_MODE=2）"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.3节：前瞻预处理"
    PRINT "开启连续插补+自动拐角减速，拐角处按比例减速"
    PRINT ""

    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"

    '设置前瞻拐角减速
    MERGE = ON                          ' 开启连续插补
    CORNER_MODE = 2                     ' 自动拐角减速
    DECEL_ANGLE = 30 * (PI/180)         ' 开始减速角度30度
    STOP_ANGLE = 90 * (PI/180)          ' 停止减速角度90度
    FORCE_SPEED = 100                   ' 参考速度100units/s
    PRINT "设置：MERGE=ON, CORNER_MODE=2"
    PRINT "DECEL_ANGLE=30度, STOP_ANGLE=90度, FORCE_SPEED=100"
    PRINT "开始执行矩形轨迹（前瞻拐角减速）"

    '连续发送四段插补指令
    PRINT "连续发送四段插补指令："
    MOVE(100, 0)                        ' 第一段
    MOVE(0, 100)                        ' 第二段
    MOVE(-100, 0)                       ' 第三段
    MOVE(0, -100)                       ' 第四段

    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)

    '关闭设置
    MERGE = OFF
    CORNER_MODE = 0

    PRINT "前瞻拐角减速完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：拐角处按比例减速，既保证连续性又减少冲击"
    PRINT "测试5完成"
    PRINT ""
END SUB

'================ 测试6：自动倒角 ================
GLOBAL SUB Test6_AutoChamfer()
    PRINT "==============================================================================="
    PRINT "                        测试6：自动倒角（CORNER_MODE=32）"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.3节：CORNER_MODE=32自动倒角"
    PRINT "改变运动轨迹，拐角处自动倒角，不降低速度"
    PRINT ""

    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"

    '设置自动倒角
    MERGE = ON                          ' 开启连续插补
    CORNER_MODE = 32                    ' 自动倒角设置
    ZSMOOTH = 10                        ' 倒角半径10units
    FORCE_SPEED = 100                   ' 运动速度100units/s
    PRINT "设置：MERGE=ON, CORNER_MODE=32"
    PRINT "ZSMOOTH=10, FORCE_SPEED=100"
    PRINT "开始执行矩形轨迹（自动倒角）"

    '连续发送四段插补指令
    PRINT "连续发送四段插补指令："
    MOVE(100, 0)                        ' 第一段
    MOVE(0, 100)                        ' 第二段
    MOVE(-100, 0)                       ' 第三段
    MOVE(0, -100)                       ' 第四段

    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)

    '关闭设置
    MERGE = OFF
    CORNER_MODE = 0

    PRINT "自动倒角完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：拐角处有圆弧倒角，轨迹平滑，速度保持高速"
    PRINT "测试6完成"
    PRINT ""
END SUB

'================ 测试7：组合前瞻 ================
GLOBAL SUB Test7_Combined()
    PRINT "==============================================================================="
    PRINT "                        测试7：组合前瞻（CORNER_MODE=2+32）"
    PRINT "==============================================================================="
    PRINT "根据官方手册：CORNER_MODE=2+32，同时使用拐角减速和自动倒角"
    PRINT "既有轨迹倒角又有速度控制"
    PRINT ""

    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"

    '设置组合前瞻
    MERGE = ON                          ' 开启连续插补
    CORNER_MODE = 2 + 32                ' 拐角减速+自动倒角
    DECEL_ANGLE = 25 * (PI/180)         ' 开始减速角度25度
    STOP_ANGLE = 80 * (PI/180)          ' 停止减速角度80度
    ZSMOOTH = 8                         ' 倒角半径8units
    FORCE_SPEED = 100                   ' 参考速度100units/s
    PRINT "设置：MERGE=ON, CORNER_MODE=34（2+32）"
    PRINT "DECEL_ANGLE=25度, STOP_ANGLE=80度"
    PRINT "ZSMOOTH=8, FORCE_SPEED=100"
    PRINT "开始执行矩形轨迹（组合前瞻）"

    '连续发送四段插补指令
    PRINT "连续发送四段插补指令："
    MOVE(100, 0)                        ' 第一段
    MOVE(0, 100)                        ' 第二段
    MOVE(-100, 0)                       ' 第三段
    MOVE(0, -100)                       ' 第四段

    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)

    '关闭设置
    MERGE = OFF
    CORNER_MODE = 0

    PRINT "组合前瞻完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：拐角处既有倒角又有减速，平衡了速度和精度"
    PRINT "测试7完成"
    PRINT ""
END SUB

'================ 测试8：复杂轨迹 ================
GLOBAL SUB Test8_Complex()
    PRINT "==============================================================================="
    PRINT "                        测试8：复杂轨迹连续插补"
    PRINT "==============================================================================="
    PRINT "直线插补+圆弧插补的组合轨迹，测试复杂路径的连续性"
    PRINT ""

    '回到原点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    PRINT "起始位置：(0, 0)"

    '设置最优连续插补参数
    MERGE = ON                          ' 开启连续插补
    CORNER_MODE = 32                    ' 自动倒角
    ZSMOOTH = 5                         ' 倒角半径5units
    FORCE_SPEED = 80                    ' 运动速度80units/s
    SRAMP = 80, 80                      ' S曲线时间80ms
    PRINT "设置：MERGE=ON, CORNER_MODE=32, ZSMOOTH=5, FORCE_SPEED=80"
    PRINT "开始执行复杂轨迹（直线+圆弧组合）"

    '复杂轨迹：直线+圆弧+直线+圆弧
    PRINT "第1段：直线 (0,0) → (50,0)"
    MOVE(50, 0)

    PRINT "第2段：圆弧 (50,0) → (75,25) → (100,0)"
    MOVECIRC2ABS(75, 25, 100, 0)

    PRINT "第3段：直线 (100,0) → (100,50)"
    MOVE(0, 50)

    PRINT "第4段：圆弧 (100,50) → (75,75) → (50,50)"
    MOVECIRC2ABS(75, 75, 50, 50)

    PRINT "第5段：直线 (50,50) → (0,50)"
    MOVE(-50, 0)

    PRINT "第6段：直线 (0,50) → (0,0)"
    MOVE(0, -50)

    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)

    '关闭设置
    MERGE = OFF
    CORNER_MODE = 0

    PRINT "复杂轨迹完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
    PRINT "观察：直线和圆弧之间完全连续，整体轨迹平滑"
    PRINT "测试8完成"
    PRINT ""
END SUB

'================ 输入电平定义 ================
GLOBAL input_pin_base = 0               ' 输入引脚起始编号（可修改为其他起始引脚）
GLOBAL last_input_state(8)              ' 记录上次输入状态，用于检测上升沿
GLOBAL enable_input_control = 1          ' 是否启用输入电平控制（1=启用，0=禁用）

'================ 设置输入引脚起始编号 ================
GLOBAL SUB SetInputPinBase(pin_base)
    input_pin_base = pin_base
    PRINT "设置输入引脚起始编号为：", input_pin_base
    PRINT "测试引脚分配："
    PRINT "IN", input_pin_base + 0, " - 两轴直线插补测试"
    PRINT "IN", input_pin_base + 1, " - 两轴圆弧插补测试"
    PRINT "IN", input_pin_base + 2, " - 非连续插补测试"
    PRINT "IN", input_pin_base + 3, " - 连续插补测试"
    PRINT "IN", input_pin_base + 4, " - 前瞻拐角减速测试"
    PRINT "IN", input_pin_base + 5, " - 自动倒角测试"
    PRINT "IN", input_pin_base + 6, " - 组合前瞻测试"
    PRINT "IN", input_pin_base + 7, " - 复杂轨迹测试"
    PRINT ""
END SUB

'================ 显示输入电平控制说明 ================
GLOBAL SUB ShowInputControl()
    PRINT "==============================================================================="
    PRINT "                        两轴插补测试程序 - 输入电平控制"
    PRINT "==============================================================================="
    PRINT "输入引脚分配："
    PRINT "IN", input_pin_base + 0, " - 两轴直线插补测试"
    PRINT "IN", input_pin_base + 1, " - 两轴圆弧插补测试"
    PRINT "IN", input_pin_base + 2, " - 非连续插补测试"
    PRINT "IN", input_pin_base + 3, " - 连续插补测试"
    PRINT "IN", input_pin_base + 4, " - 前瞻拐角减速测试"
    PRINT "IN", input_pin_base + 5, " - 自动倒角测试"
    PRINT "IN", input_pin_base + 6, " - 组合前瞻测试"
    PRINT "IN", input_pin_base + 7, " - 复杂轨迹测试"
    PRINT "==============================================================================="
    PRINT "使用方法："
    PRINT "1. 将对应输入引脚接高电平（24V或5V）触发测试"
    PRINT "2. 采用上升沿触发，避免重复执行"
    PRINT "3. 建议先触发IN0执行直线插补测试"
    PRINT "4. 程序会自动设置示波器监控"
    PRINT "5. 观察示波器速度曲线变化"
    PRINT ""
END SUB

'================ 检测输入上升沿 ================
GLOBAL SUB CheckInputRisingEdge()
    DIM i, current_state, rising_edge

    FOR i = 0 TO 7
        current_state = IN(input_pin_base + i)
        rising_edge = (current_state = 1) AND (last_input_state(i) = 0)

        IF rising_edge THEN
            PRINT "检测到IN", input_pin_base + i, "上升沿，启动测试", i + 1
            CALL SetupScope()           ' 自动设置示波器

            IF i = 0 THEN
                CALL Test1_Linear()
            ELSEIF i = 1 THEN
                CALL Test2_Circular()
            ELSEIF i = 2 THEN
                CALL Test3_NonContinuous()
            ELSEIF i = 3 THEN
                CALL Test4_Continuous()
            ELSEIF i = 4 THEN
                CALL Test5_CornerDecel()
            ELSEIF i = 5 THEN
                CALL Test6_AutoChamfer()
            ELSEIF i = 6 THEN
                CALL Test7_Combined()
            ELSEIF i = 7 THEN
                CALL Test8_Complex()
            ENDIF

            PRINT "测试", i + 1, "执行完成"
            PRINT ""
        ENDIF

        last_input_state(i) = current_state
    NEXT i
END SUB

'================ 运行所有测试 ================
GLOBAL SUB RunAllTests()
    PRINT "==============================================================================="
    PRINT "                        运行所有测试项目"
    PRINT "==============================================================================="
    PRINT "将依次执行所有8个测试项目，请观察示波器速度曲线变化"
    PRINT ""

    '设置示波器监控
    CALL SetupScope()

    '依次运行所有测试
    CALL Test1_Linear()
    CALL Test2_Circular()
    CALL Test3_NonContinuous()
    CALL Test4_Continuous()
    CALL Test5_CornerDecel()
    CALL Test6_AutoChamfer()
    CALL Test7_Combined()
    CALL Test8_Complex()

    PRINT "==============================================================================="
    PRINT "                        所有测试完成"
    PRINT "==============================================================================="
    PRINT "测试总结："
    PRINT "1. 直线插补：各轴按比例协调运动"
    PRINT "2. 圆弧插补：平滑的圆弧轨迹"
    PRINT "3. 非连续插补：每段之间有停顿"
    PRINT "4. 连续插补：段间连续但拐角有冲击"
    PRINT "5. 拐角减速：拐角处按比例减速"
    PRINT "6. 自动倒角：拐角处轨迹倒角"
    PRINT "7. 组合前瞻：倒角+减速的平衡方案"
    PRINT "8. 复杂轨迹：直线+圆弧的连续组合"
    PRINT ""
    PRINT "通过示波器观察VP_SPEED(0)和VP_SPEED(1)的变化："
    PRINT "- 连续插补成功：速度曲线连续，无断点"
    PRINT "- 前瞻功能有效：拐角处速度平滑变化"
    PRINT "- 轨迹质量良好：各轴协调运动"
    PRINT ""
END SUB

'================ 初始化输入状态 ================
GLOBAL SUB InitInputStates()
    DIM i
    FOR i = 0 TO 7
        last_input_state(i) = IN(input_pin_base + i)
    NEXT i
    PRINT "输入状态初始化完成"
END SUB

'================ 手动测试说明 ================
GLOBAL SUB ShowManualTest()
    PRINT "手动测试模式 - 可直接调用测试函数："
    PRINT "CALL Test1_Linear()          - 两轴直线插补"
    PRINT "CALL Test2_Circular()        - 两轴圆弧插补"
    PRINT "CALL Test3_NonContinuous()   - 非连续插补"
    PRINT "CALL Test4_Continuous()      - 连续插补"
    PRINT "CALL Test5_CornerDecel()     - 前瞻拐角减速"
    PRINT "CALL Test6_AutoChamfer()     - 自动倒角"
    PRINT "CALL Test7_Combined()        - 组合前瞻"
    PRINT "CALL Test8_Complex()         - 复杂轨迹"
    PRINT "CALL RunAllTests()           - 运行所有测试"
    PRINT "CALL SystemRestore()         - 恢复系统设置"
    PRINT ""
END SUB

'================ 手动测试模式 ================
GLOBAL SUB ManualTest()
    '系统初始化
    CALL SystemInit()

    PRINT "手动测试模式 - 可直接调用测试函数："
    PRINT "CALL Test1_Linear()          - 两轴直线插补"
    PRINT "CALL Test2_Circular()        - 两轴圆弧插补"
    PRINT "CALL Test3_NonContinuous()   - 非连续插补"
    PRINT "CALL Test4_Continuous()      - 连续插补"
    PRINT "CALL Test5_CornerDecel()     - 前瞻拐角减速"
    PRINT "CALL Test6_AutoChamfer()     - 自动倒角"
    PRINT "CALL Test7_Combined()        - 组合前瞻"
    PRINT "CALL Test8_Complex()         - 复杂轨迹"
    PRINT "CALL RunAllTests()           - 运行所有测试"
    PRINT "CALL SystemRestore()         - 恢复系统设置"
    PRINT ""
END SUB

'================ 程序启动 ================
PRINT "启动两轴插补测试程序..."

'系统初始化
CALL SystemInit()

'初始化输入状态
CALL InitInputStates()

'显示控制说明
CALL ShowInputControl()

PRINT "程序进入输入电平监控模式..."
PRINT "等待输入信号触发测试..."
PRINT ""

'主循环 - 持续监控输入电平
DO
    CALL CheckInputRisingEdge()
    DELAY 50                        ' 延时50ms，避免过于频繁检测
LOOP
