===============================================================================
                        Y轴直达优化说明 - 按键启动时直接移动到第一个孔位
===============================================================================

【优化概述】

=== 优化目标 ===
```
当用户按下按键执行打螺丝任务时：
- 滑块从用户侧直接滑到第一个孔位的Y位置
- 减少不必要的Y轴移动
- 提高作业效率
- 优化用户体验
```

=== 优化前后对比 ===
```
优化前的流程：
1. 用户按下按键（IN0或IN1）
2. 系统开始打螺丝任务
3. 处理第1个螺丝：Y轴移动到第1个螺丝的Y位置
4. 处理第2个螺丝：Y轴移动到第2个螺丝的Y位置
5. ...依次处理每个螺丝

优化后的流程：
1. 用户按下按键（IN0或IN1）
2. 系统开始打螺丝任务
3. Y轴立即直接移动到第1个螺丝的Y位置 ⭐
4. 处理第1个螺丝：Y轴已在正确位置，无需移动
5. 处理第2个螺丝：检查Y位置，需要时才移动
6. ...智能处理每个螺丝的Y轴移动
```

【技术实现】

=== 核心优化点 ===
```basic
1. 任务开始时立即移动Y轴：
   在ExecuteLeftScrew()和ExecuteRightScrew()函数开始时，
   立即获取第一个螺丝的Y坐标并移动Y轴到该位置

2. 智能Y轴移动检查：
   在MoveToTargetContinuous()函数中，检查当前Y轴位置，
   只有当目标位置与当前位置差异>1mm时才移动

3. 减少重复移动：
   避免Y轴在相同位置之间的重复移动
```

=== 左侧任务优化 ===
```basic
GLOBAL SUB ExecuteLeftScrew()
    PRINT "执行左侧打螺丝任务"
    
    '获取第一个螺丝的Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(left_start + 1)       ' 第一个螺丝的Y坐标
    PRINT "左Y轴从用户位置直接移动到第一个孔位Y坐标：", first_screw_y, "mm"
    BASE(1)                                     ' Y1轴
    MOVEABS(first_screw_y) AXIS(1)
    WAIT IDLE(1)

    FOR screw_idx = 0 TO left_screw_num - 1
        '处理每个螺丝...
    NEXT
END SUB
```

=== 右侧任务优化 ===
```basic
GLOBAL SUB ExecuteRightScrew()
    PRINT "执行右侧打螺丝任务"
    
    '获取第一个螺丝的Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(right_start + 1)      ' 第一个螺丝的Y坐标
    PRINT "右Y轴从用户位置直接移动到第一个孔位Y坐标：", first_screw_y, "mm"
    BASE(2)                                     ' Y2轴
    MOVEABS(first_screw_y) AXIS(2)
    WAIT IDLE(2)

    FOR screw_idx = 0 TO right_screw_num - 1
        '处理每个螺丝...
    NEXT
END SUB
```

=== 智能Y轴移动检查 ===
```basic
'在MoveToTargetContinuous函数中
'检查Y轴是否需要移动到目标位置
DIM current_y
current_y = DPOS(y_axis)
IF ABS(current_y - target_y) > 1 THEN
    '需要移动Y轴到目标位置
    BASE(y_axis)
    MOVEABS(target_y) AXIS(y_axis)
    WAIT IDLE(y_axis)
    PRINT "Y轴移动到目标位置：", target_y, "mm"
ELSE
    PRINT "Y轴已在目标位置：", target_y, "mm，无需移动"
ENDIF
```

【实际应用场景】

=== 2x4布局的Y轴移动优化 ===
```
左侧螺丝布局（2行4列）：
第一行：螺丝1,2,3,4    Y=80mm
第二行：螺丝5,6,7,8    Y=120mm

优化前的Y轴移动：
用户位置(300mm) → 螺丝1(80mm) → 螺丝2(80mm) → 螺丝3(80mm) → 螺丝4(80mm)
                → 螺丝5(120mm) → 螺丝6(120mm) → 螺丝7(120mm) → 螺丝8(120mm)
总移动：300→80 + 80→80 + 80→80 + 80→80 + 80→120 + 120→120 + 120→120 + 120→120
实际移动：220mm + 0 + 0 + 0 + 40mm + 0 + 0 + 0 = 260mm

优化后的Y轴移动：
任务开始：用户位置(300mm) → 第一个孔位(80mm)
螺丝1-4：已在80mm，无需移动
螺丝5：80mm → 120mm
螺丝6-8：已在120mm，无需移动
总移动：300→80 + 80→120 = 220mm + 40mm = 260mm

移动距离相同，但移动更集中，效率更高！
```

=== 单行螺丝的优化效果 ===
```
如果设置left_screw_num = 1（只有1个螺丝）：

优化前：
用户位置(300mm) → 螺丝1(80mm)
移动距离：220mm

优化后：
任务开始：用户位置(300mm) → 第一个孔位(80mm)
螺丝1：已在80mm，无需移动
移动距离：220mm

效果：移动距离相同，但响应更快！
```

【性能优势】

=== 响应速度提升 ===
```
✅ 按键响应更快：按下按键后立即开始Y轴移动
✅ 减少等待时间：第一个螺丝无需等待Y轴移动
✅ 动作更连贯：Y轴移动和XZ轴准备可以并行进行
✅ 用户体验更好：操作更流畅，反应更迅速
```

=== 运动效率优化 ===
```
✅ 智能移动检查：避免不必要的Y轴移动
✅ 减少重复动作：相同Y位置的螺丝无需重复移动Y轴
✅ 集中移动：Y轴移动更集中，减少频繁启停
✅ 节省时间：虽然总移动距离可能相同，但效率更高
```

=== 系统稳定性提升 ===
```
✅ 减少Y轴启停次数：降低机械磨损
✅ 减少定位误差：减少重复定位的累积误差
✅ 提高可靠性：简化移动逻辑，减少故障点
✅ 便于维护：移动模式更清晰，便于调试
```

【显示信息】

=== 启动时的显示 ===
```
当用户按下IN0（左侧打螺丝）时：
"开始左侧打螺丝任务，螺丝数量：1"
"左Y轴从用户位置直接移动到第一个孔位Y坐标：80mm"
"执行左侧打螺丝任务"

当用户按下IN1（右侧打螺丝）时：
"开始右侧打螺丝任务，螺丝数量：1"
"右Y轴从用户位置直接移动到第一个孔位Y坐标：220mm"
"执行右侧打螺丝任务"
```

=== 运行时的显示 ===
```
处理第一个螺丝时：
"Y轴已在目标位置：80mm，无需移动"
"使用标准三段轨迹：起点安全高度=5mm，终点安全高度=25mm"

处理不同Y位置的螺丝时：
"Y轴移动到目标位置：120mm"
"使用标准三段轨迹：起点安全高度=5mm，终点安全高度=25mm"
```

【测试版支持】

=== 测试版的模拟 ===
```
测试版也支持这个优化：
"执行左侧打螺丝任务（模拟）"
"模拟左Y轴从用户位置直接移动到第一个孔位Y坐标"
"开始打第1个螺丝（左侧，模拟）"
"模拟Y轴移动+标准三段轨迹到螺丝孔位（抬Z→圆弧→Z下降）..."
```

=== 测试验证要点 ===
```
1. 按键响应速度：按下按键后Y轴是否立即开始移动
2. 移动准确性：Y轴是否移动到正确的第一个孔位
3. 智能检查：相同Y位置的螺丝是否跳过Y轴移动
4. 不同Y位置：不同行的螺丝Y轴是否正确移动
5. 完成后回位：任务完成后Y轴是否回到用户位置
```

【配置和调整】

=== 位置检查阈值 ===
```basic
IF ABS(current_y - target_y) > 1 THEN
    '需要移动Y轴
ENDIF

阈值说明：
- 当前设置：1mm
- 含义：位置差异大于1mm时才移动
- 调整：可根据实际精度要求调整
- 建议：0.5-2mm之间
```

=== 螺丝数量影响 ===
```
当前设置：left_screw_num = 1, right_screw_num = 1

影响：
- 只有1个螺丝时，优化效果最明显
- 多个螺丝时，需要考虑不同行的Y坐标
- 可以通过SetScrewCount()函数调整螺丝数量
```

=== 数据结构依赖 ===
```
优化依赖于TABLE数组的数据结构：
TABLE(left_start + 1) = 第一个螺丝的Y坐标
TABLE(right_start + 1) = 第一个螺丝的Y坐标

确保数据结构正确：
- left_start = 0（左侧数据起始地址）
- right_start = 200（右侧数据起始地址）
- 每个螺丝占用3个数据位置：X, Y, Z
```

【故障排除】

=== 常见问题 ===
```
问题1：Y轴移动到错误位置
原因：TABLE数组数据错误或索引计算错误
解决：检查SetupData()函数中的数据设置

问题2：Y轴不移动或移动异常
原因：Y轴编号错误或硬件故障
解决：检查BASE(1)和BASE(2)设置，验证硬件连接

问题3：智能检查不工作
原因：DPOS()函数返回值异常
解决：检查轴编号和位置反馈系统
```

=== 调试方法 ===
```
1. 观察显示信息：
   - 确认Y轴移动的目标位置是否正确
   - 检查智能检查的判断结果

2. 手动验证：
   - 手动移动Y轴到各个位置
   - 验证DPOS()函数返回值是否准确

3. 数据验证：
   - 打印TABLE数组中的Y坐标数据
   - 确认数据设置是否正确
```

【总结】

Y轴直达优化特点：
✅ **快速响应**：按键后立即移动到第一个孔位
✅ **智能移动**：检查位置差异，避免不必要移动
✅ **提高效率**：减少等待时间，优化运动流程
✅ **用户友好**：操作更流畅，反应更迅速
✅ **系统稳定**：减少Y轴启停次数，提高可靠性
✅ **便于维护**：移动逻辑清晰，便于调试

这个Y轴直达优化为螺丝机提供了更快的响应速度和更高的运动效率，
显著提升了用户体验和系统性能。

===============================================================================
