'=============================================================================
' 螺丝机测试程序
' 用于验证螺丝机各项功能
' 包含硬件测试、运动测试、通讯测试等
'=============================================================================

'注意：请先运行螺丝机控制程序.bas，再运行本测试程序

'测试主程序
PRINT "=== 螺丝机系统测试程序 ==="
PRINT "开始系统测试..."

CALL RunAllTests()

PRINT "系统测试完成！"
END

'================ 运行所有测试 ================
GLOBAL SUB RunAllTests()
    PRINT ""
    PRINT "【1/8】硬件连接测试"
    CALL TestHardwareConnection()
    
    PRINT ""
    PRINT "【2/8】轴参数测试"
    CALL TestAxisParameters()
    
    PRINT ""
    PRINT "【3/8】输入信号测试"
    CALL TestInputSignals()
    
    PRINT ""
    PRINT "【4/8】输出信号测试"
    CALL TestOutputSignals()
    
    PRINT ""
    PRINT "【5/8】Modbus通讯测试"
    CALL TestModbusCommunication()
    
    PRINT ""
    PRINT "【6/8】运动功能测试"
    CALL TestMotionFunctions()
    
    PRINT ""
    PRINT "【7/8】插补功能测试"
    CALL TestInterpolation()
    
    PRINT ""
    PRINT "【8/8】综合功能测试"
    CALL TestIntegratedFunctions()
END SUB

'================ 硬件连接测试 ================
GLOBAL SUB TestHardwareConnection()
    PRINT "测试硬件连接..."
    
    '测试控制器连接
    PRINT "控制器型号：", CONTROLLER_TYPE
    PRINT "固件版本：", VERSION
    
    '测试轴连接
    FOR i = 0 TO 3
        BASE(i)
        PRINT "轴", i, "状态：", HEX(AXISSTATUS(i))
        IF AXISSTATUS(i) AND 16 THEN
            PRINT "  警告：轴", i, "驱动器报警"
        ELSE
            PRINT "  ✓ 轴", i, "连接正常"
        ENDIF
    NEXT
    
    PRINT "硬件连接测试完成"
END SUB

'================ 轴参数测试 ================
GLOBAL SUB TestAxisParameters()
    PRINT "测试轴参数设置..."
    
    FOR i = 0 TO 3
        BASE(i)
        PRINT "轴", i, "参数："
        PRINT "  ATYPE：", ATYPE(i)
        PRINT "  UNITS：", UNITS(i)
        PRINT "  SPEED：", SPEED(i)
        PRINT "  ACCEL：", ACCEL(i)
        PRINT "  CREEP：", CREEP(i)
        
        '验证参数合理性
        IF ATYPE(i) <> 1 THEN
            PRINT "  警告：ATYPE应为1（脉冲轴）"
        ENDIF
        
        IF UNITS(i) <= 0 THEN
            PRINT "  错误：UNITS必须大于0"
        ELSE
            PRINT "  ✓ 参数设置正常"
        ENDIF
    NEXT
    
    PRINT "轴参数测试完成"
END SUB

'================ 输入信号测试 ================
GLOBAL SUB TestInputSignals()
    PRINT "测试输入信号..."
    PRINT "请在10秒内触发各个输入信号进行测试"
    
    DIM test_inputs(20)
    FOR i = 0 TO 19
        test_inputs(i) = 0
    NEXT
    
    '监控输入信号10秒
    FOR test_time = 1 TO 100
        FOR i = 0 TO 19
            IF IN(i) = ON AND test_inputs(i) = 0 THEN
                test_inputs(i) = 1
                PRINT "  ✓ 检测到IN", i, "信号"
            ENDIF
        NEXT
        DELAY(100)
    WEND
    
    '检查关键信号
    PRINT "关键信号测试结果："
    IF test_inputs(0) = 1 THEN
        PRINT "  ✓ IN0（左侧开始）信号正常"
    ELSE
        PRINT "  - IN0（左侧开始）信号未测试"
    ENDIF
    
    IF test_inputs(1) = 1 THEN
        PRINT "  ✓ IN1（右侧开始）信号正常"
    ELSE
        PRINT "  - IN1（右侧开始）信号未测试"
    ENDIF
    
    IF test_inputs(2) = 1 THEN
        PRINT "  ✓ IN2（系统回零）信号正常"
    ELSE
        PRINT "  - IN2（系统回零）信号未测试"
    ENDIF
    
    IF test_inputs(3) = 1 THEN
        PRINT "  ✓ IN3（急停）信号正常"
    ELSE
        PRINT "  - IN3（急停）信号未测试"
    ENDIF
    
    PRINT "输入信号测试完成"
END SUB

'================ 输出信号测试 ================
GLOBAL SUB TestOutputSignals()
    PRINT "测试输出信号..."
    
    '测试吸螺丝输出
    PRINT "测试OP0（吸螺丝）输出..."
    OP(0, ON)
    PRINT "  OP0输出ON，请检查吸螺丝动作"
    DELAY(2000)
    
    OP(0, OFF)
    PRINT "  OP0输出OFF，请检查吸螺丝停止"
    DELAY(1000)
    
    '测试其他输出
    FOR i = 1 TO 7
        PRINT "测试OP", i, "输出..."
        OP(i, ON)
        DELAY(500)
        OP(i, OFF)
        DELAY(500)
    NEXT
    
    PRINT "输出信号测试完成"
END SUB

'================ Modbus通讯测试 ================
GLOBAL SUB TestModbusCommunication()
    PRINT "测试Modbus通讯..."
    
    '测试串口配置
    PRINT "串口配置：115200, 8, 1, 0"
    
    '测试电批连接
    DIM result
    result = MODBUSM_DES(gv_ScrewDriver_Addr, 1000, 1)
    IF result = 0 THEN
        PRINT "  ✓ 电批连接成功，地址：", gv_ScrewDriver_Addr
    ELSE
        PRINT "  ✗ 电批连接失败，错误码：", result
        PRINT "  请检查：1.RS485连接 2.电批地址 3.波特率设置"
    ENDIF
    
    '测试读取电批状态
    result = MODBUSM_REGGET(9728, 1, 100)
    IF result = 0 THEN
        PRINT "  ✓ 电批状态读取成功：", MODBUS_REG(100)
    ELSE
        PRINT "  ✗ 电批状态读取失败"
    ENDIF
    
    '测试写入命令
    result = MODBUSM_REGSET(9222, 1, 0)
    IF result = 0 THEN
        PRINT "  ✓ 电批命令写入成功"
    ELSE
        PRINT "  ✗ 电批命令写入失败"
    ENDIF
    
    PRINT "Modbus通讯测试完成"
END SUB

'================ 运动功能测试 ================
GLOBAL SUB TestMotionFunctions()
    PRINT "测试运动功能..."
    
    '检查是否可以运动
    DIM can_move
    can_move = 1
    
    FOR i = 0 TO 3
        IF AXISSTATUS(i) <> 0 THEN
            PRINT "  警告：轴", i, "状态异常，无法测试运动"
            can_move = 0
        ENDIF
    NEXT
    
    IF can_move = 0 THEN
        PRINT "  建议先执行回零后再测试运动"
        PRINT "运动功能测试跳过"
        RETURN
    ENDIF
    
    '测试单轴运动
    PRINT "测试单轴运动..."
    FOR i = 0 TO 3
        BASE(i)
        PRINT "  测试轴", i, "运动..."
        
        DIM start_pos
        start_pos = DPOS(i)
        
        '小幅度运动测试
        MOVE(10)
        WAIT IDLE(i)
        DELAY(500)
        
        MOVE(-10)
        WAIT IDLE(i)
        DELAY(500)
        
        '检查是否回到原位
        IF ABS(DPOS(i) - start_pos) < 0.1 THEN
            PRINT "    ✓ 轴", i, "运动正常"
        ELSE
            PRINT "    ✗ 轴", i, "运动异常，位置偏差：", ABS(DPOS(i) - start_pos)
        ENDIF
    NEXT
    
    PRINT "运动功能测试完成"
END SUB

'================ 插补功能测试 ================
GLOBAL SUB TestInterpolation()
    PRINT "测试插补功能..."
    
    '测试两轴直线插补
    PRINT "测试XY直线插补..."
    BASE(0, 1)
    
    DIM start_x, start_y
    start_x = DPOS(0)
    start_y = DPOS(1)
    
    '矩形轨迹测试
    MOVE(10, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(0, 10)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(-10, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVE(0, -10)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '检查是否回到原位
    IF ABS(DPOS(0) - start_x) < 0.1 AND ABS(DPOS(1) - start_y) < 0.1 THEN
        PRINT "  ✓ XY直线插补正常"
    ELSE
        PRINT "  ✗ XY直线插补异常"
    ENDIF
    
    '测试圆弧插补
    PRINT "测试XY圆弧插补..."
    
    '小圆弧测试
    MOVECIRC2(start_x + 10, start_y, start_x + 5, start_y + 5, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    MOVECIRC2(start_x, start_y, start_x + 5, start_y - 5, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '检查圆弧插补结果
    IF ABS(DPOS(0) - start_x) < 0.5 AND ABS(DPOS(1) - start_y) < 0.5 THEN
        PRINT "  ✓ XY圆弧插补正常"
    ELSE
        PRINT "  ✗ XY圆弧插补异常"
    ENDIF
    
    PRINT "插补功能测试完成"
END SUB

'================ 综合功能测试 ================
GLOBAL SUB TestIntegratedFunctions()
    PRINT "测试综合功能..."
    
    '测试参数设置功能
    PRINT "测试参数设置..."
    CALL SetScrewCount(1, 3)
    CALL SetScrewCount(2, 3)
    CALL SetPickPosition(50, 100, 5)
    CALL SetScrewPosition(1, 1, 100, 80, 0)
    CALL SetScrewPosition(1, 2, 150, 80, 0)
    CALL SetScrewPosition(1, 3, 200, 80, 0)
    PRINT "  ✓ 参数设置功能正常"
    
    '测试状态查询功能
    PRINT "测试状态查询..."
    CALL ShowSystemStatus()
    PRINT "  ✓ 状态查询功能正常"
    
    '测试配置保存/加载
    PRINT "测试配置保存/加载..."
    CALL SaveConfiguration()
    PRINT "  ✓ 配置保存成功"
    
    CALL LoadConfiguration()
    PRINT "  ✓ 配置加载成功"
    
    '测试配置验证
    PRINT "测试配置验证..."
    CALL ValidateConfiguration()
    PRINT "  ✓ 配置验证完成"
    
    PRINT "综合功能测试完成"
END SUB

'================ 性能测试 ================
GLOBAL SUB TestPerformance()
    PRINT "=== 性能测试 ==="
    
    '测试运动速度
    PRINT "测试运动速度..."
    BASE(0)
    
    DIM start_time, end_time, distance, actual_speed
    distance = 100  ' 100mm距离
    
    start_time = MTIME
    MOVE(distance)
    WAIT IDLE(0)
    end_time = MTIME
    
    actual_speed = distance / ((end_time - start_time) / 1000)
    PRINT "  设定速度：", SPEED(0), " mm/s"
    PRINT "  实际速度：", actual_speed, " mm/s"
    
    '回到原位
    MOVE(-distance)
    WAIT IDLE(0)
    
    '测试插补性能
    PRINT "测试插补性能..."
    BASE(0, 1)
    
    start_time = MTIME
    FOR i = 1 TO 10
        MOVE(10, 10)
        WAIT IDLE(0)
        WAIT IDLE(1)
        MOVE(-10, -10)
        WAIT IDLE(0)
        WAIT IDLE(1)
    NEXT
    end_time = MTIME
    
    PRINT "  10次往返插补时间：", (end_time - start_time) / 1000, " 秒"
    
    PRINT "性能测试完成"
END SUB

'================ 生成测试报告 ================
GLOBAL SUB GenerateTestReport()
    PRINT ""
    PRINT "========================================"
    PRINT "           螺丝机系统测试报告"
    PRINT "========================================"
    PRINT "测试时间：", MTIME
    PRINT "控制器型号：", CONTROLLER_TYPE
    PRINT "固件版本：", VERSION
    PRINT ""
    PRINT "测试项目："
    PRINT "✓ 硬件连接测试"
    PRINT "✓ 轴参数测试"
    PRINT "✓ 输入信号测试"
    PRINT "✓ 输出信号测试"
    PRINT "✓ Modbus通讯测试"
    PRINT "✓ 运动功能测试"
    PRINT "✓ 插补功能测试"
    PRINT "✓ 综合功能测试"
    PRINT ""
    PRINT "系统状态："
    FOR i = 0 TO 3
        PRINT "轴", i, "状态：", HEX(AXISSTATUS(i))
    NEXT
    PRINT ""
    PRINT "建议："
    PRINT "1. 如有测试失败项目，请检查硬件连接"
    PRINT "2. 确保所有信号连接正确"
    PRINT "3. 执行系统回零后开始正式作业"
    PRINT "========================================"
END SUB
