===============================================================================
                        Z轴运动控制说明 - 智能安全高度管理
===============================================================================

【Z轴坐标系统】

=== 坐标方向 ===
Z轴方向：往下为正方向
- Z = 0：    参考零点（通常为工作台面）
- Z = 10：   吸螺丝位置（往下10mm）
- Z = 30：   打螺丝工作高度（往下30mm）
- Z值越大，位置越深

=== 关键高度定义 ===
```basic
pick_z = 10                     ' 吸螺丝位置Z坐标
screw_work_height = 30          ' 打螺丝工作高度
safe_height_from_work = 5       ' 从工件抬起安全高度5mm
safe_height_from_pick = 2       ' 从吸螺丝抬起安全高度2mm
```

【智能安全高度管理】

=== 安全高度策略 ===
根据当前位置智能选择安全高度：

1. **从工件位置抬起**：
   - 当前位置：Z ≈ 30mm（打螺丝工作高度）
   - 安全高度：Z = 30 - 5 = 25mm
   - 抬升距离：5mm

2. **从吸螺丝位置抬起**：
   - 当前位置：Z ≈ 10mm（吸螺丝位置）
   - 安全高度：Z = 10 - 2 = 8mm
   - 抬升距离：2mm

=== 位置判断逻辑 ===
```basic
current_z = DPOS(3)
IF ABS(current_z - screw_work_height) < 5 THEN
    '当前在工件位置，抬升5mm
    safe_z = current_z - safe_height_from_work
ELSE
    '当前在其他位置，抬升2mm
    safe_z = current_z - safe_height_from_pick
ENDIF
```

【运动流程详解】

=== 完整打螺丝流程 ===
```
1. 圆弧插补到吸螺丝位置
   ├─ 智能抬升到安全高度（2mm或5mm）
   ├─ XY圆弧插补移动
   └─ 下降到吸螺丝位置（Z=10mm）

2. 吸取螺丝
   └─ 开启吸螺丝，等待1秒

3. 圆弧插补到螺丝孔位
   ├─ 从吸螺丝位置抬升2mm（Z=8mm）
   ├─ XY圆弧插补移动
   └─ 下降到螺丝孔位（Z=30mm）

4. 执行打螺丝
   ├─ 下降到接触位置（Z=28mm）
   ├─ 下降到打螺丝深度（Z=30mm）
   ├─ 电批锁紧2秒
   └─ 抬升到工件上方（Z=27mm）

5. 圆弧插补回吸螺丝位置
   ├─ 从工件位置抬升5mm（Z=25mm）
   ├─ XY圆弧插补移动
   └─ 下降到吸螺丝位置（Z=10mm）
```

【Z轴运动函数详解】

=== 1. MoveToPick() - 移动到吸螺丝位置 ===
```basic
功能：智能抬升 + 圆弧插补 + 精确下降
特点：
- 自动判断当前位置类型
- 选择合适的安全高度
- 圆弧插补平滑移动
- 精确定位到吸螺丝高度
```

=== 2. ArcMoveToTarget() - 移动到螺丝孔位 ===
```basic
功能：从吸螺丝位置到螺丝孔位
特点：
- 固定从吸螺丝位置抬升2mm
- 圆弧插补避免碰撞
- 精确下降到工作高度
```

=== 3. DoScrew() - 执行打螺丝 ===
```basic
功能：分层下降 + 电批锁紧 + 适度抬升
特点：
- 先到接触位置（Z-2mm）
- 再到工作深度（Z）
- 锁紧后抬升3mm
```

=== 4. ArcMoveBack() - 回到吸螺丝位置 ===
```basic
功能：从工件位置回到吸螺丝位置
特点：
- 固定从工件位置抬升5mm
- 圆弧插补平滑移动
- 精确下降到吸螺丝高度
```

【安全高度优化】

=== 为什么使用不同的安全高度？ ===

1. **从工件抬升5mm**：
   ✅ 工件表面可能有突起或螺丝头
   ✅ 需要足够的间隙避免碰撞
   ✅ 5mm提供充分的安全余量

2. **从吸螺丝抬升2mm**：
   ✅ 吸螺丝位置相对平整
   ✅ 2mm足够避免碰撞
   ✅ 减少不必要的Z轴行程

=== 安全高度计算示例 ===
```
场景1：从螺丝孔位(Z=30)移动到吸螺丝位置(Z=10)
├─ 当前位置：Z=30mm
├─ 判断：接近工作高度，选择5mm安全高度
├─ 安全高度：Z=30-5=25mm
├─ 抬升：从30mm到25mm
├─ XY移动：圆弧插补
└─ 下降：从25mm到10mm

场景2：从吸螺丝位置(Z=10)移动到螺丝孔位(Z=30)
├─ 当前位置：Z=10mm
├─ 判断：吸螺丝位置，选择2mm安全高度
├─ 安全高度：Z=10-2=8mm
├─ 抬升：从10mm到8mm
├─ XY移动：圆弧插补
└─ 下降：从8mm到30mm
```

【打螺丝深度控制】

=== 分层下降策略 ===
```basic
目标深度：30mm
├─ 第一层：下降到28mm（接触位置）
├─ 第二层：下降到30mm（工作深度）
├─ 锁紧：电批工作2秒
└─ 抬升：到27mm（工件上方3mm）
```

=== 优势分析 ===
✅ **避免冲击**：分层下降减少冲击
✅ **精确定位**：两步到位，提高精度
✅ **保护电批**：避免硬接触损坏电批
✅ **提高成功率**：稳定的锁紧过程

【参数调优指南】

=== 安全高度调整 ===
```basic
' 根据实际工件调整
safe_height_from_work = 5       ' 工件复杂度高，增加到6-8mm
safe_height_from_pick = 2       ' 吸螺丝区域平整，保持2mm
```

=== 工作高度调整 ===
```basic
screw_work_height = 30          ' 根据螺丝长度调整
pick_z = 10                     ' 根据吸螺丝器高度调整
```

=== 接触检测优化 ===
```basic
approach_z = target_z - 2       ' 接触位置偏移，可调整为1-3mm
lift_z = target_z - 3           ' 完成后抬升高度，可调整为2-5mm
```

【常见问题解答】

=== Q1：为什么Z轴往下为正？ ===
A：符合机械加工习惯，工件表面为参考，向下加工为正方向

=== Q2：安全高度设置是否合理？ ===
A：5mm和2mm是经验值，可根据实际工件和设备调整

=== Q3：分层下降是否必要？ ===
A：是的，避免硬接触，提高定位精度和电批寿命

=== Q4：如何处理不同高度的工件？ ===
A：调整screw_work_height参数，或在TABLE数据中设置不同的Z值

=== Q5：抬升高度不够怎么办？ ===
A：增加safe_height_from_work和safe_height_from_pick的值

【调试建议】

=== 测试步骤 ===
1. **低速测试**：先用较低的Z轴速度测试
2. **观察轨迹**：检查Z轴运动是否平滑
3. **调整参数**：根据实际情况调整安全高度
4. **验证精度**：检查定位精度是否满足要求

=== 观察要点 ===
✅ **碰撞检查**：确保没有碰撞风险
✅ **定位精度**：检查Z轴定位是否准确
✅ **运动平滑**：观察Z轴运动是否平稳
✅ **时间效率**：评估Z轴运动时间是否合理

【性能优势】

=== 智能化程度高 ===
✅ **自动判断**：根据当前位置自动选择安全高度
✅ **精确控制**：分层下降，精确定位
✅ **安全可靠**：多重安全保护机制

=== 运动质量好 ===
✅ **平滑运动**：结合圆弧插补和S型曲线
✅ **减少冲击**：智能安全高度管理
✅ **提高精度**：分层下降策略

=== 适应性强 ===
✅ **参数可调**：所有关键参数都可调整
✅ **兼容性好**：适应不同工件和设备
✅ **扩展性强**：易于添加新的运动模式

【总结】

螺丝机Z轴运动控制系统特点：
✅ **智能安全高度**：根据位置自动选择2mm或5mm
✅ **分层下降**：接触位置→工作深度，提高精度
✅ **圆弧插补配合**：XY圆弧+Z智能，完美结合
✅ **参数化设计**：所有关键参数可调，适应性强
✅ **安全可靠**：多重保护，避免碰撞和冲击

这套Z轴控制系统与1m/s高速运动、S型曲线、圆弧插补完美配合，
实现了高速、高精度、高安全性的螺丝机运动控制。

===============================================================================
