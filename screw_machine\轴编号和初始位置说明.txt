===============================================================================
                        轴编号和初始位置说明 - 系统启动后的轴配置
===============================================================================

【轴编号对应关系】

=== 四轴配置 ===
```
X轴：编号0    - 水平移动轴，控制螺丝机头的左右位置
Y1轴：编号1   - 左侧滑台，控制左侧工件的前后位置
Y2轴：编号2   - 右侧滑台，控制右侧工件的前后位置
Z轴：编号3    - 垂直移动轴，控制螺丝机头的上下位置
```

=== 轴参数配置 ===
```basic
BASE(0, 1, 2, 3)                    ' 四轴基础配置
ATYPE = 1, 1, 1, 1                  ' 步进电机开环脉冲控制
UNITS = 1000, 100, 100, 1000        ' 脉冲当量设置
SPEED = 1000, 1000, 1000, 500       ' 运动速度设置
ACCEL = 1000, 1000, 1000, 1000      ' 加速度设置
DECEL = 1000, 1000, 1000, 1000      ' 减速度设置
CREEP = 10, 10, 10, 5               ' 回零爬行速度
```

=== 脉冲当量说明 ===
```
X轴：1000脉冲/mm    - 高精度水平移动
Y1轴：100脉冲/mm    - 10mm螺距丝杆，适配大行程
Y2轴：100脉冲/mm    - 10mm螺距丝杆，适配大行程
Z轴：1000脉冲/mm    - 高精度垂直移动
```

=== 运动速度说明 ===
```
X轴：1000mm/s (1m/s)    - 快速水平移动
Y1轴：1000mm/s (1m/s)   - 快速前后移动
Y2轴：1000mm/s (1m/s)   - 快速前后移动
Z轴：500mm/s (0.5m/s)   - 适中的垂直移动速度
```

【系统启动流程】

=== 完整启动序列 ===
```
1. 系统初始化
   - 轴参数设置
   - 数据设置
   - 任务启动

2. 轴编号打印
   - 显示X\Y1\Y2\Z对应的轴编号
   - 便于调试和维护

3. 系统回零
   - 所有轴按顺序回零
   - 建立坐标系原点

4. 移动到初始位置 ⭐
   - Y1轴移动到左侧用户位置
   - Y2轴移动到右侧用户位置
   - X轴移动到吸螺丝位置
   - Z轴移动到吸螺丝安全高度
```

=== 回零完成后的初始位置 ===
```
回零完成后自动移动到：
X轴：pick_x = 50mm              ' 吸螺丝位置
Y1轴：left_user_pos = 300mm     ' 左侧用户位置
Y2轴：right_user_pos = 300mm    ' 右侧用户位置
Z轴：pick_safe_height = 8mm     ' 吸螺丝安全高度

这个位置是：
- X轴在吸螺丝器上方
- Y轴在用户操作位置
- Z轴在安全高度，可以直接下降吸螺丝
```

【初始位置的意义】

=== 为什么选择这个初始位置 ===
```
1. X轴在吸螺丝位置(50mm)：
   - 系统启动后就在螺丝供给位置
   - 可以立即开始作业
   - 减少首次移动时间

2. Y轴在用户位置(300mm)：
   - 方便用户放置工件
   - 安全的操作位置
   - 远离工作区域

3. Z轴在安全高度(8mm)：
   - 高于吸螺丝器表面
   - 可以安全下降吸螺丝
   - 避免碰撞风险
```

=== 初始位置的优势 ===
```
✅ 即时可用：系统启动后立即可以开始作业
✅ 安全可靠：所有轴都在安全位置
✅ 操作便利：用户可以方便地放置工件
✅ 效率优化：减少首次移动的时间
✅ 一致性好：每次启动都是相同的初始状态
```

【启动信息显示】

=== 轴编号显示 ===
```
系统启动时会显示：
=== 轴编号对应关系 ===
X轴：编号0
Y1轴（左侧）：编号1
Y2轴（右侧）：编号2
Z轴：编号3
=====================
```

=== 回零完成显示 ===
```
回零完成后会显示：
回零完成，移动到初始位置...
双Y轴移动到用户位置...
X轴移动到吸螺丝位置：50mm
Z轴移动到吸螺丝安全高度：8mm
所有轴回零完成，已移动到初始工作位置
当前位置：X=50mm, Y1=300mm, Y2=300mm, Z=8mm
```

【调试和维护】

=== 轴编号确认 ===
```
通过启动信息确认：
1. 各轴的编号是否正确
2. 轴参数设置是否合理
3. 初始位置是否符合预期

如果轴编号不对：
- 检查硬件连接
- 确认驱动器设置
- 验证BASE指令配置
```

=== 初始位置调试 ===
```
如果初始位置不合适，可以调整：
pick_x = 50                     ' 吸螺丝X位置
left_user_pos = 300             ' 左侧用户位置
right_user_pos = 300            ' 右侧用户位置
pick_safe_height = 8            ' 吸螺丝安全高度

调整原则：
- 确保安全，避免碰撞
- 便于操作，提高效率
- 符合机械限制
```

=== 位置验证方法 ===
```
启动后验证各轴位置：
1. 观察X轴是否在吸螺丝器上方
2. 检查Y轴是否在用户操作位置
3. 确认Z轴高度是否安全
4. 测试手动移动是否正常

如有问题：
- 检查机械限位
- 验证坐标系设置
- 确认参数配置
```

【实际应用】

=== 日常使用流程 ===
```
1. 启动系统
   - 观察轴编号显示
   - 等待回零完成

2. 确认初始位置
   - X轴在吸螺丝位置
   - Y轴在用户位置
   - Z轴在安全高度

3. 放置工件
   - 在Y轴用户位置放置工件
   - 确认工件固定牢靠

4. 开始作业
   - 按下启动按钮
   - 系统自动执行螺丝锁紧
```

=== 维护检查要点 ===
```
定期检查：
1. 轴编号显示是否正确
2. 初始位置是否准确
3. 各轴运动是否平滑
4. 安全高度是否合适

异常处理：
- 轴编号错误：检查硬件连接
- 初始位置偏差：重新标定原点
- 运动异常：检查机械部件
- 安全高度不当：调整参数设置
```

【参数调整指南】

=== 吸螺丝位置调整 ===
```basic
pick_x = 50                     ' 当前设置50mm

调整考虑：
- 螺丝供给器的实际位置
- 吸嘴的安装位置
- 机械结构的限制

调整方法：
1. 手动移动X轴到合适位置
2. 记录当前坐标值
3. 修改pick_x参数
4. 重新启动验证
```

=== 安全高度调整 ===
```basic
pick_safe_height = 8            ' 当前设置8mm

调整考虑：
- 吸螺丝器的高度
- 螺丝的长度
- 安全间隙要求

调整方法：
1. 测量吸螺丝器表面高度
2. 加上2-5mm安全间隙
3. 修改pick_safe_height参数
4. 重新启动验证
```

=== 用户位置调整 ===
```basic
left_user_pos = 300             ' 左侧用户位置
right_user_pos = 300            ' 右侧用户位置

调整考虑：
- 用户操作的便利性
- 工件放置的空间
- 安全操作距离

调整方法：
1. 确定最佳操作位置
2. 手动移动Y轴到该位置
3. 记录坐标值
4. 修改参数并验证
```

【总结】

轴编号和初始位置系统特点：
✅ **清晰标识**：启动时显示轴编号对应关系
✅ **智能初始化**：回零后自动移动到工作位置
✅ **安全可靠**：初始位置确保操作安全
✅ **即时可用**：启动完成即可开始作业
✅ **便于调试**：详细的位置信息显示
✅ **灵活调整**：参数可根据实际需求修改

这套轴编号和初始位置系统为螺丝机提供了清晰的轴配置信息和
智能的初始位置设置，确保系统启动后即可高效、安全地开始作业。

===============================================================================
