===============================================================================
                        2x4布局64孔支持说明 - Y方向跨度大的螺丝孔布局
===============================================================================

【布局说明】

=== 2x4布局特点 ===
```
2x4布局：
行1: 螺丝1  螺丝2  螺丝3  螺丝4
行2: 螺丝5  螺丝6  螺丝7  螺丝8
特点：2行4列，Y方向跨度大

布局优势：
✅ Y方向分布更广，适合宽幅工件
✅ X方向紧凑，节省X轴行程
✅ 符合传统PCB板的螺丝孔分布习惯
✅ Y轴移动距离大，但覆盖范围广
```

=== 坐标分布规律 ===
```
X方向：100, 150, 200, 250mm（间距50mm）
Y方向：工作位置, 工作位置+40mm（行间距40mm）
Z方向：统一30mm工作深度
```

【具体坐标分布】

=== 左侧螺丝位置（2x4布局）===
```
第一行（Y=80）：
螺丝1: (100, 80, 30)    ' X=100
螺丝2: (150, 80, 30)    ' X=150
螺丝3: (200, 80, 30)    ' X=200
螺丝4: (250, 80, 30)    ' X=250

第二行（Y=120）：
螺丝5: (100, 120, 30)   ' X=100
螺丝6: (150, 120, 30)   ' X=150
螺丝7: (200, 120, 30)   ' X=200
螺丝8: (250, 120, 30)   ' X=250
```

=== 右侧螺丝位置（2x4布局）===
```
第一行（Y=220）：
螺丝1: (100, 220, 30)   ' X=100
螺丝2: (150, 220, 30)   ' X=150
螺丝3: (200, 220, 30)   ' X=200
螺丝4: (250, 220, 30)   ' X=250

第二行（Y=260）：
螺丝5: (100, 260, 30)   ' X=100
螺丝6: (150, 260, 30)   ' X=150
螺丝7: (200, 260, 30)   ' X=200
螺丝8: (250, 260, 30)   ' X=250
```

【64孔支持架构】

=== 数据存储优化 ===
```basic
数据存储布局：
left_start = 0               ' 左侧起始地址（0-191）
right_start = 200            ' 右侧起始地址（200-391）
数据容量：64个螺丝 × 3个坐标 = 192个数据位置/侧
```

=== 变量定义 ===
```basic
GLOBAL left_screw_num = 8          ' 左侧螺丝数量（默认8个，最多64个）
GLOBAL right_screw_num = 8         ' 右侧螺丝数量（默认8个，最多64个）
GLOBAL max_screw_num = 64          ' 单侧最大螺丝数量
```

=== 动态设置螺丝数量 ===
```basic
GLOBAL SUB SetScrewCount(left_count, right_count)
功能：动态设置左右两侧的螺丝数量
参数：
- left_count：左侧螺丝数量（1-64）
- right_count：右侧螺丝数量（1-64）

使用示例：
CALL SetScrewCount(16, 16)      ' 设置左右各16个螺丝
CALL SetScrewCount(32, 24)      ' 设置左侧32个，右侧24个螺丝
CALL SetScrewCount(64, 64)      ' 设置最大64个螺丝/侧
```

【扩展布局示例】

=== 16个螺丝布局（2x8）===
```
如果设置left_screw_num = 16：

第1行: 螺丝1,2,3,4,5,6,7,8     (Y=80)
      X=100,150,200,250,300,350,400,450
第2行: 螺丝9,10,11,12,13,14,15,16 (Y=120)
      X=100,150,200,250,300,350,400,450

X跨度：350mm（从100到450）
Y跨度：40mm（两行之间）
```

=== 32个螺丝布局（2x16）===
```
如果设置left_screw_num = 32：

第1行: 螺丝1-16   (Y=80)
      X=100,150,200...850
第2行: 螺丝17-32  (Y=120)
      X=100,150,200...850

X跨度：750mm（从100到850）
Y跨度：40mm（两行之间）
```

=== 64个螺丝布局（2x32）===
```
如果设置left_screw_num = 64：

第1行: 螺丝1-32   (Y=80)
      X=100,150,200...1650
第2行: 螺丝33-64  (Y=120)
      X=100,150,200...1650

X跨度：1550mm（从100到1650）
Y跨度：40mm（两行之间）
```

【数据结构详解】

=== TABLE数组布局 ===
```basic
左侧螺丝数据（起始地址0）：
TABLE(0) = X1, TABLE(1) = Y1, TABLE(2) = Z1     ' 螺丝1
TABLE(3) = X2, TABLE(4) = Y2, TABLE(5) = Z2     ' 螺丝2
...
TABLE(189) = X64, TABLE(190) = Y64, TABLE(191) = Z64  ' 螺丝64

右侧螺丝数据（起始地址200）：
TABLE(200) = X1, TABLE(201) = Y1, TABLE(202) = Z1    ' 螺丝1
TABLE(203) = X2, TABLE(204) = Y2, TABLE(205) = Z2    ' 螺丝2
...
TABLE(389) = X64, TABLE(390) = Y64, TABLE(391) = Z64 ' 螺丝64
```

=== 坐标计算公式 ===
```basic
对于2x4布局的螺丝i（从0开始计数）：
行号 = i / 4                    ' 整数除法（每行4个螺丝）
列号 = i % 4                    ' 取余数

X坐标 = 100 + 列号 * 50         ' 起始100mm，间距50mm
Y坐标 = work_pos + 行号 * 40    ' 工作位置，行间距40mm
Z坐标 = screw_work_height       ' 固定工作深度30mm

示例：
螺丝0: 行0列0 → (100, 80, 30)
螺丝1: 行0列1 → (150, 80, 30)
螺丝2: 行0列2 → (200, 80, 30)
螺丝3: 行0列3 → (250, 80, 30)
螺丝4: 行1列0 → (100, 120, 30)
螺丝5: 行1列1 → (150, 120, 30)
```

【使用方法】

=== 默认8个螺丝 ===
```basic
' 系统默认设置
left_screw_num = 8              ' 左侧8个螺丝
right_screw_num = 8             ' 右侧8个螺丝

' 启动程序即可使用2x4布局
RUN "螺丝机多线程简化版.bas"
```

=== 自定义螺丝数量 ===
```basic
' 设置左右各16个螺丝（2x8布局）
CALL SetScrewCount(16, 16)

' 设置左侧32个，右侧24个螺丝
CALL SetScrewCount(32, 24)

' 设置最大64个螺丝（2x32布局）
CALL SetScrewCount(64, 64)
```

=== 验证设置 ===
```basic
启动后会显示：
"数据设置完成"
"左侧螺丝数量：8（2x4阵列）"
"右侧螺丝数量：8（2x4阵列）"
"最大支持螺丝数量：64个/侧"
```

【性能影响分析】

=== Y轴移动特点 ===
```
2x4布局：Y轴跨度 = 40mm
特点：
- Y方向覆盖范围大，适合宽幅工件
- 每行螺丝较多，X方向移动效率高
- Y轴移动距离适中，平衡了覆盖范围和效率
```

=== 内存使用 ===
```
8个螺丝：24个数据位置（8×3）
16个螺丝：48个数据位置（16×3）
32个螺丝：96个数据位置（32×3）
64个螺丝：192个数据位置（64×3）

总内存需求：左侧192 + 右侧192 = 384个TABLE位置
控制器TABLE数组通常支持1000+位置，完全足够
```

=== 运动时间分析 ===
```
2x4布局优势：
- X方向移动距离适中
- Y方向覆盖范围大但移动距离合理
- 适合大多数PCB板的螺丝孔分布
- 运动效率和覆盖范围的良好平衡
```

【扩展应用场景】

=== 小型PCB（8个螺丝）===
```
使用默认2x4布局：
- 2行4列，标准布局
- 适合小型电子产品
- X跨度：150mm，Y跨度：40mm
```

=== 中型PCB（16个螺丝）===
```
使用2x8布局：
- 2行8列，中等布局
- 适合中型电子产品
- X跨度：350mm，Y跨度：40mm
```

=== 大型PCB（32个螺丝）===
```
使用2x16布局：
- 2行16列，大型布局
- 适合大型电子产品
- X跨度：750mm，Y跨度：40mm
```

=== 超大型应用（64个螺丝）===
```
使用2x32布局：
- 2行32列，超大布局
- 适合工业级大型产品
- X跨度：1550mm，Y跨度：40mm
```

【注意事项】

=== 机械限制 ===
```
使用大量螺丝时需要考虑：
- X轴行程是否足够（最大需要1650mm）
- Y轴行程是否足够（需要覆盖40mm跨度）
- 工作台尺寸是否匹配
- 夹具设计是否合适
```

=== 性能考虑 ===
```
螺丝数量增加时：
- 单次作业时间会相应增加
- 需要更多的螺丝供给
- 电批寿命消耗加快
- 建议分批次作业
```

【总结】

2x4布局64孔支持系统特点：
✅ **经典布局**：2x4布局，Y方向跨度大，适合宽幅工件
✅ **大容量支持**：单侧最多64个螺丝，总计128个螺丝
✅ **灵活配置**：支持1-64个螺丝的任意数量设置
✅ **数据优化**：重新设计存储结构，支持大容量数据
✅ **向后兼容**：默认8个螺丝，与原有系统兼容
✅ **扩展性强**：可根据实际需求灵活调整螺丝数量
✅ **覆盖范围广**：Y方向跨度大，适合各种宽幅工件

这套2x4布局64孔支持系统为螺丝机提供了强大的适应性和扩展性，
特别适合需要Y方向大跨度覆盖的各种螺丝锁紧应用。

===============================================================================
