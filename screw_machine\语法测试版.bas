'===============================================================================
'                        螺丝机语法测试版 - 验证修复后的语法
'===============================================================================

'轨迹模式设置
GLOBAL use_safe_height = 1          ' 是否使用安全高度：1=使用三段轨迹，0=只用圆弧插补

'Z轴高度设置
GLOBAL screw_work_height = 30       ' 打螺丝工作高度30mm
GLOBAL pick_safe_height = 8         ' 吸螺丝位置安全高度8mm（10-2）
GLOBAL work_safe_height = 25        ' 工件位置安全高度25mm（30-5）
GLOBAL arc_top_height = 20          ' 圆弧插补最高点Z轴高度20mm

'吸螺丝位置
GLOBAL pick_x = 50                  ' 吸螺丝位置X
GLOBAL pick_y = 150                 ' 吸螺丝位置Y  
GLOBAL pick_z = 10                  ' 吸螺丝位置Z（往下为正）

'================ 主程序 ================
PRINT "语法测试开始"

'测试智能轨迹函数
PRINT "测试use_safe_height = 1（三段轨迹）"
use_safe_height = 1
CALL TestSmartTrajectory(50, 10, 100, 30)

PRINT "测试use_safe_height = 0（纯圆弧）"
use_safe_height = 0
CALL TestSmartTrajectory(50, 10, 100, 30)

PRINT "语法测试完成"
END

'================ 测试智能轨迹 ================
GLOBAL SUB TestSmartTrajectory(start_x, start_z, end_x, end_z)
    DIM distance_x, total_distance
    DIM mid_x
    
    distance_x = end_x - start_x
    total_distance = ABS(distance_x)
    mid_x = (start_x + end_x) / 2
    
    PRINT "测试轨迹：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"
    
    '检查安全高度设置，决定使用哪种轨迹
    IF use_safe_height = 1 THEN
        '使用安全高度，执行三段轨迹
        PRINT "使用三段连续轨迹"
        CALL TestThreeSegmentTrajectory(start_x, start_z, end_x, end_z, pick_safe_height, work_safe_height, mid_x, total_distance)
    ELSE
        '不使用安全高度，只做圆弧插补
        PRINT "只使用圆弧插补"
        CALL TestDirectArcMove(start_x, start_z, end_x, end_z, total_distance)
    ENDIF
END SUB

'================ 测试直接圆弧插补 ================
GLOBAL SUB TestDirectArcMove(start_x, start_z, end_x, end_z, total_distance)
    DIM arc_mid_x
    arc_mid_x = (start_x + end_x) / 2
    
    IF total_distance >= 5 THEN
        PRINT "模拟MOVECIRC2ABS(", arc_mid_x, ",", arc_top_height, ",", end_x, ",", end_z, ")"
    ELSE
        PRINT "模拟MOVEABS(", end_x, ",", end_z, ")"
    ENDIF
END SUB

'================ 测试三段连续轨迹 ================
GLOBAL SUB TestThreeSegmentTrajectory(start_x, start_z, end_x, end_z, start_safe_z, end_safe_z, arc_mid_x, arc_total_distance)
    PRINT "第一段：模拟MOVEABS(", start_x, ",", start_safe_z, ")"
    
    IF arc_total_distance >= 5 THEN
        PRINT "第二段：模拟MOVECIRC2ABS(", arc_mid_x, ",", arc_top_height, ",", end_x, ",", end_safe_z, ")"
    ELSE
        PRINT "第二段：模拟MOVEABS(", end_x, ",", end_safe_z, ")"
    ENDIF
    
    PRINT "第三段：模拟MOVEABS(", end_x, ",", end_z, ")"
END SUB
