
'=============================================================================
' 任务0：主控制任务（本文件，包含所有共享函数）
' 任务1：回零任务（仅包含回零逻辑）
' 任务2：打螺丝任务（仅包含打螺丝逻辑）
'=============================================================================

'================ MODBUS寄存器地址映射 ================
'按照ZMC官方建议使用用户自定义区域（0-7999），避开系统保留地址
'
'控制寄存器（写入）：
'  MODBUS_BIT(100)    - 多轴回零触发（写1触发，自动清零）
'  MODBUS_BIT(101)    - 左侧工作启动（写1触发，自动清零）
'  MODBUS_BIT(102)    - 右侧工作启动（写1触发，自动清零）
'
'螺丝位置寄存器（读写）：
'  MODBUS_IEEE(2000+n*6+0) - 左边第n个点X坐标（n=0,1,2...）
'  MODBUS_IEEE(2000+n*6+1) - 左边第n个点Y坐标
'  MODBUS_IEEE(2000+n*6+2) - 左边第n个点Z坐标
'  MODBUS_IEEE(2000+n*6+3) - 右边第n个点X坐标
'  MODBUS_IEEE(2000+n*6+4) - 右边第n个点Y坐标
'  MODBUS_IEEE(2000+n*6+5) - 右边第n个点Z坐标
'  示例：第1个点 2000-2005，第2个点 2006-2011，第3个点 2012-2017...
'
'状态寄存器（只读）：
'  MODBUS_REG(1000)   - 系统状态（0-待机，1-运行，2-回零）
'  MODBUS_REG(1001-1011) - 系统运行状态
'  MODBUS_REG(1020-1027) - 各轴回零状态（轴0-7，0-未回零，1-回零中，2-已回零，3-失败）
'  MODBUS_REG(1040-1047) - 各轴启用状态（轴0-7，0-禁用，1-启用）
'  MODBUS_IEEE(1100-1107) - 各轴当前位置（轴0-7，单位：mm）
'
'螺丝位置寄存器（读写）：
'  MODBUS_IEEE(2000-2191) - 螺丝位置数据，按左右交替存放
'    格式：左1_X, 左1_Y, 左1_Z, 右1_X, 右1_Y, 右1_Z, 左2_X, 左2_Y, 左2_Z, 右2_X, 右2_Y, 右2_Z...
'    最多支持32对螺丝（64个螺丝点），共192个浮点数

'全局常量定义
GLOBAL CONST MAX_AXISNUM = 4        ' 最大轴数量

'全局变量定义
GLOBAL sys_status                  ' 系统状态：0-待机，1-运行中，2-回零中

'轴配置系统
'使用系统内置变量 AXIS_ENABLE(n) 控制轴启用状态：0-禁用，1-启用
'支持非连续轴配置，如：轴0、2、5启用，轴1、3、4禁用
GLOBAL DIM axis_home_mode(MAX_AXISNUM)       ' 轴回零模式：1-DATUM(1), 2-DATUM(2), 3-DATUM(3), 4-DATUM(4)
GLOBAL DIM axis_priority(MAX_AXISNUM)        ' 轴回零优先级：数字越大越先回零，相同优先级同时回零
GLOBAL DIM axis_home_stat(MAX_AXISNUM)       ' 轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL left_screw_num              ' 左侧螺丝点位数量（默认8个，最多64个）
GLOBAL right_screw_num             ' 右侧螺丝点位数量（默认8个，最多64个）
GLOBAL max_screw_num               ' 单侧最大螺丝数量
GLOBAL cur_screw                   ' 当前打螺丝序号

'双Y轴滑轨控制变量
GLOBAL left_user_pos               ' 左侧用户位置（靠近用户侧）
GLOBAL right_user_pos              ' 右侧用户位置（靠近用户侧）

GLOBAL left_slide_status           ' 左侧滑轨状态：0-用户侧，1-工作侧，2-移动中
GLOBAL right_slide_stat             ' 右侧滑轨状态：0-用户侧，1-工作侧，2-移动中

'队列控制变量
GLOBAL left_queue                   ' 左侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL right_queue                  ' 右侧队列状态：0-无任务，1-等待中，2-执行中
GLOBAL screwdriver_busy             ' 电批忙碌状态：0-空闲，1-忙碌

'任务状态变量
GLOBAL task_home_running            ' 回零任务运行状态
GLOBAL task_screw_run               ' 打螺丝任务运行状态

'打螺丝任务控制变量
GLOBAL screw_task_stop              ' 打螺丝任务停止标志：0-继续运行，1-停止

'螺丝位置数据存储（每个螺丝3个数据：X,Y,Z）
GLOBAL left_start                   ' 左侧螺丝位置数据起始地址（0-191）
GLOBAL right_start                  ' 右侧螺丝位置数据起始地址（200-391）
'数据布局：64个螺丝 × 3个坐标 = 192个数据位置

'吸螺丝位置（固定位置，与Y轴无关）
GLOBAL pick_x                       ' 吸螺丝位置X
GLOBAL pick_z                       ' 吸螺丝位置Z（往下为正）

'Z轴高度设置（注意：安全高度必须大于0，系统强制使用三段插补）
GLOBAL screw_work_height            ' 打螺丝工作高度30mm
GLOBAL pick_safe_height             ' 吸螺丝位置安全高度8mm（必须>0）
GLOBAL work_safe_height             ' 工件位置安全高度25mm（必须>0）
GLOBAL arc_top_height               ' 圆弧插补最高点Z轴高度20mm

'主程序（任务0）
CALL InitSystem()
CALL SetupAxis()
CALL SetupData()

PRINT "=== 螺丝机多线程简化版启动 ==="
PRINT "简化的多任务架构："
PRINT "任务0：主控制（输入扫描、状态管理、所有共享函数）"
PRINT "任务1：回零任务（仅回零逻辑）"
PRINT "任务2：打螺丝任务（仅打螺丝逻辑）"
PRINT ""
PRINT "操作说明："
PRINT "IN0 - 系统回零"
PRINT "IN1 - 左侧开始（可在任何时候按下）"
PRINT "IN2 - 右侧开始（可在任何时候按下）"
PRINT "IN3 - 急停"
PRINT "IN4 - 手动左Y轴到用户侧"
PRINT "IN5 - 手动右Y轴到用户侧"

'启动打螺丝任务（持续运行）
PRINT "启动打螺丝任务..."
screw_task_stop = 0
RUNTASK 2, SimpleScrewTask
task_screw_run = 1

'主循环（任务0持续运行）
WHILE 1
    CALL ScanInput()                ' 扫描输入信号
    CALL UpdateTaskStatus()         ' 更新任务状态
    CALL UpdateStatus()             ' 更新系统状态
    DELAY(50)                       ' 50ms扫描周期
WEND
END

'================ 系统初始化 ================
GLOBAL SUB InitSystem()
    sys_status = 0
    left_screw_num = 2              ' 默认左侧8个螺丝
    right_screw_num = 2             ' 默认右侧8个螺丝
    max_screw_num = 64              ' 单侧最大螺丝数量
    cur_screw = 0
    left_user_pos = 5               ' 左侧用户位置
    right_user_pos = 5              ' 右侧用户位置
    left_slide_status = 0           ' 左侧滑轨状态
    right_slide_stat = 0            ' 右侧滑轨状态

    '初始化队列状态
    left_queue = 0
    right_queue = 0
    screwdriver_busy = 0

    '初始化任务状态
    task_home_running = 0
    task_screw_run = 0
    screw_task_stop = 0

    '初始化螺丝位置数据起始地址
    left_start = 0                  ' 左侧螺丝位置数据起始地址（0-191）
    right_start = 200               ' 右侧螺丝位置数据起始地址（200-391）

    '初始化吸螺丝位置
    pick_x = 330                     ' 吸螺丝位置X
    pick_z = 30                     ' 吸螺丝位置Z（往下为正）

    '初始化Z轴高度设置
    screw_work_height = 30          ' 打螺丝工作高度30mm
    pick_safe_height = 15           ' 吸螺丝位置安全高度25mm（必须>0）
    work_safe_height = 15           ' 工件位置安全高度25mm（必须>0）
    arc_top_height = 5             ' 圆弧插补最高点Z轴高度20mm

    '初始化并行运动控制变量
    next_y_target = 0               ' 下一个目标Y位置
    next_y_axis = 0                 ' 下一个目标Y轴编号
    y_move_started = 0              ' Y轴移动是否已启动
    is_last_screw = 0               ' 是否是最后一个螺丝
    user_pos_target = 0             ' 用户位置目标值

    '初始化所有轴配置（支持最多MAX_AXISNUM轴）
    FOR i = 0 TO MAX_AXISNUM - 1
        AXIS_ENABLE(i) = 0          ' 使用系统变量，默认禁用
        axis_home_mode(i) = 4       ' 默认使用DATUM(4)负向原点开关
        axis_priority(i) = -1       ' 默认无效优先级
        axis_home_stat(i) = 0       ' 默认未回零
    NEXT

    '配置当前使用的轴（支持非连续轴配置）
    '轴0 (X轴)
    AXIS_ENABLE(0) = 1              ' 使用系统变量启用
    axis_home_mode(0) = 4           ' DATUM(4)负向原点开关
    axis_priority(0) = 0            ' 优先级0（第二批）

    '轴1 (Y1轴)
    AXIS_ENABLE(1) = 1              ' 使用系统变量启用
    axis_home_mode(1) = 4           ' DATUM(4)负向原点开关
    axis_priority(1) = 0            ' 优先级0（第二批）

    '轴2 (Y2轴)
    AXIS_ENABLE(2) = 1              ' 使用系统变量启用
    axis_home_mode(2) = 4           ' DATUM(4)负向原点开关
    axis_priority(2) = 0            ' 优先级0（第二批）

    '轴3 (Z轴)
    AXIS_ENABLE(3) = 1              ' 使用系统变量启用
    axis_home_mode(3) = 4           ' DATUM(4)负向原点开关
    axis_priority(3) = 1            ' 优先级1（第一批，最高优先级）

    '示例：非连续轴配置
    '如果需要启用轴0、2、5而禁用轴1、3、4，可以这样配置：
    'AXIS_ENABLE(0) = 1, AXIS_ENABLE(2) = 1, AXIS_ENABLE(5) = 1
    'AXIS_ENABLE(1) = 0, AXIS_ENABLE(3) = 0, AXIS_ENABLE(4) = 0

    CALL PrintAxisSummary()         ' 显示轴配置摘要

    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB SetupAxis()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0,1,2,3)
    ATYPE = 1,1,1,1                 ' 步进电机开环脉冲控制
    UNITS = 100,100,100,100       ' Y轴脉冲当量100脉冲/mm（10mm螺距）

    '根据官方手册，设置合适的MAX_SPEED避免脉冲频率超限
    MAX_SPEED = 500000,500000,500000,500000  ' 设置脉冲频率限制500kHz

    '计算安全速度：MAX_SPEED / UNITS = 最大速度
    'X轴: 500000/1000 = 500mm/s, Z轴: 500000/1000 = 500mm/s
    'Y轴: 500000/100 = 5000mm/s (太高，需要限制)
    SPEED = 100,100,100,100         ' 保守设置：所有轴100mm/s
    ACCEL = 100,100,100,100         ' 加速度100mm/s²
    DECEL = 100,100,100,100         ' 减速度100mm/s²
    CREEP = 5,5,5,3                 ' 回零爬行速度

    '打印轴编号对应关系
    PRINT "=== 轴编号对应关系 ==="
    PRINT "X轴：编号0"
    PRINT "Y1轴（左侧）：编号1"
    PRINT "Y2轴（右侧）：编号2"
    PRINT "Z轴：编号3"
    PRINT "====================="
    
    '限位和原点信号配置
    FWD_IN = 8,9,10,11              ' 正限位 IN8-IN11
    REV_IN = 12,13,14,15            ' 负限位 IN12-IN15
    DATUM_IN = 16,17,18,19          ' 原点开关 IN16-IN19
    
    '信号反转(根据实际硬件调整)
    FOR i = 0 TO 19
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    '注意：LOOKAHEAD和BLEND_TOL可能不是所有控制器都支持

    'S型曲线设置（平滑运动曲线）
    SRAMP = 100                     ' S型曲线平滑时间100ms（0-250ms范围）

    '圆弧插补设置（如果控制器支持）
    '注意：ARC_MODE和ARC_RADIUS可能不是所有控制器都支持
    'ARC_MODE = 1                    ' 启用圆弧插补模式
    'ARC_RADIUS = 5                  ' 默认圆弧半径5mm（可根据需要调整）

    '说明：所有运动都将使用XZ平面圆弧插补，Y轴独立移动
    '圆弧最高点固定在Z=20mm，实现平滑的弧形轨迹
    
    PRINT "轴参数设置完成（Y轴适配10mm螺距，运动速度1m/s，S型曲线100ms，标准三段轨迹）"
    PRINT "安全高度设置：取螺丝=", pick_safe_height, "mm，工件=", work_safe_height, "mm（必须>0）"
END SUB

'================ 数据设置 ================
GLOBAL SUB SetupData()
    '使用MODBUS寄存器存放螺丝位置数据
    '格式：左边第n个点xyz+右边第n个点xyz
    '地址：MODBUS_IEEE(2000+n*6+0到5)

    PRINT "初始化螺丝位置数据到MODBUS寄存器..."

    '根据实际机械尺寸设置默认点位：
    'X轴总行程：580mm，吸螺丝位置：330mm
    '左边第一个点X：130mm，右边第一个点X：530mm（对称分布在吸螺丝两边）
    '左右第一个点Y坐标都是：200mm

    '第1个点（n=0，地址2000-2005）
    MODBUS_IEEE(2000) = 130         ' 左边第1个点X=130（吸螺丝左侧）
    MODBUS_IEEE(2001) = 200         ' 左边第1个点Y=200
    MODBUS_IEEE(2002) = screw_work_height  ' 左边第1个点Z=30
    MODBUS_IEEE(2003) = 530         ' 右边第1个点X=530（吸螺丝右侧）
    MODBUS_IEEE(2004) = 200         ' 右边第1个点Y=200
    MODBUS_IEEE(2005) = screw_work_height  ' 右边第1个点Z=30

    '第2个点（n=1，地址2006-2011）
    MODBUS_IEEE(2006) = 230         ' 左边第2个点X=230
    MODBUS_IEEE(2007) = 200         ' 左边第2个点Y=200
    MODBUS_IEEE(2008) = screw_work_height  ' 左边第2个点Z=30
    MODBUS_IEEE(2009) = 430         ' 右边第2个点X=430
    MODBUS_IEEE(2010) = 200         ' 右边第2个点Y=200
    MODBUS_IEEE(2011) = screw_work_height  ' 右边第2个点Z=30

    '第3个点（n=2，地址2012-2017）
    MODBUS_IEEE(2012) = 130         ' 左边第3个点X=130
    MODBUS_IEEE(2013) = 250         ' 左边第3个点Y=250
    MODBUS_IEEE(2014) = screw_work_height  ' 左边第3个点Z=30
    MODBUS_IEEE(2015) = 530         ' 右边第3个点X=530
    MODBUS_IEEE(2016) = 250         ' 右边第3个点Y=250
    MODBUS_IEEE(2017) = screw_work_height  ' 右边第3个点Z=30

    '第4个点（n=3，地址2018-2023）
    MODBUS_IEEE(2018) = 230         ' 左边第4个点X=230
    MODBUS_IEEE(2019) = 250         ' 左边第4个点Y=250
    MODBUS_IEEE(2020) = screw_work_height  ' 左边第4个点Z=30
    MODBUS_IEEE(2021) = 430         ' 右边第4个点X=430
    MODBUS_IEEE(2022) = 250         ' 右边第4个点Y=250
    MODBUS_IEEE(2023) = screw_work_height  ' 右边第4个点Z=30

    '第5个点（n=4，地址2024-2029）
    MODBUS_IEEE(2024) = 130         ' 左边第5个点X=130
    MODBUS_IEEE(2025) = 300         ' 左边第5个点Y=300
    MODBUS_IEEE(2026) = screw_work_height  ' 左边第5个点Z=30
    MODBUS_IEEE(2027) = 530         ' 右边第5个点X=530
    MODBUS_IEEE(2028) = 300         ' 右边第5个点Y=300
    MODBUS_IEEE(2029) = screw_work_height  ' 右边第5个点Z=30

    '第6个点（n=5，地址2030-2035）
    MODBUS_IEEE(2030) = 230         ' 左边第6个点X=230
    MODBUS_IEEE(2031) = 300         ' 左边第6个点Y=300
    MODBUS_IEEE(2032) = screw_work_height  ' 左边第6个点Z=30
    MODBUS_IEEE(2033) = 430         ' 右边第6个点X=430
    MODBUS_IEEE(2034) = 300         ' 右边第6个点Y=300
    MODBUS_IEEE(2035) = screw_work_height  ' 右边第6个点Z=30

    '第7个点（n=6，地址2036-2041）
    MODBUS_IEEE(2036) = 130         ' 左边第7个点X=130
    MODBUS_IEEE(2037) = 350         ' 左边第7个点Y=350
    MODBUS_IEEE(2038) = screw_work_height  ' 左边第7个点Z=30
    MODBUS_IEEE(2039) = 530         ' 右边第7个点X=530
    MODBUS_IEEE(2040) = 350         ' 右边第7个点Y=350
    MODBUS_IEEE(2041) = screw_work_height  ' 右边第7个点Z=30

    '第8个点（n=7，地址2042-2047）
    MODBUS_IEEE(2042) = 230         ' 左边第8个点X=230
    MODBUS_IEEE(2043) = 350         ' 左边第8个点Y=350
    MODBUS_IEEE(2044) = screw_work_height  ' 左边第8个点Z=30
    MODBUS_IEEE(2045) = 430         ' 右边第8个点X=430
    MODBUS_IEEE(2046) = 350         ' 右边第8个点Y=350
    MODBUS_IEEE(2047) = screw_work_height  ' 右边第8个点Z=30

    PRINT "螺丝位置数据初始化完成（存储在MODBUS_IEEE 2000-2047）"
    PRINT "机械参数：X轴行程580mm，吸螺丝位置330mm"
    PRINT "左侧螺丝数量：", left_screw_num, "个"
    PRINT "右侧螺丝数量：", right_screw_num, "个"
    PRINT "最大支持螺丝数量：", max_screw_num, "个/侧"
    PRINT "左侧X坐标：", MODBUS_IEEE(2000), "mm和", MODBUS_IEEE(2006), "mm（对称分布在吸螺丝左侧）"
    PRINT "右侧X坐标：", MODBUS_IEEE(2003), "mm和", MODBUS_IEEE(2009), "mm（对称分布在吸螺丝右侧）"
    PRINT "Y坐标分布：", MODBUS_IEEE(2001), "mm,", MODBUS_IEEE(2013), "mm,", MODBUS_IEEE(2025), "mm,", MODBUS_IEEE(2037), "mm"
    PRINT "左侧用户位置：", left_user_pos, "mm，右侧用户位置：", right_user_pos, "mm"
END SUB

'================ 螺丝位置获取函数 ================
GLOBAL SUB GetScrewPos(screw_index, side, coord)
    '获取螺丝位置坐标
    '参数：screw_index - 螺丝索引（0-7）
    '      side - 侧别（0-左侧，1-右侧）
    '      coord - 坐标（0-X，1-Y，2-Z）
    '返回：函数返回值 - 坐标值

    DIM base_addr
    base_addr = 2000 + screw_index * 6  ' 计算基地址

    IF side = 0 THEN
        '左侧：base_addr + coord
        RETURN MODBUS_IEEE(base_addr + coord)
    ELSE
        '右侧：base_addr + 3 + coord
        RETURN MODBUS_IEEE(base_addr + 3 + coord)
    ENDIF
END SUB

GLOBAL SUB SetScrewPos(screw_index, side, coord, value)
    '设置螺丝位置坐标
    '参数：screw_index - 螺丝索引（0-7）
    '      side - 侧别（0-左侧，1-右侧）
    '      coord - 坐标（0-X，1-Y，2-Z）
    '      value - 坐标值

    DIM base_addr
    base_addr = 2000 + screw_index * 6  ' 计算基地址

    IF side = 0 THEN
        '左侧：base_addr + coord
        MODBUS_IEEE(base_addr + coord) = value
    ELSE
        '右侧：base_addr + 3 + coord
        MODBUS_IEEE(base_addr + 3 + coord) = value
    ENDIF
END SUB

'================ 设置螺丝数量 ================
GLOBAL SUB SetScrewCount(left_count, right_count)
    '验证螺丝数量范围
    IF left_count < 1 OR left_count > max_screw_num THEN
        PRINT "错误：左侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", left_count
        RETURN
    ENDIF

    IF right_count < 1 OR right_count > max_screw_num THEN
        PRINT "错误：右侧螺丝数量必须在1-", max_screw_num, "之间，当前值：", right_count
        RETURN
    ENDIF

    '设置螺丝数量
    left_screw_num = left_count
    right_screw_num = right_count

    PRINT "螺丝数量设置完成："
    PRINT "左侧：", left_screw_num, "个螺丝"
    PRINT "右侧：", right_screw_num, "个螺丝"
    PRINT "总计：", left_screw_num + right_screw_num, "个螺丝"
END SUB

'================ 并行运动控制全局变量 ================
GLOBAL next_y_target                ' 下一个目标Y位置
GLOBAL next_y_axis                  ' 下一个目标Y轴编号
GLOBAL y_move_started               ' Y轴移动是否已启动
GLOBAL is_last_screw                ' 是否是最后一个螺丝
GLOBAL user_pos_target              ' 用户位置目标值

'================ 启动Y轴并行移动 ================
GLOBAL SUB StartYMove(y_axis, target_y, last_flag)
    next_y_target = target_y
    next_y_axis = y_axis
    is_last_screw = last_flag
    y_move_started = 1

    IF last_flag = 1 THEN
        '最后一个螺丝，移动到用户位置
        IF y_axis = 1 THEN
            user_pos_target = left_user_pos
        ELSE
            user_pos_target = right_user_pos
        ENDIF
        PRINT "启动Y轴并行移动到用户位置：", user_pos_target, "mm"
    ELSE
        PRINT "启动Y轴并行移动到下一个孔位：", target_y, "mm"
    ENDIF

    BASE(y_axis)
    IF last_flag = 1 THEN
        MOVEABS(user_pos_target) AXIS(y_axis)
    ELSE
        MOVEABS(target_y) AXIS(y_axis)
    ENDIF
END SUB

'================ 等待Y轴移动完成 ================
GLOBAL SUB WaitForYMove()
    IF y_move_started = 1 THEN
        WAIT IDLE(next_y_axis)
        IF is_last_screw = 1 THEN
            PRINT "Y轴已到达用户位置：", user_pos_target, "mm"
        ELSE
            PRINT "Y轴已到达下一个孔位：", next_y_target, "mm"
        ENDIF
        y_move_started = 0
    ENDIF
END SUB

'================ 输入扫描（任务0持续执行）================
GLOBAL SUB ScanInput()
    '系统回零触发检测
    DIM home_trigger
    home_trigger = 0

    '方式1：IN0输入触发（保留原有功能）
    IF SCAN_EVENT(IN(0)) > 0 THEN
        home_trigger = 1
        PRINT "检测到IN0输入触发回零"
    ENDIF

    '方式2：MODBUS寄存器触发（新增功能）
    IF MODBUS_BIT(100) = 1 THEN
        MODBUS_BIT(100) = 0             ' 清除触发标志
        home_trigger = 1
        PRINT "检测到MODBUS寄存器触发回零"
    ENDIF

    '执行回零任务（共用逻辑）
    IF home_trigger = 1 THEN
        IF task_home_running = 0 THEN
            PRINT "开始系统回零"
            STOPTASK 1                  ' 停止可能存在的回零任务
            RUNTASK 1, SimpleHomeTask   ' 启动回零任务
            task_home_running = 1
        ELSE
            PRINT "回零任务正在运行中"
        ENDIF
    ENDIF

    '左侧工作启动触发检测
    DIM left_trigger
    left_trigger = 0

    '方式1：IN1输入触发（保留原有功能）
    IF SCAN_EVENT(IN(1)) > 0 THEN
        left_trigger = 1
        PRINT "检测到IN1输入触发左侧工作"
    ENDIF

    '方式2：MODBUS寄存器触发（新增功能）
    IF MODBUS_BIT(101) = 1 THEN
        MODBUS_BIT(101) = 0             ' 清除触发标志
        left_trigger = 1
        PRINT "检测到MODBUS寄存器触发左侧工作"
    ENDIF

    '执行左侧工作任务（共用逻辑）
    IF left_trigger = 1 THEN
        CALL CheckHome()
        IF RETURN = 1 THEN
            PRINT "左侧作业请求"
            IF left_queue = 0 THEN
                left_queue = 1          ' 设置为等待状态
                PRINT "左侧任务加入队列"
                CALL LeftSlideToWork()  ' 左Y轴移动到工作位置
            ELSE
                PRINT "左侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF

    '右侧工作启动触发检测
    DIM right_trigger
    right_trigger = 0

    '方式1：IN2输入触发（保留原有功能）
    IF SCAN_EVENT(IN(2)) > 0 THEN
        right_trigger = 1
        PRINT "检测到IN2输入触发右侧工作"
    ENDIF

    '方式2：MODBUS寄存器触发（新增功能）
    IF MODBUS_BIT(102) = 1 THEN
        MODBUS_BIT(102) = 0             ' 清除触发标志
        right_trigger = 1
        PRINT "检测到MODBUS寄存器触发右侧工作"
    ENDIF

    '执行右侧工作任务（共用逻辑）
    IF right_trigger = 1 THEN
        CALL CheckHome()
        IF RETURN = 1 THEN
            PRINT "右侧作业请求"
            IF right_queue = 0 THEN
                right_queue = 1         ' 设置为等待状态
                PRINT "右侧任务加入队列"
                CALL RightSlideToWork() ' 右Y轴移动到工作位置
            ELSE
                PRINT "右侧任务已在队列中"
            ENDIF
        ELSE
            PRINT "系统未回零，无法开始作业"
        ENDIF
    ENDIF
    '手动左Y轴到用户侧
    IF SCAN_EVENT(IN(4)) > 0 THEN
        IF left_queue = 0 THEN
            PRINT "手动左Y轴到用户侧"
            CALL LeftSlideToUser()
        ELSE
            PRINT "左侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '手动右Y轴到用户侧
    IF SCAN_EVENT(IN(5)) > 0 THEN
        IF right_queue = 0 THEN
            PRINT "手动右Y轴到用户侧"
            CALL RightSlideToUser()
        ELSE
            PRINT "右侧有任务，无法手动控制"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        
        '停止所有任务
        STOPTASK 1                  ' 停止回零任务
        screw_task_stop = 1         ' 通知打螺丝任务停止
        
        '清除所有状态
        sys_status = 0
        left_queue = 0
        right_queue = 0
        screwdriver_busy = 0
        task_home_running = 0
        
        OP(0, OFF)                  ' 关闭吸螺丝
        PRINT "所有任务已停止"
        
        '重新启动打螺丝任务
        DELAY(1000)
        screw_task_stop = 0
        STOPTASK 2
        RUNTASK 2, SimpleScrewTask
        task_screw_run = 1
        PRINT "打螺丝任务已重启"
    ENDIF
END SUB

'================ 更新任务状态 ================
GLOBAL SUB UpdateTaskStatus()
    '检查回零任务状态
    IF task_home_running = 1 THEN
        IF PROC_STATUS(1) = 0 THEN      ' 任务1已停止
            PRINT "回零任务完成"
            task_home_running = 0
            sys_status = 0              ' 回到待机状态
        ENDIF
    ENDIF

    '检查打螺丝任务状态
    IF task_screw_run = 1 THEN
        IF PROC_STATUS(2) = 0 THEN      ' 任务2已停止
            PRINT "打螺丝任务异常停止，重新启动"
            screw_task_stop = 0
            RUNTASK 2, SimpleScrewTask
            task_screw_run = 1
        ENDIF
    ENDIF
END SUB

'================ 双Y轴滑轨控制 ================
'强制移动到用户位置（找零点后使用，忽略状态检查）
GLOBAL SUB ForceLeftToUser()
    PRINT "左Y轴强制移动到用户位置..."
    left_slide_status = 2       ' 设置为移动中

    'Y轴直线移动到用户位置
    BASE(1)
    MOVEABS(left_user_pos) AXIS(1)
    WAIT IDLE(1)

    left_slide_status = 0       ' 设置为用户侧
    PRINT "左Y轴已强制到达用户位置：", left_user_pos, "mm，实际位置：", DPOS(1), "mm"
END SUB

GLOBAL SUB ForceRightToUser()
    PRINT "右Y轴强制移动到用户位置..."
    right_slide_stat = 2        ' 设置为移动中

    'Y轴直线移动到用户位置
    BASE(2)
    MOVEABS(right_user_pos) AXIS(2)
    WAIT IDLE(2)

    right_slide_stat = 0        ' 设置为用户侧
    PRINT "右Y轴已强制到达用户位置：", right_user_pos, "mm，实际位置：", DPOS(2), "mm"
END SUB
GLOBAL SUB LeftSlideToUser()
    IF left_slide_status <> 0 THEN
        PRINT "左Y轴移动到用户位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(1)
        MOVEABS(left_user_pos) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 0       ' 设置为用户侧
        PRINT "左Y轴已到达用户位置：", left_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB LeftSlideToWork()
    IF left_slide_status <> 1 THEN
        PRINT "左Y轴移动到第一个螺丝位置..."
        left_slide_status = 2       ' 设置为移动中

        'Y轴直线移动到第一个螺丝的实际Y坐标
        DIM first_screw_y
        CALL GetScrewPos(0, 0, 1)               ' 获取左侧第1个螺丝的Y坐标
        first_screw_y = RETURN
        BASE(1)
        MOVEABS(first_screw_y) AXIS(1)
        WAIT IDLE(1)

        left_slide_status = 1       ' 设置为工作侧
        PRINT "左Y轴已到达第一个螺丝位置：", first_screw_y, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToUser()
    IF right_slide_stat <> 0 THEN
        PRINT "右Y轴移动到用户位置..."
        right_slide_stat = 2        ' 设置为移动中

        'Y轴直线移动到用户位置
        BASE(2)
        MOVEABS(right_user_pos) AXIS(2)
        WAIT IDLE(2)

        right_slide_stat = 0        ' 设置为用户侧
        PRINT "右Y轴已到达用户位置：", right_user_pos, "mm"
    ENDIF
END SUB

GLOBAL SUB RightSlideToWork()
    IF right_slide_stat <> 1 THEN
        PRINT "右Y轴移动到第一个螺丝位置..."
        right_slide_stat = 2        ' 设置为移动中

        'Y轴直线移动到第一个螺丝的实际Y坐标
        DIM first_screw_y
        CALL GetScrewPos(0, 1, 1)               ' 获取右侧第1个螺丝的Y坐标
        first_screw_y = RETURN
        BASE(2)
        MOVEABS(first_screw_y) AXIS(2)
        WAIT IDLE(2)

        right_slide_stat = 1        ' 设置为工作侧
        PRINT "右Y轴已到达第一个螺丝位置：", first_screw_y, "mm"
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckHome()
    FOR i = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(i) = 1 AND axis_home_stat(i) <> 2 THEN
            RETURN 0                ' 有启用的轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有启用的轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB UpdateStatus()
    '更新Modbus寄存器供HMI显示（使用高地址段避免冲突）
    MODBUS_REG(1000) = sys_status
    MODBUS_REG(1001) = left_screw_num
    MODBUS_REG(1002) = right_screw_num
    MODBUS_REG(1003) = cur_screw
    MODBUS_REG(1004) = left_slide_status
    MODBUS_REG(1005) = right_slide_stat
    MODBUS_REG(1006) = left_queue
    MODBUS_REG(1007) = right_queue
    MODBUS_REG(1008) = screwdriver_busy
    MODBUS_REG(1009) = task_home_running
    MODBUS_REG(1010) = task_screw_run
    MODBUS_REG(1011) = screw_task_stop

    '更新各轴回零状态
    FOR i = 0 TO MAX_AXISNUM - 1
        MODBUS_REG(1020 + i) = axis_home_stat(i)
    NEXT

    '更新各轴当前位置
    FOR i = 0 TO MAX_AXISNUM - 1
        MODBUS_IEEE(1100 + i) = DPOS(i)
    NEXT

    '更新轴启用状态到MODBUS寄存器（只读）
    FOR i = 0 TO MAX_AXISNUM - 1
        MODBUS_REG(1040 + i) = AXIS_ENABLE(i)
    NEXT
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", sys_status, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", cur_screw
    PRINT "左侧螺丝数量：", left_screw_num
    PRINT "右侧螺丝数量：", right_screw_num

    PRINT "=== 双Y轴滑轨状态 ==="
    PRINT "左Y轴状态：", left_slide_status, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "左Y轴位置：", DPOS(1), " mm"
    PRINT "右Y轴状态：", right_slide_stat, " (0-用户侧,1-工作侧,2-移动中)"
    PRINT "右Y轴位置：", DPOS(2), " mm"

    PRINT "=== 队列状态 ==="
    PRINT "左侧队列：", left_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "右侧队列：", right_queue, " (0-无任务,1-等待,2-执行中)"
    PRINT "电批状态：", screwdriver_busy, " (0-空闲,1-忙碌)"

    PRINT "=== 任务运行状态 ==="
    PRINT "回零任务：", task_home_running, " (0-停止,1-运行)"
    PRINT "打螺丝任务：", task_screw_run, " (0-停止,1-运行)"
    PRINT "任务停止标志：", screw_task_stop, " (0-继续,1-停止)"

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(i) = 1 THEN
            PRINT "轴", i, "：", axis_home_stat(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
        ELSE
            PRINT "轴", i, "：禁用"
        ENDIF
    NEXT
END SUB

'================ 简化回零任务（任务1执行）================
GLOBAL SUB SimpleHomeTask()
    sys_status = 2                  ' 设置为回零状态
    PRINT "开始多轴回零..."

    '显示轴配置信息
    CALL PrintAxisConfig()

    '执行优先级回零
    CALL ExecPriorHome()

    PRINT "回零任务完成"
    '任务结束，sys_status将在UpdateTaskStatus中设置为0
END SUB

'================ 优先级回零执行函数 ================
GLOBAL SUB ExecPriorHome()
    '找出最高优先级（数字最大）
    DIM max_priority, min_priority, current_priority
    max_priority = -1
    min_priority = 999

    '扫描所有启用的轴，找出优先级范围
    FOR axis_idx = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(axis_idx) = 1 AND axis_priority(axis_idx) >= 0 THEN
            IF axis_priority(axis_idx) > max_priority THEN max_priority = axis_priority(axis_idx)
            IF axis_priority(axis_idx) < min_priority THEN min_priority = axis_priority(axis_idx)
        ENDIF
    NEXT

    IF max_priority < 0 THEN
        PRINT "没有启用的轴需要回零"
        RETURN
    ENDIF

    PRINT "优先级范围：", min_priority, " 到 ", max_priority

    '从最高优先级开始，逐级回零
    FOR current_priority = max_priority TO min_priority STEP -1
        PRINT "=== 执行优先级 ", current_priority, " 的轴回零 ==="

        '找出当前优先级的所有启用轴并启动回零
        DIM axis_count
        axis_count = 0

        FOR axis_idx = 0 TO MAX_AXISNUM - 1
            IF AXIS_ENABLE(axis_idx) = 1 AND axis_priority(axis_idx) = current_priority THEN
                PRINT "轴", axis_idx, "加入当前批次，回零模式：DATUM(", axis_home_mode(axis_idx), ")"

                '清除轴错误状态
                BASE(axis_idx)
                DATUM(0) AXIS(axis_idx)
                axis_home_stat(axis_idx) = 1
                axis_count = axis_count + 1
            ENDIF
        NEXT

        IF axis_count > 0 THEN
            DELAY(200)                  ' 等待错误清除

            '启动当前优先级的所有轴回零
            FOR axis_idx = 0 TO MAX_AXISNUM - 1
                IF AXIS_ENABLE(axis_idx) = 1 AND axis_priority(axis_idx) = current_priority THEN
                    BASE(axis_idx)
                    '根据轴配置使用相应的回零模式
                    IF axis_home_mode(axis_idx) = 1 THEN
                        DATUM(1) AXIS(axis_idx)
                    ELSEIF axis_home_mode(axis_idx) = 2 THEN
                        DATUM(2) AXIS(axis_idx)
                    ELSEIF axis_home_mode(axis_idx) = 3 THEN
                        DATUM(3) AXIS(axis_idx)
                    ELSE
                        DATUM(4) AXIS(axis_idx)     ' 默认使用DATUM(4)
                    ENDIF
                    PRINT "启动轴", axis_idx, "回零，模式：DATUM(", axis_home_mode(axis_idx), ")"
                ENDIF
            NEXT

            '监控当前优先级的轴回零状态
            CALL MonitorAxisHome(current_priority)

            '检查是否有轴回零失败
            DIM failed_axis
            failed_axis = -1
            FOR axis_idx = 0 TO MAX_AXISNUM - 1
                IF AXIS_ENABLE(axis_idx) = 1 AND axis_priority(axis_idx) = current_priority AND axis_home_stat(axis_idx) = 3 THEN
                    failed_axis = axis_idx
                    EXIT FOR
                ENDIF
            NEXT

            IF failed_axis >= 0 THEN
                PRINT "轴", failed_axis, "回零失败，停止后续回零"
                RETURN
            ENDIF
        ENDIF
    NEXT

    PRINT "所有优先级回零完成！"

    '回零完成后，移动到初始位置
    PRINT "回零完成，移动到初始位置..."
    CALL MoveToInitPos()
END SUB

'================ 轴状态监控函数 ================
GLOBAL SUB MonitorAxisHome(priority)
    PRINT "开始监控优先级", priority, "的轴回零状态..."

    '创建完成标志数组
    DIM done_flag(MAX_AXISNUM), all_done, axis_status
    FOR axis_idx = 0 TO MAX_AXISNUM - 1
        done_flag(axis_idx) = 0
    NEXT

    '轮询监控直到所有轴完成
    WHILE 1
        all_done = 1

        FOR axis_idx = 0 TO MAX_AXISNUM - 1
            IF AXIS_ENABLE(axis_idx) = 1 AND axis_priority(axis_idx) = priority AND done_flag(axis_idx) = 0 THEN
                all_done = 0

                '检查轴是否停止运动
                IF IDLE(axis_idx) = -1 THEN
                    axis_status = AXISSTATUS(axis_idx)
                    CALL PrintAxisStatus(axis_idx, axis_status)

                    '直接在这里处理轴状态，避免参数传递
                    IF axis_status = 0 THEN
                        '回零成功
                        DPOS(axis_idx) = 0
                        MPOS(axis_idx) = 0
                        axis_home_stat(axis_idx) = 2
                        PRINT ">>> 轴", axis_idx, "回零成功 [OK]"
                        done_flag(axis_idx) = 1
                    ELSEIF axis_status = 64 THEN
                        '仍在找原点中
                        PRINT ">>> 轴", axis_idx, "仍在找原点中，继续等待..."
                        '不设置done_flag，继续监控
                    ELSEIF axis_status = 4160 THEN
                        '脉冲频率超限，尝试重试
                        PRINT ">>> 轴", axis_idx, "速度超限，尝试清除错误..."
                        BASE(axis_idx)
                        DATUM(0) AXIS(axis_idx)         ' 清除错误状态
                        DELAY(500)
                        '根据轴配置重新启动回零
                        IF axis_home_mode(axis_idx) = 1 THEN
                            DATUM(1) AXIS(axis_idx)
                        ELSEIF axis_home_mode(axis_idx) = 2 THEN
                            DATUM(2) AXIS(axis_idx)
                        ELSEIF axis_home_mode(axis_idx) = 3 THEN
                            DATUM(3) AXIS(axis_idx)
                        ELSE
                            DATUM(4) AXIS(axis_idx)
                        ENDIF
                        PRINT ">>> 轴", axis_idx, "重新启动回零"
                        '不设置done_flag，继续监控
                    ELSE
                        '其他错误状态
                        axis_home_stat(axis_idx) = 3
                        PRINT ">>> 轴", axis_idx, "回零失败 [FAIL]，状态：", HEX(axis_status)
                        done_flag(axis_idx) = 1
                    ENDIF
                ENDIF
            ENDIF
        NEXT

        IF all_done = 1 THEN EXIT WHILE
        DELAY(50)                       ' 50ms轮询间隔
    WEND

    PRINT "优先级", priority, "的轴回零监控完成！"
END SUB

'================ 轴配置显示函数 ================
GLOBAL SUB PrintAxisSummary()
    '统计启用的轴数量
    DIM enabled_count
    enabled_count = 0

    FOR i = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(i) = 1 THEN
            enabled_count = enabled_count + 1
        ENDIF
    NEXT

    PRINT "轴配置完成：", enabled_count, "轴启用（最大支持", MAX_AXISNUM, "轴）"
    PRINT "启用的轴编号："
    FOR i = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(i) = 1 THEN
            PRINT "  轴", i, "：优先级", axis_priority(i), "，DATUM(", axis_home_mode(i), ")"
        ENDIF
    NEXT
END SUB

GLOBAL SUB PrintAxisConfig()
    PRINT "=== 系统轴配置信息 ==="
    PRINT "最大支持轴数：", MAX_AXISNUM

    '统计并显示启用的轴
    DIM enabled_count
    enabled_count = 0
    FOR i = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(i) = 1 THEN
            enabled_count = enabled_count + 1
        ENDIF
    NEXT
    PRINT "当前启用轴数：", enabled_count

    PRINT "轴配置详情："
    FOR axis_idx = 0 TO MAX_AXISNUM - 1
        IF AXIS_ENABLE(axis_idx) = 1 THEN
            PRINT "轴", axis_idx, "：启用，优先级=", axis_priority(axis_idx), "，回零模式=DATUM(", axis_home_mode(axis_idx), ")"
        ENDIF
    NEXT
END SUB

'================ 轴状态显示函数 ================
GLOBAL SUB PrintAxisStatus(axis_num, status_code)
    '直接使用轴号显示，避免字符串赋值问题
    IF axis_num = 0 THEN
        PRINT ">>> X轴运动停止，状态：", HEX(status_code)
    ELSEIF axis_num = 1 THEN
        PRINT ">>> Y1轴运动停止，状态：", HEX(status_code)
    ELSEIF axis_num = 2 THEN
        PRINT ">>> Y2轴运动停止，状态：", HEX(status_code)
    ELSEIF axis_num = 3 THEN
        PRINT ">>> Z轴运动停止，状态：", HEX(status_code)
    ELSE
        PRINT ">>> 轴", axis_num, "运动停止，状态：", HEX(status_code)
    ENDIF
END SUB



'================ 移动到初始位置函数 ================
GLOBAL SUB MoveToInitPos()
    '双Y轴强制移动到用户位置（找零点后）
    PRINT "双Y轴强制移动到用户位置..."
    CALL ForceLeftToUser()
    CALL ForceRightToUser()

    'X轴移动到吸螺丝位置
    PRINT "X轴移动到吸螺丝位置：", pick_x, "mm"
    BASE(0)                         ' X轴
    MOVEABS(pick_x) AXIS(0)
    WAIT IDLE(0)

    'Z轴移动到吸螺丝安全高度
    PRINT "Z轴移动到吸螺丝安全高度：", pick_safe_height, "mm"
    BASE(3)                         ' Z轴
    MOVEABS(pick_safe_height) AXIS(3)
    WAIT IDLE(3)

    PRINT "所有轴回零完成，已移动到初始工作位置"
    PRINT "当前实际位置：X=", DPOS(0), "mm, Y1=", DPOS(1), "mm, Y2=", DPOS(2), "mm, Z=", DPOS(3), "mm"
    PRINT "目标用户位置：Y1=", left_user_pos, "mm, Y2=", right_user_pos, "mm"
END SUB

'================ 简化打螺丝任务（任务2执行）================
GLOBAL SUB SimpleScrewTask()
    PRINT "打螺丝任务启动，开始监控左右两侧队列"

    '持续监控左右两侧队列
    WHILE screw_task_stop = 0
        '检查电批是否空闲
        IF screwdriver_busy = 0 THEN
            '电批空闲，检查是否有等待的任务
            IF left_queue = 1 AND left_slide_status = 1 THEN
                '左侧任务等待中且左Y轴在工作位置
                PRINT "开始执行左侧打螺丝"
                left_queue = 2          ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteLeftScrew()

            ELSEIF right_queue = 1 AND right_slide_stat = 1 THEN
                '右侧任务等待中且右Y轴在工作位置
                PRINT "开始执行右侧打螺丝"
                right_queue = 2         ' 设置为执行中
                screwdriver_busy = 1    ' 电批设为忙碌
                CALL ExecuteRightScrew()
            ENDIF
        ENDIF

        DELAY(10)                      ' 10ms检查周期
    WEND

    PRINT "打螺丝任务停止"
END SUB

'================ 执行左侧打螺丝 ================
GLOBAL SUB ExecuteLeftScrew()
    PRINT "执行左侧打螺丝任务"

    '获取第一个螺丝的实际Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(left_start + 1)       ' 第一个螺丝的实际Y坐标
    PRINT "左Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：", first_screw_y, "mm"
    BASE(1)                                     ' Y1轴
    MOVEABS(first_screw_y) AXIS(1)
    WAIT IDLE(1)

    FOR screw_idx = 0 TO left_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，左侧任务中断"
            GOTO left_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（左侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(left_start + screw_idx * 3)
        screw_y = TABLE(left_start + screw_idx * 3 + 1)
        screw_z = TABLE(left_start + screw_idx * 3 + 2)

        '执行打螺丝流程（直接在调用中计算下一个位置和最后标志）
        IF screw_idx < left_screw_num - 1 THEN
            '不是最后一个螺丝，获取下一个螺丝的Y位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 1, TABLE(left_start + (screw_idx + 1) * 3 + 1), 0)
        ELSE
            '是最后一个螺丝，移动到用户位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 1, left_user_pos, 1)
        ENDIF

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    left_task_end:
    PRINT "左侧打螺丝任务完成"

    '任务完成后的处理
    left_queue = 0                  ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '左Y轴回到用户位置
    CALL LeftSlideToUser()
    PRINT "左侧任务完成，左Y轴已回到用户位置"
END SUB

'================ 执行右侧打螺丝 ================
GLOBAL SUB ExecuteRightScrew()
    PRINT "执行右侧打螺丝任务"

    '获取第一个螺丝的实际Y坐标，Y轴直接移动到第一个孔位
    DIM first_screw_y
    first_screw_y = TABLE(right_start + 1)      ' 第一个螺丝的实际Y坐标
    PRINT "右Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：", first_screw_y, "mm"
    BASE(2)                                     ' Y2轴
    MOVEABS(first_screw_y) AXIS(2)
    WAIT IDLE(2)

    FOR screw_idx = 0 TO right_screw_num - 1
        '检查停止标志
        IF screw_task_stop = 1 THEN
            PRINT "收到停止信号，右侧任务中断"
            GOTO right_task_end
        ENDIF

        cur_screw = screw_idx + 1
        PRINT "开始打第", cur_screw, "个螺丝（右侧）"

        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(right_start + screw_idx * 3)
        screw_y = TABLE(right_start + screw_idx * 3 + 1)
        screw_z = TABLE(right_start + screw_idx * 3 + 2)

        '执行打螺丝流程（直接在调用中计算下一个位置和最后标志）
        IF screw_idx < right_screw_num - 1 THEN
            '不是最后一个螺丝，获取下一个螺丝的Y位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 2, TABLE(right_start + (screw_idx + 1) * 3 + 1), 0)
        ELSE
            '是最后一个螺丝，移动到用户位置
            CALL WorkScrew(screw_x, screw_y, screw_z, 2, right_user_pos, 1)
        ENDIF

        'DELAY(500)                  ' 螺丝间延时
    NEXT

    right_task_end:
    PRINT "右侧打螺丝任务完成"

    '任务完成后的处理
    right_queue = 0                 ' 清除队列状态
    screwdriver_busy = 0            ' 电批设为空闲
    cur_screw = 0

    '右Y轴回到用户位置
    CALL RightSlideToUser()
    PRINT "右侧任务完成，右Y轴已回到用户位置"
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB WorkScrew(target_x, target_y, target_z, y_axis, next_y, last_flag)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 确保在取螺丝位置（统一的取螺丝位置）
    PRINT "确保在取螺丝位置"
    CALL EnsureAtPick(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(200)                     ' 等待吸取稳定

    '3. 三段连续轨迹到螺丝孔位（中间不停）
    PRINT "三段连续轨迹到螺丝孔位（中间不停）"
    CALL MoveToTarget(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 三段连续轨迹回到取螺丝位置（中间不停）
    PRINT "三段连续轨迹回到取螺丝位置（中间不停）"
    CALL MoveBackToPick(y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    '7. 安全移动Y轴到下一个位置（三段插补完成后）
    PRINT "安全移动Y轴到下一个位置"
    CALL StartYMove(y_axis, next_y, last_flag)
    CALL WaitForYMove()             ' 等待Y轴移动完成

    PRINT "螺丝完成"
END SUB

'================ 确保在取螺丝位置 ================
GLOBAL SUB EnsureAtPick(y_axis)
    PRINT "确保在取螺丝位置（吸螺丝器固定位置，不控制Y轴）"

    '注意：吸螺丝器在固定位置，不需要移动Y轴
    '批头固定在Z轴上，Z轴装在X轴上，Y轴移动的是工件位置

    '检查当前XZ位置，如果不在取螺丝位置则移动过去
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)

    '如果不在取螺丝位置，使用智能轨迹移动过去
    IF ABS(current_x - pick_x) > 1 OR ABS(current_z - pick_z) > 1 THEN
        PRINT "当前位置(", current_x, ",", current_z, ")，需要移动到取螺丝位置"

        '使用标准三段轨迹
        DIM start_safe, end_safe
        start_safe = work_safe_height
        end_safe = pick_safe_height

        CALL ThreeSegMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)
    ELSE
        PRINT "已在取螺丝位置：X=", pick_x, " Z=", pick_z, "（吸螺丝器固定位置）"
    ENDIF
END SUB

'================ 智能轨迹到目标位置（中间不停）================
GLOBAL SUB MoveToTarget(target_x, target_y, target_z, y_axis)
    PRINT "智能轨迹到螺丝孔位（从取螺丝位置出发，中间不停）"

    '检查Y轴是否需要移动到目标位置
    DIM current_y
    current_y = DPOS(y_axis)
    IF ABS(current_y - target_y) > 1 THEN
        '需要移动Y轴到目标位置
        BASE(y_axis)
        MOVEABS(target_y) AXIS(y_axis)
        WAIT IDLE(y_axis)
        PRINT "Y轴移动到目标位置：", target_y, "mm"
    ELSE
        PRINT "Y轴已在目标位置：", target_y, "mm，无需移动"
    ENDIF

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = pick_safe_height
    end_safe = work_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从取螺丝位置到螺丝孔位
    PRINT "从取螺丝位置(", pick_x, ",", pick_z, ")到螺丝孔位(", target_x, ",", target_z, ")"
    CALL ThreeSegMove(pick_x, pick_z, target_x, target_z, start_safe, end_safe)

    PRINT "到达螺丝孔位：X=", target_x, " Y=", target_y, " Z=", target_z
END SUB

'================ 智能轨迹回到取螺丝位置（中间不停）================
GLOBAL SUB MoveBackToPick(y_axis)
    PRINT "智能轨迹回到取螺丝位置（从螺丝孔位出发，中间不停）"

    '注意：Y轴移动已经在StartYMove中启动，这里不再处理Y轴
    '只处理XZ轴的三段轨迹

    '使用标准三段轨迹
    DIM start_safe, end_safe
    start_safe = work_safe_height
    end_safe = pick_safe_height
    PRINT "使用标准三段轨迹：起点安全高度=", start_safe, "mm，终点安全高度=", end_safe, "mm"

    '从螺丝孔位回到取螺丝位置
    DIM current_x, current_z
    current_x = DPOS(0)
    current_z = DPOS(3)
    PRINT "从螺丝孔位(", current_x, ",", current_z, ")回到取螺丝位置(", pick_x, ",", pick_z, ")"
    CALL ThreeSegMove(current_x, current_z, pick_x, pick_z, start_safe, end_safe)

    PRINT "回到取螺丝位置：X=", pick_x, " Z=", pick_z, "（吸螺丝器固定位置）"
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '三段插补完成后已经到达可以打螺丝的位置，直接控制电批即可
    PRINT "开始打螺丝（Z轴已在正确位置：", target_z, "mm）"

    '电批锁紧
    PRINT "电批开始锁紧"
    OP(1, ON)                       ' 开启电批
    DELAY(500)                     ' 锁紧2秒
    OP(1, OFF)                      ' 关闭电批
    PRINT "电批锁紧完成"

    PRINT "打螺丝完成，Z轴保持在工作位置"
END SUB

'================ 标准三段轨迹核心函数 ================
'标准三段轨迹：抬Z → 圆弧 → Z下降，安全高度必须大于0
GLOBAL SUB ThreeSegMove(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe)
    DIM move_dist_x, move_total
    DIM move_mid_x

    move_dist_x = end_x - start_x
    move_total = ABS(move_dist_x)

    PRINT "标准三段轨迹：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"
    PRINT "起点安全高度：", start_z_safe, "mm，终点安全高度：", end_z_safe, "mm"

    '验证安全高度必须大于0
    IF start_z_safe <= 0 OR end_z_safe <= 0 THEN
        PRINT "错误：安全高度必须大于0！起点=", start_z_safe, "，终点=", end_z_safe
        RETURN
    ENDIF

    '计算圆弧中间点
    move_mid_x = (start_x + end_x) / 2   ' X轴中点

    '执行标准三段轨迹（使用新的连续轨迹方式）
    CALL StdThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, move_mid_x, move_total)

    '不等待，让后续指令可以连续融合
    PRINT "标准三段轨迹已排入队列"
END SUB



'================ 连续轨迹预设（只在批次开始时做一次）================
GLOBAL SUB BeginContPath()
    '预设连续轨迹参数，整个批次保持连续插补
    BASE(0, 3)                      ' XZ两轴插补，X轴为主轴

    '设置VP_SPEED显示单轴速度，便于监控各轴分速度
    DIM original_zset
    original_zset = SYSTEM_ZSET     ' 保存原始设置
    SYSTEM_ZSET = original_zset AND (NOT 1)  ' 清除bit0，VP_SPEED使用单轴速度
    PRINT "设置VP_SPEED显示单轴速度，便于监控X轴和Z轴分速度"

    MERGE = ON                      ' 整个批次都保持连续插补
    CORNER_MODE = 32                ' 只倒角，不自动减速
    ZSMOOTH = 10                    ' 倒角半径放大到10mm，更平滑
    VP_MODE = 7,7                   ' SS曲线，最平滑的曲线类型（需要新固件支持）
    SRAMP = 150,150                 ' S曲线时间加长到150ms，更柔和
    FORCE_SPEED = 80                ' 统一行进速度80mm/s
    PRINT "开启连续轨迹批处理模式，整个批次无间断"
END SUB

'================ 结束连续轨迹（批次结束时调用）================
GLOBAL SUB EndContPath()
    '批次全部发送完毕后等待完成
    WAIT IDLE(0)                    ' 等待X轴完成
    WAIT IDLE(3)                    ' 等待Z轴完成
    MERGE = OFF                     ' 关闭连续插补

    '恢复VP_SPEED为默认的插补速度显示
    DIM current_zset
    current_zset = SYSTEM_ZSET
    SYSTEM_ZSET = current_zset OR 1  ' 设置bit0，恢复VP_SPEED插补速度显示
    PRINT "恢复VP_SPEED为插补速度显示"

    PRINT "连续轨迹批处理完成，整个批次平滑无间断"
END SUB

'================ 纯粹排指令的三段轨迹（调用多次，不等待）================
GLOBAL SUB PushThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    '纯粹排指令，不做任何等待或参数修改，让控制器自行融合
    PRINT "排入三段轨迹：", start_x, ",", start_z, " → ", end_x, ",", end_z
    PRINT "三段轨迹详细："
    PRINT "  第一段：垂直抬Z (", start_x, ",", start_z, ") → (", start_x, ",", start_z_safe, ")"
    PRINT "  第二段：水平移动 (", start_x, ",", start_z_safe, ") → (", end_x, ",", end_z_safe, ")"
    PRINT "  第三段：垂直下降 (", end_x, ",", end_z_safe, ") → (", end_x, ",", end_z, ")"

    '第一段：垂直抬Z到起点安全高度（现在是真正的垂直运动）
    MOVEABS(start_x, start_z_safe)          ' X不变（已经在start_x），只抬Z

    '第二段：在安全高度之间移动（圆弧或直线）
    IF std_dist >= 5 THEN
        '距离较大时使用圆弧插补，更平滑
        MOVECIRC2ABS(std_mid_x, arc_top_height, end_x, end_z_safe)
    ELSE
        '距离较小时使用直线插补
        MOVEABS(end_x, end_z_safe)
    ENDIF

    '第三段：垂直下降Z到目标位置（X不变，只降Z）
    MOVEABS(end_x, end_z)                   ' X不变（已经在end_x），只降Z

    '不做任何等待，让控制器自行与下一条轨迹融合
END SUB

'================ 速度监控设置 ================
GLOBAL SUB SetupSpeedMon()
    '设置示波器监控VP_SPEED和MSPEED，用于分析连续插补效果
    TRIGGER                         ' 自动触发示波器
    PRINT "示波器监控设置："
    PRINT "VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）"
    PRINT "VP_SPEED(3) - Z轴单轴速度（红色，刻度100，偏移-60）"
    PRINT "MSPEED(0)  - X轴分速度（绿色，刻度100，偏移-120）"
    PRINT "MSPEED(3)  - Z轴分速度（黄色，刻度100，偏移-180）"
    PRINT "连续插补成功标志：各轴速度在衔接处不降到0，整体平滑连续"
END SUB

'================ 兼容旧接口的标准三段轨迹 ================
GLOBAL SUB StdThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    '兼容旧接口，内部使用新的连续轨迹方式
    CALL BeginContPath()            ' 开始连续轨迹
    CALL PushThreeSeg(start_x, start_z, end_x, end_z, start_z_safe, end_z_safe, std_mid_x, std_dist)
    CALL EndContPath()              ' 结束连续轨迹
END SUB





'================ 备用直线移动函数 ================
GLOBAL SUB LinearXZMove(start_x, start_z, end_x, end_z)
    PRINT "直线XZ移动：从(", start_x, ",", start_z, ")到(", end_x, ",", end_z, ")"

    BASE(0, 3)
    MOVEABS(end_x) AXIS(0)
    MOVEABS(end_z) AXIS(3)
    WAIT IDLE(0)
    WAIT IDLE(3)

    PRINT "直线XZ移动完成"
END SUB


