===============================================================================
                        两轴插补测试程序 - 输入电平控制接线说明
===============================================================================

【接线图示】

=== 控制器输入端子接线 ===
```
正运动控制器数字输入端子：

IN0+  ←── 24V信号（测试1触发）
IN0-  ←── 0V (GND)

IN1+  ←── 24V信号（测试2触发）
IN1-  ←── 0V (GND)

IN2+  ←── 24V信号（测试3触发）
IN2-  ←── 0V (GND)

IN3+  ←── 24V信号（测试4触发）
IN3-  ←── 0V (GND)

IN4+  ←── 24V信号（测试5触发）
IN4-  ←── 0V (GND)

IN5+  ←── 24V信号（测试6触发）
IN5-  ←── 0V (GND)

IN6+  ←── 24V信号（测试7触发）
IN6-  ←── 0V (GND)

IN7+  ←── 24V信号（测试8触发）
IN7-  ←── 0V (GND)
```

=== PLC控制接线示例 ===
```
PLC输出端子 → 控制器输入端子

PLC侧：                    控制器侧：
Y0 (24V输出) ──────────→ IN0+
COM (0V)     ──────────→ IN0-

Y1 (24V输出) ──────────→ IN1+
COM (0V)     ──────────→ IN1-

Y2 (24V输出) ──────────→ IN2+
COM (0V)     ──────────→ IN2-

...以此类推

PLC程序示例：
LD M0        ' 启动测试1条件
OUT Y0       ' 输出到控制器IN0

LD M1        ' 启动测试2条件
OUT Y1       ' 输出到控制器IN1
```

=== 按钮开关接线示例 ===
```
按钮开关 → 控制器输入端子

24V电源正极 ──┬── 按钮1 ──→ IN0+
              ├── 按钮2 ──→ IN1+
              ├── 按钮3 ──→ IN2+
              ├── 按钮4 ──→ IN3+
              ├── 按钮5 ──→ IN4+
              ├── 按钮6 ──→ IN5+
              ├── 按钮7 ──→ IN6+
              └── 按钮8 ──→ IN7+

24V电源负极 ──┬── IN0-
              ├── IN1-
              ├── IN2-
              ├── IN3-
              ├── IN4-
              ├── IN5-
              ├── IN6-
              └── IN7-

按钮类型：常开按钮（NO）
按钮规格：24V耐压，适当电流容量
```

=== 传感器触发接线示例 ===
```
传感器输出 → 控制器输入端子

传感器类型：NPN或PNP输出型
电源要求：24V DC

NPN型传感器接线：
传感器棕线(+) ── 24V电源正极
传感器蓝线(-) ── 24V电源负极
传感器黑线(信号) ── IN0+
24V电源负极 ── IN0-

PNP型传感器接线：
传感器棕线(+) ── 24V电源正极
传感器蓝线(-) ── 24V电源负极
传感器黑线(信号) ── IN0+
24V电源正极 ── 上拉电阻 ── IN0+
24V电源负极 ── IN0-
```

【信号要求】

=== 电气参数 ===
```
输入电压：24V DC（标准）或5V DC（根据控制器规格）
输入电流：通常<10mA（光耦隔离输入）
信号类型：数字开关信号
触发方式：上升沿触发（0V→24V）
信号宽度：建议≥100ms（避免抖动误触发）
信号频率：不建议过于频繁，间隔≥1秒
```

=== 信号质量要求 ===
```
上升时间：<10ms（避免缓慢变化）
下降时间：<10ms
信号稳定性：高电平期间保持稳定
抗干扰性：使用屏蔽线缆，远离强电干扰源
接地良好：确保0V参考点一致
```

【测试引脚功能分配】

=== 默认分配（IN0-IN7）===
```
IN0 → Test1_Linear()
功能：两轴直线插补测试
轨迹：(0,0) → (100,100)
用途：基础功能验证，建议首先测试

IN1 → Test2_Circular()
功能：两轴圆弧插补测试
轨迹：三点圆弧插补
用途：圆弧功能验证

IN2 → Test3_NonContinuous()
功能：非连续插补测试（MERGE=OFF）
轨迹：矩形轨迹，四段直线
用途：对比测试，展示非连续插补问题

IN3 → Test4_Continuous()
功能：连续插补测试（MERGE=ON）
轨迹：矩形轨迹，连续执行
用途：连续插补效果验证

IN4 → Test5_CornerDecel()
功能：前瞻拐角减速测试（CORNER_MODE=2）
特点：拐角处自动减速
用途：前瞻减速功能验证

IN5 → Test6_AutoChamfer()
功能：自动倒角测试（CORNER_MODE=32）
特点：拐角处轨迹倒角
用途：自动倒角功能验证

IN6 → Test7_Combined()
功能：组合前瞻测试（CORNER_MODE=2+32）
特点：倒角+减速组合
用途：综合前瞻功能验证

IN7 → Test8_Complex()
功能：复杂轨迹连续插补测试
轨迹：直线+圆弧组合
用途：复杂应用场景验证
```

=== 自定义分配方法 ===
```
如需使用其他输入引脚，可修改程序：

方法1：修改起始引脚号
CALL SetInputPinBase(8)          ' 使用IN8-IN15
CALL SetInputPinBase(16)         ' 使用IN16-IN23

方法2：直接修改程序变量
GLOBAL input_pin_base = 8        ' 改为所需起始引脚

方法3：禁用输入控制，改为手动模式
GLOBAL enable_input_control = 0  ' 禁用输入控制
CALL ManualTest()                ' 启动手动测试模式
```

【操作流程】

=== 第一次使用流程 ===
```
1. 硬件准备：
   - 连接控制器与电机
   - 连接输入信号线
   - 连接示波器监控线

2. 程序加载：
   - 加载"两轴插补测试修正版.bas"
   - 程序自动启动并显示引脚分配
   - 进入输入电平监控模式

3. 基础测试：
   - 给IN0施加24V信号（直线插补测试）
   - 观察电机运动和示波器曲线
   - 确认基本功能正常

4. 逐项测试：
   - 依次触发IN1-IN7
   - 观察不同插补模式的效果
   - 对比分析速度曲线差异

5. 综合验证：
   - 多次重复测试验证稳定性
   - 记录测试结果和观察现象
   - 分析插补性能和质量
```

=== 日常使用流程 ===
```
1. 上电启动：
   - 控制器上电，程序自动运行
   - 显示引脚分配和状态信息
   - 进入监控模式等待触发

2. 选择测试：
   - 根据需要触发对应输入引脚
   - 程序自动执行相应测试
   - 观察运动过程和速度曲线

3. 结果分析：
   - 通过示波器分析速度特性
   - 评估插补质量和连续性
   - 记录测试数据和现象

4. 参数调整：
   - 根据测试结果调整运动参数
   - 优化前瞻和平滑设置
   - 重新测试验证效果
```

【安全注意事项】

=== 电气安全 ===
```
1. 电源安全：
   - 确认电压等级匹配
   - 使用合适的保险丝保护
   - 注意电源极性，避免反接

2. 接线安全：
   - 断电状态下进行接线
   - 使用合适规格的导线
   - 接线牢固，避免松动

3. 绝缘安全：
   - 确保良好的电气绝缘
   - 避免裸露导体接触
   - 使用绝缘工具操作
```

=== 机械安全 ===
```
1. 运动安全：
   - 确保运动范围内无障碍物
   - 设置合适的行程限位
   - 准备急停按钮

2. 人员安全：
   - 测试时人员保持安全距离
   - 穿戴适当的防护用品
   - 熟悉急停操作程序

3. 设备安全：
   - 检查机械连接牢固性
   - 确认轴参数设置合理
   - 避免超出设备能力范围
```

【故障排除】

=== 输入信号问题 ===
```
现象：输入信号无响应
检查：
1. 信号电压是否正确（24V或5V）
2. 接线是否正确（+/-极性）
3. 信号源是否正常输出
4. 控制器输入端子是否损坏

解决：
1. 用万用表测量信号电压
2. 检查接线图对照实际接线
3. 更换信号源测试
4. 联系技术支持检查硬件
```

=== 重复触发问题 ===
```
现象：一次信号触发多次测试
原因：
1. 信号抖动或干扰
2. 信号上升时间过慢
3. 程序检测间隔过短

解决：
1. 增加信号滤波电路
2. 使用施密特触发器整形
3. 调整DELAY时间参数
4. 检查信号源质量
```

=== 测试执行异常 ===
```
现象：测试执行过程中出错
检查：
1. 轴参数设置是否合理
2. 机械系统是否正常
3. 运动范围是否足够
4. 电机驱动是否正常

解决：
1. 调整SPEED、ACCEL等参数
2. 检查机械连接和润滑
3. 确认工作空间足够大
4. 检查驱动器状态和参数
```

【维护保养】

=== 定期检查 ===
```
1. 电气连接：
   - 检查接线端子紧固情况
   - 测量信号电压是否正常
   - 检查线缆绝缘状态

2. 机械系统：
   - 检查轴承润滑状态
   - 检查传动机构磨损情况
   - 确认行程限位功能正常

3. 程序运行：
   - 验证各测试功能正常
   - 检查示波器监控效果
   - 确认参数设置合理
```

=== 记录管理 ===
```
1. 测试记录：
   - 记录测试时间和条件
   - 保存示波器波形图
   - 记录异常现象和处理方法

2. 参数记录：
   - 记录优化后的参数设置
   - 保存不同应用的参数组合
   - 建立参数变更历史

3. 维护记录：
   - 记录维护保养时间
   - 记录更换的零部件
   - 记录故障处理过程
```

【总结】

输入电平控制接线的关键要点：
✅ **正确的电压等级**：确保信号电压与控制器输入匹配
✅ **可靠的接线方式**：使用合适的端子和导线规格
✅ **良好的信号质量**：避免干扰和抖动，确保干净的上升沿
✅ **合理的引脚分配**：根据实际需要选择合适的输入引脚
✅ **完善的安全措施**：确保电气和机械安全
✅ **规范的操作流程**：按照标准流程进行测试和维护

通过正确的接线和配置，可以实现稳定可靠的输入电平控制，
为两轴插补测试提供便捷的自动化触发方式。

===============================================================================
