This is make.info, produced by makeinfo version 4.8 from make.texi.

   This file documents the GNU `make' utility, which determines
automatically which pieces of a large program need to be recompiled,
and issues the commands to recompile them.

   This is Edition 0.70, last updated 1 April 2006, of `The GNU Make
Manual', for GNU `make' version 3.81.

   Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996,
1997, 1998, 1999, 2000, 2002, 2003, 2004, 2005, 2006 Free Software
Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have freedom to copy and
     modify this GNU Manual, like GNU software.  Copies published by
     the Free Software Foundation raise funds for GNU development."

INFO-DIR-SECTION GNU Packages
START-INFO-DIR-ENTRY
* Make: (make).            Remake files automatically.
END-INFO-DIR-ENTRY


File: make.info,  Node: Top,  Next: Overview,  Prev: (dir),  Up: (dir)

GNU `make'
**********

This file documents the GNU `make' utility, which determines
automatically which pieces of a large program need to be recompiled,
and issues the commands to recompile them.

   This is Edition 0.70, last updated 1 April 2006, of `The GNU Make
Manual', for GNU `make' version 3.81.

   Copyright (C) 1988, 1989, 1990, 1991, 1992, 1993, 1994, 1995, 1996,
1997, 1998, 1999, 2000, 2002, 2003, 2004, 2005, 2006 Free Software
Foundation, Inc.

     Permission is granted to copy, distribute and/or modify this
     document under the terms of the GNU Free Documentation License,
     Version 1.2 or any later version published by the Free Software
     Foundation; with no Invariant Sections, with the Front-Cover Texts
     being "A GNU Manual," and with the Back-Cover Texts as in (a)
     below.  A copy of the license is included in the section entitled
     "GNU Free Documentation License."

     (a) The FSF's Back-Cover Text is: "You have freedom to copy and
     modify this GNU Manual, like GNU software.  Copies published by
     the Free Software Foundation raise funds for GNU development."

* Menu:

* Overview::                    Overview of `make'.
* Introduction::                An introduction to `make'.
* Makefiles::                   Makefiles tell `make' what to do.
* Rules::                       Rules describe when a file must be remade.
* Commands::                    Commands say how to remake a file.
* Using Variables::             You can use variables to avoid repetition.
* Conditionals::                Use or ignore parts of the makefile based
                                  on the values of variables.
* Functions::                   Many powerful ways to manipulate text.
* Invoking make: Running.       How to invoke `make' on the command line.
* Implicit Rules::              Use implicit rules to treat many files alike,
                                  based on their file names.
* Archives::                    How `make' can update library archives.
* Features::                    Features GNU `make' has over other `make's.
* Missing::                     What GNU `make' lacks from other `make's.
* Makefile Conventions::        Conventions for writing makefiles for
                                  GNU programs.
* Quick Reference::             A quick reference for experienced users.
* Error Messages::              A list of common errors generated by `make'.
* Complex Makefile::            A real example of a straightforward,
                                  but nontrivial, makefile.

* GNU Free Documentation License::  License for copying this manual
* Concept Index::               Index of Concepts
* Name Index::                  Index of Functions, Variables, & Directives

 --- The Detailed Node Listing ---

Overview of `make'

* Preparing::                   Preparing and Running Make
* Reading::                     On Reading this Text
* Bugs::                        Problems and Bugs

An Introduction to Makefiles

* Rule Introduction::           What a rule looks like.
* Simple Makefile::             A Simple Makefile
* How Make Works::              How `make' Processes This Makefile
* Variables Simplify::          Variables Make Makefiles Simpler
* make Deduces::                Letting `make' Deduce the Commands
* Combine By Prerequisite::     Another Style of Makefile
* Cleanup::                     Rules for Cleaning the Directory

Writing Makefiles

* Makefile Contents::           What makefiles contain.
* Makefile Names::              How to name your makefile.
* Include::                     How one makefile can use another makefile.
* MAKEFILES Variable::          The environment can specify extra makefiles.
* MAKEFILE_LIST Variable::      Discover which makefiles have been read.
* Special Variables::           Other special variables.
* Remaking Makefiles::          How makefiles get remade.
* Overriding Makefiles::        How to override part of one makefile
                                  with another makefile.
* Reading Makefiles::           How makefiles are parsed.
* Secondary Expansion::         How and when secondary expansion is performed.

Writing Rules

* Rule Example::                An example explained.
* Rule Syntax::                 General syntax explained.
* Prerequisite Types::          There are two types of prerequisites.
* Wildcards::                   Using wildcard characters such as `*'.
* Directory Search::            Searching other directories for source files.
* Phony Targets::               Using a target that is not a real file's name.
* Force Targets::               You can use a target without commands
                                  or prerequisites to mark other targets
                                  as phony.
* Empty Targets::               When only the date matters and the
                                  files are empty.
* Special Targets::             Targets with special built-in meanings.
* Multiple Targets::            When to make use of several targets in a rule.
* Multiple Rules::              How to use several rules with the same target.
* Static Pattern::              Static pattern rules apply to multiple targets
                                  and can vary the prerequisites according to
                                  the target name.
* Double-Colon::                How to use a special kind of rule to allow
                                  several independent rules for one target.
* Automatic Prerequisites::     How to automatically generate rules giving
                                  prerequisites from source files themselves.

Using Wildcard Characters in File Names

* Wildcard Examples::           Several examples
* Wildcard Pitfall::            Problems to avoid.
* Wildcard Function::           How to cause wildcard expansion where
                                  it does not normally take place.

Searching Directories for Prerequisites

* General Search::              Specifying a search path that applies
                                  to every prerequisite.
* Selective Search::            Specifying a search path
                                  for a specified class of names.
* Search Algorithm::            When and how search paths are applied.
* Commands/Search::             How to write shell commands that work together
                                  with search paths.
* Implicit/Search::             How search paths affect implicit rules.
* Libraries/Search::            Directory search for link libraries.

Static Pattern Rules

* Static Usage::                The syntax of static pattern rules.
* Static versus Implicit::      When are they better than implicit rules?

Writing the Commands in Rules

* Command Syntax::              Command syntax features and pitfalls.
* Echoing::                     How to control when commands are echoed.
* Execution::                   How commands are executed.
* Parallel::                    How commands can be executed in parallel.
* Errors::                      What happens after a command execution error.
* Interrupts::                  What happens when a command is interrupted.
* Recursion::                   Invoking `make' from makefiles.
* Sequences::                   Defining canned sequences of commands.
* Empty Commands::              Defining useful, do-nothing commands.

Command Syntax

* Splitting Lines::             Breaking long command lines for readability.
* Variables in Commands::       Using `make' variables in commands.

Command Execution

* Choosing the Shell::          How `make' chooses the shell used
                                  to run commands.

Recursive Use of `make'

* MAKE Variable::               The special effects of using `$(MAKE)'.
* Variables/Recursion::         How to communicate variables to a sub-`make'.
* Options/Recursion::           How to communicate options to a sub-`make'.
* -w Option::                   How the `-w' or `--print-directory' option
                                  helps debug use of recursive `make' commands.

How to Use Variables

* Reference::                   How to use the value of a variable.
* Flavors::                     Variables come in two flavors.
* Advanced::                    Advanced features for referencing a variable.
* Values::                      All the ways variables get their values.
* Setting::                     How to set a variable in the makefile.
* Appending::                   How to append more text to the old value
                                  of a variable.
* Override Directive::          How to set a variable in the makefile even if
                                  the user has set it with a command argument.
* Defining::                    An alternate way to set a variable
                                  to a verbatim string.
* Environment::                 Variable values can come from the environment.
* Target-specific::             Variable values can be defined on a per-target
                                  basis.
* Pattern-specific::            Target-specific variable values can be applied
                                  to a group of targets that match a pattern.

Advanced Features for Reference to Variables

* Substitution Refs::           Referencing a variable with
                                  substitutions on the value.
* Computed Names::              Computing the name of the variable to refer to.

Conditional Parts of Makefiles

* Conditional Example::         Example of a conditional
* Conditional Syntax::          The syntax of conditionals.
* Testing Flags::               Conditionals that test flags.

Functions for Transforming Text

* Syntax of Functions::         How to write a function call.
* Text Functions::              General-purpose text manipulation functions.
* File Name Functions::         Functions for manipulating file names.
* Conditional Functions::       Functions that implement conditions.
* Foreach Function::            Repeat some text with controlled variation.
* Call Function::               Expand a user-defined function.
* Value Function::              Return the un-expanded value of a variable.
* Eval Function::               Evaluate the arguments as makefile syntax.
* Origin Function::             Find where a variable got its value.
* Flavor Function::             Find out the flavor of a variable.
* Shell Function::              Substitute the output of a shell command.
* Make Control Functions::      Functions that control how make runs.

How to Run `make'

* Makefile Arguments::          How to specify which makefile to use.
* Goals::                       How to use goal arguments to specify which
                                  parts of the makefile to use.
* Instead of Execution::        How to use mode flags to specify what
                                  kind of thing to do with the commands
                                  in the makefile other than simply
                                  execute them.
* Avoiding Compilation::        How to avoid recompiling certain files.
* Overriding::                  How to override a variable to specify
                                  an alternate compiler and other things.
* Testing::                     How to proceed past some errors, to
                                  test compilation.
* Options Summary::             Summary of Options

Using Implicit Rules

* Using Implicit::              How to use an existing implicit rule
                                  to get the commands for updating a file.
* Catalogue of Rules::          A list of built-in implicit rules.
* Implicit Variables::          How to change what predefined rules do.
* Chained Rules::               How to use a chain of implicit rules.
* Pattern Rules::               How to define new implicit rules.
* Last Resort::                 How to define commands for rules which
                                  cannot find any.
* Suffix Rules::                The old-fashioned style of implicit rule.
* Implicit Rule Search::        The precise algorithm for applying
                                  implicit rules.

Defining and Redefining Pattern Rules

* Pattern Intro::               An introduction to pattern rules.
* Pattern Examples::            Examples of pattern rules.
* Automatic Variables::         How to use automatic variables in the
                                  commands of implicit rules.
* Pattern Match::               How patterns match.
* Match-Anything Rules::        Precautions you should take prior to
                                  defining rules that can match any
                                  target file whatever.
* Canceling Rules::             How to override or cancel built-in rules.

Using `make' to Update Archive Files

* Archive Members::             Archive members as targets.
* Archive Update::              The implicit rule for archive member targets.
* Archive Pitfalls::            Dangers to watch out for when using archives.
* Archive Suffix Rules::        You can write a special kind of suffix rule
                                  for updating archives.

Implicit Rule for Archive Member Targets

* Archive Symbols::             How to update archive symbol directories.


File: make.info,  Node: Overview,  Next: Introduction,  Prev: Top,  Up: Top

1 Overview of `make'
********************

The `make' utility automatically determines which pieces of a large
program need to be recompiled, and issues commands to recompile them.
This manual describes GNU `make', which was implemented by Richard
Stallman and Roland McGrath.  Development since Version 3.76 has been
handled by Paul D. Smith.

   GNU `make' conforms to section 6.2 of `IEEE Standard 1003.2-1992'
(POSIX.2).  

   Our examples show C programs, since they are most common, but you
can use `make' with any programming language whose compiler can be run
with a shell command.  Indeed, `make' is not limited to programs.  You
can use it to describe any task where some files must be updated
automatically from others whenever the others change.

* Menu:

* Preparing::                   Preparing and Running Make
* Reading::                     On Reading this Text
* Bugs::                        Problems and Bugs


File: make.info,  Node: Preparing,  Next: Reading,  Prev: Overview,  Up: Overview

Preparing and Running Make
==========================

   To prepare to use `make', you must write a file called the
"makefile" that describes the relationships among files in your program
and provides commands for updating each file.  In a program, typically,
the executable file is updated from object files, which are in turn
made by compiling source files.

   Once a suitable makefile exists, each time you change some source
files, this simple shell command:

     make

suffices to perform all necessary recompilations.  The `make' program
uses the makefile data base and the last-modification times of the
files to decide which of the files need to be updated.  For each of
those files, it issues the commands recorded in the data base.

   You can provide command line arguments to `make' to control which
files should be recompiled, or how.  *Note How to Run `make': Running.


File: make.info,  Node: Reading,  Next: Bugs,  Prev: Preparing,  Up: Overview

1.1 How to Read This Manual
===========================

If you are new to `make', or are looking for a general introduction,
read the first few sections of each chapter, skipping the later
sections.  In each chapter, the first few sections contain introductory
or general information and the later sections contain specialized or
technical information.  The exception is the second chapter, *Note An
Introduction to Makefiles: Introduction, all of which is introductory.

   If you are familiar with other `make' programs, see *Note Features
of GNU `make': Features, which lists the enhancements GNU `make' has,
and *Note Incompatibilities and Missing Features: Missing, which
explains the few things GNU `make' lacks that others have.

   For a quick summary, see *Note Options Summary::, *Note Quick
Reference::, and *Note Special Targets::.


File: make.info,  Node: Bugs,  Prev: Reading,  Up: Overview

1.2 Problems and Bugs
=====================

If you have problems with GNU `make' or think you've found a bug,
please report it to the developers; we cannot promise to do anything but
we might well want to fix it.

   Before reporting a bug, make sure you've actually found a real bug.
Carefully reread the documentation and see if it really says you can do
what you're trying to do.  If it's not clear whether you should be able
to do something or not, report that too; it's a bug in the
documentation!

   Before reporting a bug or trying to fix it yourself, try to isolate
it to the smallest possible makefile that reproduces the problem.  Then
send us the makefile and the exact results `make' gave you, including
any error or warning messages.  Please don't paraphrase these messages:
it's best to cut and paste them into your report.  When generating this
small makefile, be sure to not use any non-free or unusual tools in
your commands: you can almost always emulate what such a tool would do
with simple shell commands.  Finally, be sure to explain what you
expected to occur; this will help us decide whether the problem was
really in the documentation.

   Once you have a precise problem you can report it in one of two ways.
Either send electronic mail to:

         <EMAIL>

or use our Web-based project management tool, at:

         http://savannah.gnu.org/projects/make/

In addition to the information above, please be careful to include the
version number of `make' you are using.  You can get this information
with the command `make --version'.  Be sure also to include the type of
machine and operating system you are using.  One way to obtain this
information is by looking at the final lines of output from the command
`make --help'.


File: make.info,  Node: Introduction,  Next: Makefiles,  Prev: Overview,  Up: Top

2 An Introduction to Makefiles
******************************

You need a file called a "makefile" to tell `make' what to do.  Most
often, the makefile tells `make' how to compile and link a program.  

   In this chapter, we will discuss a simple makefile that describes
how to compile and link a text editor which consists of eight C source
files and three header files.  The makefile can also tell `make' how to
run miscellaneous commands when explicitly asked (for example, to remove
certain files as a clean-up operation).  To see a more complex example
of a makefile, see *Note Complex Makefile::.

   When `make' recompiles the editor, each changed C source file must
be recompiled.  If a header file has changed, each C source file that
includes the header file must be recompiled to be safe.  Each
compilation produces an object file corresponding to the source file.
Finally, if any source file has been recompiled, all the object files,
whether newly made or saved from previous compilations, must be linked
together to produce the new executable editor.  

* Menu:

* Rule Introduction::           What a rule looks like.
* Simple Makefile::             A Simple Makefile
* How Make Works::              How `make' Processes This Makefile
* Variables Simplify::          Variables Make Makefiles Simpler
* make Deduces::                Letting `make' Deduce the Commands
* Combine By Prerequisite::     Another Style of Makefile
* Cleanup::                     Rules for Cleaning the Directory


File: make.info,  Node: Rule Introduction,  Next: Simple Makefile,  Prev: Introduction,  Up: Introduction

2.1 What a Rule Looks Like
==========================

A simple makefile consists of "rules" with the following shape:

     TARGET ... : PREREQUISITES ...
             COMMAND
             ...
             ...

   A "target" is usually the name of a file that is generated by a
program; examples of targets are executable or object files.  A target
can also be the name of an action to carry out, such as `clean' (*note
Phony Targets::).

   A "prerequisite" is a file that is used as input to create the
target.  A target often depends on several files.

   A "command" is an action that `make' carries out.  A rule may have
more than one command, each on its own line.  *Please note:* you need
to put a tab character at the beginning of every command line!  This is
an obscurity that catches the unwary.

   Usually a command is in a rule with prerequisites and serves to
create a target file if any of the prerequisites change.  However, the
rule that specifies commands for the target need not have
prerequisites.  For example, the rule containing the delete command
associated with the target `clean' does not have prerequisites.

   A "rule", then, explains how and when to remake certain files which
are the targets of the particular rule.  `make' carries out the
commands on the prerequisites to create or update the target.  A rule
can also explain how and when to carry out an action.  *Note Writing
Rules: Rules.

   A makefile may contain other text besides rules, but a simple
makefile need only contain rules.  Rules may look somewhat more
complicated than shown in this template, but all fit the pattern more
or less.


File: make.info,  Node: Simple Makefile,  Next: How Make Works,  Prev: Rule Introduction,  Up: Introduction

2.2 A Simple Makefile
=====================

Here is a straightforward makefile that describes the way an executable
file called `edit' depends on eight object files which, in turn, depend
on eight C source and three header files.

   In this example, all the C files include `defs.h', but only those
defining editing commands include `command.h', and only low level files
that change the editor buffer include `buffer.h'.

     edit : main.o kbd.o command.o display.o \
            insert.o search.o files.o utils.o
             cc -o edit main.o kbd.o command.o display.o \
                        insert.o search.o files.o utils.o

     main.o : main.c defs.h
             cc -c main.c
     kbd.o : kbd.c defs.h command.h
             cc -c kbd.c
     command.o : command.c defs.h command.h
             cc -c command.c
     display.o : display.c defs.h buffer.h
             cc -c display.c
     insert.o : insert.c defs.h buffer.h
             cc -c insert.c
     search.o : search.c defs.h buffer.h
             cc -c search.c
     files.o : files.c defs.h buffer.h command.h
             cc -c files.c
     utils.o : utils.c defs.h
             cc -c utils.c
     clean :
             rm edit main.o kbd.o command.o display.o \
                insert.o search.o files.o utils.o

We split each long line into two lines using backslash-newline; this is
like using one long line, but is easier to read.  

   To use this makefile to create the executable file called `edit',
type:

     make

   To use this makefile to delete the executable file and all the object
files from the directory, type:

     make clean

   In the example makefile, the targets include the executable file
`edit', and the object files `main.o' and `kbd.o'.  The prerequisites
are files such as `main.c' and `defs.h'.  In fact, each `.o' file is
both a target and a prerequisite.  Commands include `cc -c main.c' and
`cc -c kbd.c'.

   When a target is a file, it needs to be recompiled or relinked if any
of its prerequisites change.  In addition, any prerequisites that are
themselves automatically generated should be updated first.  In this
example, `edit' depends on each of the eight object files; the object
file `main.o' depends on the source file `main.c' and on the header
file `defs.h'.

   A shell command follows each line that contains a target and
prerequisites.  These shell commands say how to update the target file.
A tab character must come at the beginning of every command line to
distinguish command lines from other lines in the makefile.  (Bear in
mind that `make' does not know anything about how the commands work.
It is up to you to supply commands that will update the target file
properly.  All `make' does is execute the commands in the rule you have
specified when the target file needs to be updated.)  

   The target `clean' is not a file, but merely the name of an action.
Since you normally do not want to carry out the actions in this rule,
`clean' is not a prerequisite of any other rule.  Consequently, `make'
never does anything with it unless you tell it specifically.  Note that
this rule not only is not a prerequisite, it also does not have any
prerequisites, so the only purpose of the rule is to run the specified
commands.  Targets that do not refer to files but are just actions are
called "phony targets".  *Note Phony Targets::, for information about
this kind of target.  *Note Errors in Commands: Errors, to see how to
cause `make' to ignore errors from `rm' or any other command.  


File: make.info,  Node: How Make Works,  Next: Variables Simplify,  Prev: Simple Makefile,  Up: Introduction

2.3 How `make' Processes a Makefile
===================================

By default, `make' starts with the first target (not targets whose
names start with `.').  This is called the "default goal".  ("Goals"
are the targets that `make' strives ultimately to update.    You can
override this behavior using the command line (*note Arguments to
Specify the Goals: Goals.) or with the `.DEFAULT_GOAL' special variable
(*note Other Special Variables: Special Variables.).  

   In the simple example of the previous section, the default goal is to
update the executable program `edit'; therefore, we put that rule first.

   Thus, when you give the command:

     make

`make' reads the makefile in the current directory and begins by
processing the first rule.  In the example, this rule is for relinking
`edit'; but before `make' can fully process this rule, it must process
the rules for the files that `edit' depends on, which in this case are
the object files.  Each of these files is processed according to its
own rule.  These rules say to update each `.o' file by compiling its
source file.  The recompilation must be done if the source file, or any
of the header files named as prerequisites, is more recent than the
object file, or if the object file does not exist.

   The other rules are processed because their targets appear as
prerequisites of the goal.  If some other rule is not depended on by the
goal (or anything it depends on, etc.), that rule is not processed,
unless you tell `make' to do so (with a command such as `make clean').

   Before recompiling an object file, `make' considers updating its
prerequisites, the source file and header files.  This makefile does not
specify anything to be done for them--the `.c' and `.h' files are not
the targets of any rules--so `make' does nothing for these files.  But
`make' would update automatically generated C programs, such as those
made by Bison or Yacc, by their own rules at this time.

   After recompiling whichever object files need it, `make' decides
whether to relink `edit'.  This must be done if the file `edit' does
not exist, or if any of the object files are newer than it.  If an
object file was just recompiled, it is now newer than `edit', so `edit'
is relinked.  

   Thus, if we change the file `insert.c' and run `make', `make' will
compile that file to update `insert.o', and then link `edit'.  If we
change the file `command.h' and run `make', `make' will recompile the
object files `kbd.o', `command.o' and `files.o' and then link the file
`edit'.


File: make.info,  Node: Variables Simplify,  Next: make Deduces,  Prev: How Make Works,  Up: Introduction

2.4 Variables Make Makefiles Simpler
====================================

In our example, we had to list all the object files twice in the rule
for `edit' (repeated here):

     edit : main.o kbd.o command.o display.o \
                   insert.o search.o files.o utils.o
             cc -o edit main.o kbd.o command.o display.o \
                        insert.o search.o files.o utils.o

   Such duplication is error-prone; if a new object file is added to the
system, we might add it to one list and forget the other.  We can
eliminate the risk and simplify the makefile by using a variable.
"Variables" allow a text string to be defined once and substituted in
multiple places later (*note How to Use Variables: Using Variables.).

   It is standard practice for every makefile to have a variable named
`objects', `OBJECTS', `objs', `OBJS', `obj', or `OBJ' which is a list
of all object file names.  We would define such a variable `objects'
with a line like this in the makefile:

     objects = main.o kbd.o command.o display.o \
               insert.o search.o files.o utils.o

Then, each place we want to put a list of the object file names, we can
substitute the variable's value by writing `$(objects)' (*note How to
Use Variables: Using Variables.).

   Here is how the complete simple makefile looks when you use a
variable for the object files:

     objects = main.o kbd.o command.o display.o \
               insert.o search.o files.o utils.o

     edit : $(objects)
             cc -o edit $(objects)
     main.o : main.c defs.h
             cc -c main.c
     kbd.o : kbd.c defs.h command.h
             cc -c kbd.c
     command.o : command.c defs.h command.h
             cc -c command.c
     display.o : display.c defs.h buffer.h
             cc -c display.c
     insert.o : insert.c defs.h buffer.h
             cc -c insert.c
     search.o : search.c defs.h buffer.h
             cc -c search.c
     files.o : files.c defs.h buffer.h command.h
             cc -c files.c
     utils.o : utils.c defs.h
             cc -c utils.c
     clean :
             rm edit $(objects)


File: make.info,  Node: make Deduces,  Next: Combine By Prerequisite,  Prev: Variables Simplify,  Up: Introduction

2.5 Letting `make' Deduce the Commands
======================================

It is not necessary to spell out the commands for compiling the
individual C source files, because `make' can figure them out: it has an
"implicit rule" for updating a `.o' file from a correspondingly named
`.c' file using a `cc -c' command.  For example, it will use the
command `cc -c main.c -o main.o' to compile `main.c' into `main.o'.  We
can therefore omit the commands from the rules for the object files.
*Note Using Implicit Rules: Implicit Rules.

   When a `.c' file is used automatically in this way, it is also
automatically added to the list of prerequisites.  We can therefore omit
the `.c' files from the prerequisites, provided we omit the commands.

   Here is the entire example, with both of these changes, and a
variable `objects' as suggested above:

     objects = main.o kbd.o command.o display.o \
               insert.o search.o files.o utils.o

     edit : $(objects)
             cc -o edit $(objects)

     main.o : defs.h
     kbd.o : defs.h command.h
     command.o : defs.h command.h
     display.o : defs.h buffer.h
     insert.o : defs.h buffer.h
     search.o : defs.h buffer.h
     files.o : defs.h buffer.h command.h
     utils.o : defs.h

     .PHONY : clean
     clean :
             rm edit $(objects)

This is how we would write the makefile in actual practice.  (The
complications associated with `clean' are described elsewhere.  See
*Note Phony Targets::, and *Note Errors in Commands: Errors.)

   Because implicit rules are so convenient, they are important.  You
will see them used frequently.


File: make.info,  Node: Combine By Prerequisite,  Next: Cleanup,  Prev: make Deduces,  Up: Introduction

2.6 Another Style of Makefile
=============================

When the objects of a makefile are created only by implicit rules, an
alternative style of makefile is possible.  In this style of makefile,
you group entries by their prerequisites instead of by their targets.
Here is what one looks like:

     objects = main.o kbd.o command.o display.o \
               insert.o search.o files.o utils.o

     edit : $(objects)
             cc -o edit $(objects)

     $(objects) : defs.h
     kbd.o command.o files.o : command.h
     display.o insert.o search.o files.o : buffer.h

Here `defs.h' is given as a prerequisite of all the object files;
`command.h' and `buffer.h' are prerequisites of the specific object
files listed for them.

   Whether this is better is a matter of taste: it is more compact, but
some people dislike it because they find it clearer to put all the
information about each target in one place.


File: make.info,  Node: Cleanup,  Prev: Combine By Prerequisite,  Up: Introduction

2.7 Rules for Cleaning the Directory
====================================

Compiling a program is not the only thing you might want to write rules
for.  Makefiles commonly tell how to do a few other things besides
compiling a program: for example, how to delete all the object files
and executables so that the directory is `clean'.

   Here is how we could write a `make' rule for cleaning our example
editor:

     clean:
             rm edit $(objects)

   In practice, we might want to write the rule in a somewhat more
complicated manner to handle unanticipated situations.  We would do
this:

     .PHONY : clean
     clean :
             -rm edit $(objects)

This prevents `make' from getting confused by an actual file called
`clean' and causes it to continue in spite of errors from `rm'.  (See
*Note Phony Targets::, and *Note Errors in Commands: Errors.)

A rule such as this should not be placed at the beginning of the
makefile, because we do not want it to run by default!  Thus, in the
example makefile, we want the rule for `edit', which recompiles the
editor, to remain the default goal.

   Since `clean' is not a prerequisite of `edit', this rule will not
run at all if we give the command `make' with no arguments.  In order
to make the rule run, we have to type `make clean'.  *Note How to Run
`make': Running.


File: make.info,  Node: Makefiles,  Next: Rules,  Prev: Introduction,  Up: Top

3 Writing Makefiles
*******************

The information that tells `make' how to recompile a system comes from
reading a data base called the "makefile".

* Menu:

* Makefile Contents::           What makefiles contain.
* Makefile Names::              How to name your makefile.
* Include::                     How one makefile can use another makefile.
* MAKEFILES Variable::          The environment can specify extra makefiles.
* MAKEFILE_LIST Variable::      Discover which makefiles have been read.
* Special Variables::           Other special variables.
* Remaking Makefiles::          How makefiles get remade.
* Overriding Makefiles::        How to override part of one makefile
                                  with another makefile.
* Reading Makefiles::           How makefiles are parsed.
* Secondary Expansion::         How and when secondary expansion is performed.


File: make.info,  Node: Makefile Contents,  Next: Makefile Names,  Prev: Makefiles,  Up: Makefiles

3.1 What Makefiles Contain
==========================

Makefiles contain five kinds of things: "explicit rules", "implicit
rules", "variable definitions", "directives", and "comments".  Rules,
variables, and directives are described at length in later chapters.

   * An "explicit rule" says when and how to remake one or more files,
     called the rule's "targets".  It lists the other files that the
     targets depend on, called the "prerequisites" of the target, and
     may also give commands to use to create or update the targets.
     *Note Writing Rules: Rules.

   * An "implicit rule" says when and how to remake a class of files
     based on their names.  It describes how a target may depend on a
     file with a name similar to the target and gives commands to
     create or update such a target.  *Note Using Implicit Rules:
     Implicit Rules.

   * A "variable definition" is a line that specifies a text string
     value for a variable that can be substituted into the text later.
     The simple makefile example shows a variable definition for
     `objects' as a list of all object files (*note Variables Make
     Makefiles Simpler: Variables Simplify.).

   * A "directive" is a command for `make' to do something special while
     reading the makefile.  These include:

        * Reading another makefile (*note Including Other Makefiles:
          Include.).

        * Deciding (based on the values of variables) whether to use or
          ignore a part of the makefile (*note Conditional Parts of
          Makefiles: Conditionals.).

        * Defining a variable from a verbatim string containing
          multiple lines (*note Defining Variables Verbatim: Defining.).

   * `#' in a line of a makefile starts a "comment".  It and the rest
     of the line are ignored, except that a trailing backslash not
     escaped by another backslash will continue the comment across
     multiple lines.  A line containing just a comment (with perhaps
     spaces before it) is effectively blank, and is ignored.  If you
     want a literal `#', escape it with a backslash (e.g., `\#').
     Comments may appear on any line in the makefile, although they are
     treated specially in certain situations.

     Within a command script (if the line begins with a TAB character)
     the entire line is passed to the shell, just as with any other
     line that begins with a TAB.  The shell decides how to interpret
     the text: whether or not this is a comment is up to the shell.

     Within a `define' directive, comments are not ignored during the
     definition of the variable, but rather kept intact in the value of
     the variable.  When the variable is expanded they will either be
     treated as `make' comments or as command script text, depending on
     the context in which the variable is evaluated.


File: make.info,  Node: Makefile Names,  Next: Include,  Prev: Makefile Contents,  Up: Makefiles

3.2 What Name to Give Your Makefile
===================================

By default, when `make' looks for the makefile, it tries the following
names, in order: `GNUmakefile', `makefile' and `Makefile'.  

   Normally you should call your makefile either `makefile' or
`Makefile'.  (We recommend `Makefile' because it appears prominently
near the beginning of a directory listing, right near other important
files such as `README'.)  The first name checked, `GNUmakefile', is not
recommended for most makefiles.  You should use this name if you have a
makefile that is specific to GNU `make', and will not be understood by
other versions of `make'.  Other `make' programs look for `makefile' and
`Makefile', but not `GNUmakefile'.

   If `make' finds none of these names, it does not use any makefile.
Then you must specify a goal with a command argument, and `make' will
attempt to figure out how to remake it using only its built-in implicit
rules.  *Note Using Implicit Rules: Implicit Rules.

   If you want to use a nonstandard name for your makefile, you can
specify the makefile name with the `-f' or `--file' option.  The
arguments `-f NAME' or `--file=NAME' tell `make' to read the file NAME
as the makefile.  If you use more than one `-f' or `--file' option, you
can specify several makefiles.  All the makefiles are effectively
concatenated in the order specified.  The default makefile names
`GNUmakefile', `makefile' and `Makefile' are not checked automatically
if you specify `-f' or `--file'.  


File: make.info,  Node: Include,  Next: MAKEFILES Variable,  Prev: Makefile Names,  Up: Makefiles

3.3 Including Other Makefiles
=============================

The `include' directive tells `make' to suspend reading the current
makefile and read one or more other makefiles before continuing.  The
directive is a line in the makefile that looks like this:

     include FILENAMES...

FILENAMES can contain shell file name patterns.  If FILENAMES is empty,
nothing is included and no error is printed.  

   Extra spaces are allowed and ignored at the beginning of the line,
but a tab is not allowed.  (If the line begins with a tab, it will be
considered a command line.)  Whitespace is required between `include'
and the file names, and between file names; extra whitespace is ignored
there and at the end of the directive.  A comment starting with `#' is
allowed at the end of the line.  If the file names contain any variable
or function references, they are expanded.  *Note How to Use Variables:
Using Variables.

   For example, if you have three `.mk' files, `a.mk', `b.mk', and
`c.mk', and `$(bar)' expands to `bish bash', then the following
expression

     include foo *.mk $(bar)

   is equivalent to

     include foo a.mk b.mk c.mk bish bash

   When `make' processes an `include' directive, it suspends reading of
the containing makefile and reads from each listed file in turn.  When
that is finished, `make' resumes reading the makefile in which the
directive appears.

   One occasion for using `include' directives is when several programs,
handled by individual makefiles in various directories, need to use a
common set of variable definitions (*note Setting Variables: Setting.)
or pattern rules (*note Defining and Redefining Pattern Rules: Pattern
Rules.).

   Another such occasion is when you want to generate prerequisites from
source files automatically; the prerequisites can be put in a file that
is included by the main makefile.  This practice is generally cleaner
than that of somehow appending the prerequisites to the end of the main
makefile as has been traditionally done with other versions of `make'.
*Note Automatic Prerequisites::.  

   If the specified name does not start with a slash, and the file is
not found in the current directory, several other directories are
searched.  First, any directories you have specified with the `-I' or
`--include-dir' option are searched (*note Summary of Options: Options
Summary.).  Then the following directories (if they exist) are
searched, in this order: `PREFIX/include' (normally `/usr/local/include'
(1)) `/usr/gnu/include', `/usr/local/include', `/usr/include'.

   If an included makefile cannot be found in any of these directories,
a warning message is generated, but it is not an immediately fatal
error; processing of the makefile containing the `include' continues.
Once it has finished reading makefiles, `make' will try to remake any
that are out of date or don't exist.  *Note How Makefiles Are Remade:
Remaking Makefiles.  Only after it has tried to find a way to remake a
makefile and failed, will `make' diagnose the missing makefile as a
fatal error.

   If you want `make' to simply ignore a makefile which does not exist
and cannot be remade, with no error message, use the `-include'
directive instead of `include', like this:

     -include FILENAMES...

   This acts like `include' in every way except that there is no error
(not even a warning) if any of the FILENAMES do not exist.  For
compatibility with some other `make' implementations, `sinclude' is
another name for `-include'.

   ---------- Footnotes ----------

   (1) GNU Make compiled for MS-DOS and MS-Windows behaves as if PREFIX
has been defined to be the root of the DJGPP tree hierarchy.


File: make.info,  Node: MAKEFILES Variable,  Next: MAKEFILE_LIST Variable,  Prev: Include,  Up: Makefiles

3.4 The Variable `MAKEFILES'
============================

If the environment variable `MAKEFILES' is defined, `make' considers
its value as a list of names (separated by whitespace) of additional
makefiles to be read before the others.  This works much like the
`include' directive: various directories are searched for those files
(*note Including Other Makefiles: Include.).  In addition, the default
goal is never taken from one of these makefiles and it is not an error
if the files listed in `MAKEFILES' are not found.

   The main use of `MAKEFILES' is in communication between recursive
invocations of `make' (*note Recursive Use of `make': Recursion.).  It
usually is not desirable to set the environment variable before a
top-level invocation of `make', because it is usually better not to
mess with a makefile from outside.  However, if you are running `make'
without a specific makefile, a makefile in `MAKEFILES' can do useful
things to help the built-in implicit rules work better, such as
defining search paths (*note Directory Search::).

   Some users are tempted to set `MAKEFILES' in the environment
automatically on login, and program makefiles to expect this to be done.
This is a very bad idea, because such makefiles will fail to work if
run by anyone else.  It is much better to write explicit `include'
directives in the makefiles.  *Note Including Other Makefiles: Include.


File: make.info,  Node: MAKEFILE_LIST Variable,  Next: Special Variables,  Prev: MAKEFILES Variable,  Up: Makefiles

3.5 The Variable `MAKEFILE_LIST'
================================

As `make' reads various makefiles, including any obtained from the
`MAKEFILES' variable, the command line, the default files, or from
`include' directives, their names will be automatically appended to the
`MAKEFILE_LIST' variable.  They are added right before `make' begins to
parse them.

   This means that if the first thing a makefile does is examine the
last word in this variable, it will be the name of the current makefile.
Once the current makefile has used `include', however, the last word
will be the just-included makefile.

   If a makefile named `Makefile' has this content:

     name1 := $(lastword $(MAKEFILE_LIST))

     include inc.mk

     name2 := $(lastword $(MAKEFILE_LIST))

     all:
             @echo name1 = $(name1)
             @echo name2 = $(name2)

then you would expect to see this output:

     name1 = Makefile
     name2 = inc.mk

   *Note Text Functions::, for more information on the `word' and
`words' functions used above.  *Note The Two Flavors of Variables:
Flavors, for more information on simply-expanded (`:=') variable
definitions.


File: make.info,  Node: Special Variables,  Next: Remaking Makefiles,  Prev: MAKEFILE_LIST Variable,  Up: Makefiles

3.6 Other Special Variables
===========================

GNU `make' also supports other special variables.  Unless otherwise
documented here, these values lose their special properties if they are
set by a makefile or on the command line.

`.DEFAULT_GOAL'
     Sets the default goal to be used if no targets were specified on
     the command line (*note Arguments to Specify the Goals: Goals.).
     The `.DEFAULT_GOAL' variable allows you to discover the current
     default goal, restart the default goal selection algorithm by
     clearing its value, or to explicitly set the default goal.  The
     following example illustrates these cases:

          # Query the default goal.
          ifeq ($(.DEFAULT_GOAL),)
            $(warning no default goal is set)
          endif

          .PHONY: foo
          foo: ; @echo $@

          $(warning default goal is $(.DEFAULT_GOAL))

          # Reset the default goal.
          .DEFAULT_GOAL :=

          .PHONY: bar
          bar: ; @echo $@

          $(warning default goal is $(.DEFAULT_GOAL))

          # Set our own.
          .DEFAULT_GOAL := foo

     This makefile prints:

          no default goal is set
          default goal is foo
          default goal is bar
          foo

     Note that assigning more than one target name to `.DEFAULT_GOAL' is
     illegal and will result in an error.

`MAKE_RESTARTS'
     This variable is set only if this instance of `make' has restarted
     (*note How Makefiles Are Remade: Remaking Makefiles.): it will
     contain the number of times this instance has restarted.  Note
     this is not the same as recursion (counted by the `MAKELEVEL'
     variable).  You should not set, modify, or export this variable.

`.VARIABLES'
     Expands to a list of the _names_ of all global variables defined
     so far.  This includes variables which have empty values, as well
     as built-in variables (*note Variables Used by Implicit Rules:
     Implicit Variables.), but does not include any variables which are
     only defined in a target-specific context.  Note that any value
     you assign to this variable will be ignored; it will always return
     its special value.

`.FEATURES'
     Expands to a list of special features supported by this version of
     `make'.  Possible values include:

    `archives'
          Supports `ar' (archive) files using special filename syntax.
          *Note Using `make' to Update Archive Files: Archives.

    `check-symlink'
          Supports the `-L' (`--check-symlink-times') flag.  *Note
          Summary of Options: Options Summary.

    `else-if'
          Supports "else if" non-nested conditionals.  *Note Syntax of
          Conditionals: Conditional Syntax.

    `jobserver'
          Supports "job server" enhanced parallel builds.  *Note
          Parallel Execution: Parallel.

    `second-expansion'
          Supports secondary expansion of prerequisite lists.

    `order-only'
          Supports order-only prerequisites.  *Note Types of
          Prerequisites: Prerequisite Types.

    `target-specific'
          Supports target-specific and pattern-specific variable
          assignments.  *Note Target-specific Variable Values:
          Target-specific.


`.INCLUDE_DIRS'
     Expands to a list of directories that `make' searches for included
     makefiles (*note Including Other Makefiles: Include.).



File: make.info,  Node: Remaking Makefiles,  Next: Overriding Makefiles,  Prev: Special Variables,  Up: Makefiles

3.7 How Makefiles Are Remade
============================

Sometimes makefiles can be remade from other files, such as RCS or SCCS
files.  If a makefile can be remade from other files, you probably want
`make' to get an up-to-date version of the makefile to read in.

   To this end, after reading in all makefiles, `make' will consider
each as a goal target and attempt to update it.  If a makefile has a
rule which says how to update it (found either in that very makefile or
in another one) or if an implicit rule applies to it (*note Using
Implicit Rules: Implicit Rules.), it will be updated if necessary.
After all makefiles have been checked, if any have actually been
changed, `make' starts with a clean slate and reads all the makefiles
over again.  (It will also attempt to update each of them over again,
but normally this will not change them again, since they are already up
to date.)

   If you know that one or more of your makefiles cannot be remade and
you want to keep `make' from performing an implicit rule search on
them, perhaps for efficiency reasons, you can use any normal method of
preventing implicit rule lookup to do so.  For example, you can write an
explicit rule with the makefile as the target, and an empty command
string (*note Using Empty Commands: Empty Commands.).

   If the makefiles specify a double-colon rule to remake a file with
commands but no prerequisites, that file will always be remade (*note
Double-Colon::).  In the case of makefiles, a makefile that has a
double-colon rule with commands but no prerequisites will be remade
every time `make' is run, and then again after `make' starts over and
reads the makefiles in again.  This would cause an infinite loop:
`make' would constantly remake the makefile, and never do anything
else.  So, to avoid this, `make' will *not* attempt to remake makefiles
which are specified as targets of a double-colon rule with commands but
no prerequisites.

   If you do not specify any makefiles to be read with `-f' or `--file'
options, `make' will try the default makefile names; *note What Name to
Give Your Makefile: Makefile Names.  Unlike makefiles explicitly
requested with `-f' or `--file' options, `make' is not certain that
these makefiles should exist.  However, if a default makefile does not
exist but can be created by running `make' rules, you probably want the
rules to be run so that the makefile can be used.

   Therefore, if none of the default makefiles exists, `make' will try
to make each of them in the same order in which they are searched for
(*note What Name to Give Your Makefile: Makefile Names.)  until it
succeeds in making one, or it runs out of names to try.  Note that it
is not an error if `make' cannot find or make any makefile; a makefile
is not always necessary.

   When you use the `-t' or `--touch' option (*note Instead of
Executing the Commands: Instead of Execution.), you would not want to
use an out-of-date makefile to decide which targets to touch.  So the
`-t' option has no effect on updating makefiles; they are really
updated even if `-t' is specified.  Likewise, `-q' (or `--question')
and `-n' (or `--just-print') do not prevent updating of makefiles,
because an out-of-date makefile would result in the wrong output for
other targets.  Thus, `make -f mfile -n foo' will update `mfile', read
it in, and then print the commands to update `foo' and its prerequisites
without running them.  The commands printed for `foo' will be those
specified in the updated contents of `mfile'.

   However, on occasion you might actually wish to prevent updating of
even the makefiles.  You can do this by specifying the makefiles as
goals in the command line as well as specifying them as makefiles.
When the makefile name is specified explicitly as a goal, the options
`-t' and so on do apply to them.

   Thus, `make -f mfile -n mfile foo' would read the makefile `mfile',
print the commands needed to update it without actually running them,
and then print the commands needed to update `foo' without running
them.  The commands for `foo' will be those specified by the existing
contents of `mfile'.


File: make.info,  Node: Overriding Makefiles,  Next: Reading Makefiles,  Prev: Remaking Makefiles,  Up: Makefiles

3.8 Overriding Part of Another Makefile
=======================================

Sometimes it is useful to have a makefile that is mostly just like
another makefile.  You can often use the `include' directive to include
one in the other, and add more targets or variable definitions.
However, if the two makefiles give different commands for the same
target, `make' will not let you just do this.  But there is another way.

   In the containing makefile (the one that wants to include the other),
you can use a match-anything pattern rule to say that to remake any
target that cannot be made from the information in the containing
makefile, `make' should look in another makefile.  *Note Pattern
Rules::, for more information on pattern rules.

   For example, if you have a makefile called `Makefile' that says how
to make the target `foo' (and other targets), you can write a makefile
called `GNUmakefile' that contains:

     foo:
             frobnicate > foo

     %: force
             @$(MAKE) -f Makefile $@
     force: ;

   If you say `make foo', `make' will find `GNUmakefile', read it, and
see that to make `foo', it needs to run the command `frobnicate > foo'.
If you say `make bar', `make' will find no way to make `bar' in
`GNUmakefile', so it will use the commands from the pattern rule: `make
-f Makefile bar'.  If `Makefile' provides a rule for updating `bar',
`make' will apply the rule.  And likewise for any other target that
`GNUmakefile' does not say how to make.

   The way this works is that the pattern rule has a pattern of just
`%', so it matches any target whatever.  The rule specifies a
prerequisite `force', to guarantee that the commands will be run even
if the target file already exists.  We give `force' target empty
commands to prevent `make' from searching for an implicit rule to build
it--otherwise it would apply the same match-anything rule to `force'
itself and create a prerequisite loop!


File: make.info,  Node: Reading Makefiles,  Next: Secondary Expansion,  Prev: Overriding Makefiles,  Up: Makefiles

3.9 How `make' Reads a Makefile
===============================

GNU `make' does its work in two distinct phases.  During the first
phase it reads all the makefiles, included makefiles, etc. and
internalizes all the variables and their values, implicit and explicit
rules, and constructs a dependency graph of all the targets and their
prerequisites.  During the second phase, `make' uses these internal
structures to determine what targets will need to be rebuilt and to
invoke the rules necessary to do so.

   It's important to understand this two-phase approach because it has a
direct impact on how variable and function expansion happens; this is
often a source of some confusion when writing makefiles.  Here we will
present a summary of the phases in which expansion happens for different
constructs within the makefile.  We say that expansion is "immediate"
if it happens during the first phase: in this case `make' will expand
any variables or functions in that section of a construct as the
makefile is parsed.  We say that expansion is "deferred" if expansion
is not performed immediately.  Expansion of deferred construct is not
performed until either the construct appears later in an immediate
context, or until the second phase.

   You may not be familiar with some of these constructs yet.  You can
reference this section as you become familiar with them, in later
chapters.

Variable Assignment
-------------------

Variable definitions are parsed as follows:

     IMMEDIATE = DEFERRED
     IMMEDIATE ?= DEFERRED
     IMMEDIATE := IMMEDIATE
     IMMEDIATE += DEFERRED or IMMEDIATE

     define IMMEDIATE
       DEFERRED
     endef

   For the append operator, `+=', the right-hand side is considered
immediate if the variable was previously set as a simple variable
(`:='), and deferred otherwise.

Conditional Statements
----------------------

All instances of conditional syntax are parsed immediately, in their
entirety; this includes the `ifdef', `ifeq', `ifndef', and `ifneq'
forms.  Of course this means that automatic variables cannot be used in
conditional statements, as automatic variables are not set until the
command script for that rule is invoked.  If you need to use automatic
variables in a conditional you _must_ use shell conditional syntax, in
your command script proper, for these tests, not `make' conditionals.

Rule Definition
---------------

A rule is always expanded the same way, regardless of the form:

     IMMEDIATE : IMMEDIATE ; DEFERRED
     	DEFERRED

   That is, the target and prerequisite sections are expanded
immediately, and the commands used to construct the target are always
deferred.  This general rule is true for explicit rules, pattern rules,
suffix rules, static pattern rules, and simple prerequisite definitions.


File: make.info,  Node: Secondary Expansion,  Prev: Reading Makefiles,  Up: Makefiles

3.10 Secondary Expansion
========================

In the previous section we learned that GNU `make' works in two
distinct phases: a read-in phase and a target-update phase (*note How
`make' Reads a Makefile: Reading Makefiles.).  GNU make also has the
ability to enable a _second expansion_ of the prerequisites (only) for
some or all targets defined in the makefile.  In order for this second
expansion to occur, the special target `.SECONDEXPANSION' must be
defined before the first prerequisite list that makes use of this
feature.

   If that special target is defined then in between the two phases
mentioned above, right at the end of the read-in phase, all the
prerequisites of the targets defined after the special target are
expanded a _second time_.  In most circumstances this secondary
expansion will have no effect, since all variable and function
references will have been expanded during the initial parsing of the
makefiles.  In order to take advantage of the secondary expansion phase
of the parser, then, it's necessary to _escape_ the variable or
function reference in the makefile.  In this case the first expansion
merely un-escapes the reference but doesn't expand it, and expansion is
left to the secondary expansion phase.  For example, consider this
makefile:

     .SECONDEXPANSION:
     ONEVAR = onefile
     TWOVAR = twofile
     myfile: $(ONEVAR) $$(TWOVAR)

   After the first expansion phase the prerequisites list of the
`myfile' target will be `onefile' and `$(TWOVAR)'; the first
(unescaped) variable reference to ONEVAR is expanded, while the second
(escaped) variable reference is simply unescaped, without being
recognized as a variable reference.  Now during the secondary expansion
the first word is expanded again but since it contains no variable or
function references it remains the static value `onefile', while the
second word is now a normal reference to the variable TWOVAR, which is
expanded to the value `twofile'.  The final result is that there are
two prerequisites, `onefile' and `twofile'.

   Obviously, this is not a very interesting case since the same result
could more easily have been achieved simply by having both variables
appear, unescaped, in the prerequisites list.  One difference becomes
apparent if the variables are reset; consider this example:

     .SECONDEXPANSION:
     AVAR = top
     onefile: $(AVAR)
     twofile: $$(AVAR)
     AVAR = bottom

   Here the prerequisite of `onefile' will be expanded immediately, and
resolve to the value `top', while the prerequisite of `twofile' will
not be full expanded until the secondary expansion and yield a value of
`bottom'.

   This is marginally more exciting, but the true power of this feature
only becomes apparent when you discover that secondary expansions
always take place within the scope of the automatic variables for that
target.  This means that you can use variables such as `$@', `$*', etc.
during the second expansion and they will have their expected values,
just as in the command script.  All you have to do is defer the
expansion by escaping the `$'.  Also, secondary expansion occurs for
both explicit and implicit (pattern) rules.  Knowing this, the possible
uses for this feature increase dramatically.  For example:

     .SECONDEXPANSION:
     main_OBJS := main.o try.o test.o
     lib_OBJS := lib.o api.o

     main lib: $$($$@_OBJS)

   Here, after the initial expansion the prerequisites of both the
`main' and `lib' targets will be `$($@_OBJS)'.  During the secondary
expansion, the `$@' variable is set to the name of the target and so
the expansion for the `main' target will yield `$(main_OBJS)', or
`main.o try.o test.o', while the secondary expansion for the `lib'
target will yield `$(lib_OBJS)', or `lib.o api.o'.

   You can also mix functions here, as long as they are properly
escaped:

     main_SRCS := main.c try.c test.c
     lib_SRCS := lib.c api.c

     .SECONDEXPANSION:
     main lib: $$(patsubst %.c,%.o,$$($$@_SRCS))

   This version allows users to specify source files rather than object
files, but gives the same resulting prerequisites list as the previous
example.

   Evaluation of automatic variables during the secondary expansion
phase, especially of the target name variable `$$@', behaves similarly
to evaluation within command scripts.  However, there are some subtle
differences and "corner cases" which come into play for the different
types of rule definitions that `make' understands.  The subtleties of
using the different automatic variables are described below.

Secondary Expansion of Explicit Rules
-------------------------------------

During the secondary expansion of explicit rules, `$$@' and `$$%'
evaluate, respectively, to the file name of the target and, when the
target is an archive member, the target member name.  The `$$<'
variable evaluates to the first prerequisite in the first rule for this
target.  `$$^' and `$$+' evaluate to the list of all prerequisites of
rules _that have already appeared_ for the same target (`$$+' with
repetitions and `$$^' without).  The following example will help
illustrate these behaviors:

     .SECONDEXPANSION:

     foo: foo.1 bar.1 $$< $$^ $$+    # line #1

     foo: foo.2 bar.2 $$< $$^ $$+    # line #2

     foo: foo.3 bar.3 $$< $$^ $$+    # line #3

   In the first prerequisite list, all three variables (`$$<', `$$^',
and `$$+') expand to the empty string.  In the second, they will have
values `foo.1', `foo.1 bar.1', and `foo.1 bar.1' respectively.  In the
third they will have values `foo.1', `foo.1 bar.1 foo.2 bar.2', and
`foo.1 bar.1 foo.2 bar.2' respectively.

   Rules undergo secondary expansion in makefile order, except that the
rule with the command script is always evaluated last.

   The variables `$$?' and `$$*' are not available and expand to the
empty string.

Secondary Expansion of Static Pattern Rules
-------------------------------------------

Rules for secondary expansion of static pattern rules are identical to
those for explicit rules, above, with one exception: for static pattern
rules the `$$*' variable is set to the pattern stem.  As with explicit
rules, `$$?' is not available and expands to the empty string.

Secondary Expansion of Implicit Rules
-------------------------------------

As `make' searches for an implicit rule, it substitutes the stem and
then performs secondary expansion for every rule with a matching target
pattern.  The value of the automatic variables is derived in the same
fashion as for static pattern rules.  As an example:

     .SECONDEXPANSION:

     foo: bar

     foo foz: fo%: bo%

     %oo: $$< $$^ $$+ $$*

   When the implicit rule is tried for target `foo', `$$<' expands to
`bar', `$$^' expands to `bar boo', `$$+' also expands to `bar boo', and
`$$*' expands to `f'.

   Note that the directory prefix (D), as described in *Note Implicit
Rule Search Algorithm: Implicit Rule Search, is appended (after
expansion) to all the patterns in the prerequisites list.  As an
example:

     .SECONDEXPANSION:

     /tmp/foo.o:

     %.o: $$(addsuffix /%.c,foo bar) foo.h

   The prerequisite list after the secondary expansion and directory
prefix reconstruction will be `/tmp/foo/foo.c /tmp/var/bar/foo.c
foo.h'.  If you are not interested in this reconstruction, you can use
`$$*' instead of `%' in the prerequisites list.


File: make.info,  Node: Rules,  Next: Commands,  Prev: Makefiles,  Up: Top

4 Writing Rules
***************

A "rule" appears in the makefile and says when and how to remake
certain files, called the rule's "targets" (most often only one per
rule).  It lists the other files that are the "prerequisites" of the
target, and "commands" to use to create or update the target.

   The order of rules is not significant, except for determining the
"default goal": the target for `make' to consider, if you do not
otherwise specify one.  The default goal is the target of the first
rule in the first makefile.  If the first rule has multiple targets,
only the first target is taken as the default.  There are two
exceptions: a target starting with a period is not a default unless it
contains one or more slashes, `/', as well; and, a target that defines
a pattern rule has no effect on the default goal.  (*Note Defining and
Redefining Pattern Rules: Pattern Rules.)

   Therefore, we usually write the makefile so that the first rule is
the one for compiling the entire program or all the programs described
by the makefile (often with a target called `all').  *Note Arguments to
Specify the Goals: Goals.

* Menu:

* Rule Example::                An example explained.
* Rule Syntax::                 General syntax explained.
* Prerequisite Types::          There are two types of prerequisites.
* Wildcards::                   Using wildcard characters such as `*'.
* Directory Search::            Searching other directories for source files.
* Phony Targets::               Using a target that is not a real file's name.
* Force Targets::               You can use a target without commands
                                  or prerequisites to mark other targets
                                  as phony.
* Empty Targets::               When only the date matters and the
                                  files are empty.
* Special Targets::             Targets with special built-in meanings.
* Multiple Targets::            When to make use of several targets in a rule.
* Multiple Rules::              How to use several rules with the same target.
* Static Pattern::              Static pattern rules apply to multiple targets
                                  and can vary the prerequisites according to
                                  the target name.
* Double-Colon::                How to use a special kind of rule to allow
                                  several independent rules for one target.
* Automatic Prerequisites::     How to automatically generate rules giving
                                  prerequisites from source files themselves.


File: make.info,  Node: Rule Example,  Next: Rule Syntax,  Prev: Rules,  Up: Rules

4.1 Rule Example
================

Here is an example of a rule:

     foo.o : foo.c defs.h       # module for twiddling the frobs
             cc -c -g foo.c

   Its target is `foo.o' and its prerequisites are `foo.c' and
`defs.h'.  It has one command, which is `cc -c -g foo.c'.  The command
line starts with a tab to identify it as a command.

   This rule says two things:

   * How to decide whether `foo.o' is out of date: it is out of date if
     it does not exist, or if either `foo.c' or `defs.h' is more recent
     than it.

   * How to update the file `foo.o': by running `cc' as stated.  The
     command does not explicitly mention `defs.h', but we presume that
     `foo.c' includes it, and that that is why `defs.h' was added to
     the prerequisites.


File: make.info,  Node: Rule Syntax,  Next: Prerequisite Types,  Prev: Rule Example,  Up: Rules

4.2 Rule Syntax
===============

In general, a rule looks like this:

     TARGETS : PREREQUISITES
             COMMAND
             ...

or like this:

     TARGETS : PREREQUISITES ; COMMAND
             COMMAND
             ...

   The TARGETS are file names, separated by spaces.  Wildcard
characters may be used (*note Using Wildcard Characters in File Names:
Wildcards.) and a name of the form `A(M)' represents member M in
archive file A (*note Archive Members as Targets: Archive Members.).
Usually there is only one target per rule, but occasionally there is a
reason to have more (*note Multiple Targets in a Rule: Multiple
Targets.).

   The COMMAND lines start with a tab character.  The first command may
appear on the line after the prerequisites, with a tab character, or may
appear on the same line, with a semicolon.  Either way, the effect is
the same.  There are other differences in the syntax of command lines.
*Note Writing the Commands in Rules: Commands.

   Because dollar signs are used to start `make' variable references,
if you really want a dollar sign in a target or prerequisite you must
write two of them, `$$' (*note How to Use Variables: Using Variables.).
If you have enabled secondary expansion (*note Secondary Expansion::)
and you want a literal dollar sign in the prerequisites lise, you must
actually write _four_ dollar signs (`$$$$').

   You may split a long line by inserting a backslash followed by a
newline, but this is not required, as `make' places no limit on the
length of a line in a makefile.

   A rule tells `make' two things: when the targets are out of date,
and how to update them when necessary.

   The criterion for being out of date is specified in terms of the
PREREQUISITES, which consist of file names separated by spaces.
(Wildcards and archive members (*note Archives::) are allowed here too.)
A target is out of date if it does not exist or if it is older than any
of the prerequisites (by comparison of last-modification times).  The
idea is that the contents of the target file are computed based on
information in the prerequisites, so if any of the prerequisites
changes, the contents of the existing target file are no longer
necessarily valid.

   How to update is specified by COMMANDS.  These are lines to be
executed by the shell (normally `sh'), but with some extra features
(*note Writing the Commands in Rules: Commands.).


File: make.info,  Node: Prerequisite Types,  Next: Wildcards,  Prev: Rule Syntax,  Up: Rules

4.3 Types of Prerequisites
==========================

There are actually two different types of prerequisites understood by
GNU `make': normal prerequisites such as described in the previous
section, and "order-only" prerequisites.  A normal prerequisite makes
two statements: first, it imposes an order of execution of build
commands: any commands necessary to build any of a target's
prerequisites will be fully executed before any commands necessary to
build the target.  Second, it imposes a dependency relationship: if any
prerequisite is newer than the target, then the target is considered
out-of-date and must be rebuilt.

   Normally, this is exactly what you want: if a target's prerequisite
is updated, then the target should also be updated.

   Occasionally, however, you have a situation where you want to impose
a specific ordering on the rules to be invoked _without_ forcing the
target to be updated if one of those rules is executed.  In that case,
you want to define "order-only" prerequisites.  Order-only
prerequisites can be specified by placing a pipe symbol (`|') in the
prerequisites list: any prerequisites to the left of the pipe symbol
are normal; any prerequisites to the right are order-only:

     TARGETS : NORMAL-PREREQUISITES | ORDER-ONLY-PREREQUISITES

   The normal prerequisites section may of course be empty.  Also, you
may still declare multiple lines of prerequisites for the same target:
they are appended appropriately.  Note that if you declare the same
file to be both a normal and an order-only prerequisite, the normal
prerequisite takes precedence (since they are a strict superset of the
behavior of an order-only prerequisite).


File: make.info,  Node: Wildcards,  Next: Directory Search,  Prev: Prerequisite Types,  Up: Rules

4.4 Using Wildcard Characters in File Names
===========================================

A single file name can specify many files using "wildcard characters".
The wildcard characters in `make' are `*', `?' and `[...]', the same as
in the Bourne shell.  For example, `*.c' specifies a list of all the
files (in the working directory) whose names end in `.c'.

   The character `~' at the beginning of a file name also has special
significance.  If alone, or followed by a slash, it represents your home
directory.  For example `~/bin' expands to `/home/<USER>/bin'.  If the `~'
is followed by a word, the string represents the home directory of the
user named by that word.  For example `~john/bin' expands to
`/home/<USER>/bin'.  On systems which don't have a home directory for
each user (such as MS-DOS or MS-Windows), this functionality can be
simulated by setting the environment variable HOME.

   Wildcard expansion is performed by `make' automatically in targets
and in prerequisites.  In commands the shell is responsible for
wildcard expansion.  In other contexts, wildcard expansion happens only
if you request it explicitly with the `wildcard' function.

   The special significance of a wildcard character can be turned off by
preceding it with a backslash.  Thus, `foo\*bar' would refer to a
specific file whose name consists of `foo', an asterisk, and `bar'.

* Menu:

* Wildcard Examples::           Several examples
* Wildcard Pitfall::            Problems to avoid.
* Wildcard Function::           How to cause wildcard expansion where
                                  it does not normally take place.


File: make.info,  Node: Wildcard Examples,  Next: Wildcard Pitfall,  Prev: Wildcards,  Up: Wildcards

4.4.1 Wildcard Examples
-----------------------

Wildcards can be used in the commands of a rule, where they are expanded
by the shell.  For example, here is a rule to delete all the object
files:

     clean:
             rm -f *.o

   Wildcards are also useful in the prerequisites of a rule.  With the
following rule in the makefile, `make print' will print all the `.c'
files that have changed since the last time you printed them:

     print: *.c
             lpr -p $?
             touch print

This rule uses `print' as an empty target file; see *Note Empty Target
Files to Record Events: Empty Targets.  (The automatic variable `$?' is
used to print only those files that have changed; see *Note Automatic
Variables::.)

   Wildcard expansion does not happen when you define a variable.
Thus, if you write this:

     objects = *.o

then the value of the variable `objects' is the actual string `*.o'.
However, if you use the value of `objects' in a target, prerequisite or
command, wildcard expansion will take place at that time.  To set
`objects' to the expansion, instead use:

     objects := $(wildcard *.o)

*Note Wildcard Function::.


File: make.info,  Node: Wildcard Pitfall,  Next: Wildcard Function,  Prev: Wildcard Examples,  Up: Wildcards

4.4.2 Pitfalls of Using Wildcards
---------------------------------

Now here is an example of a naive way of using wildcard expansion, that
does not do what you would intend.  Suppose you would like to say that
the executable file `foo' is made from all the object files in the
directory, and you write this:

     objects = *.o

     foo : $(objects)
             cc -o foo $(CFLAGS) $(objects)

The value of `objects' is the actual string `*.o'.  Wildcard expansion
happens in the rule for `foo', so that each _existing_ `.o' file
becomes a prerequisite of `foo' and will be recompiled if necessary.

   But what if you delete all the `.o' files?  When a wildcard matches
no files, it is left as it is, so then `foo' will depend on the
oddly-named file `*.o'.  Since no such file is likely to exist, `make'
will give you an error saying it cannot figure out how to make `*.o'.
This is not what you want!

   Actually it is possible to obtain the desired result with wildcard
expansion, but you need more sophisticated techniques, including the
`wildcard' function and string substitution.  *Note The Function
`wildcard': Wildcard Function.

   Microsoft operating systems (MS-DOS and MS-Windows) use backslashes
to separate directories in pathnames, like so:

       c:\foo\bar\baz.c

   This is equivalent to the Unix-style `c:/foo/bar/baz.c' (the `c:'
part is the so-called drive letter).  When `make' runs on these
systems, it supports backslashes as well as the Unix-style forward
slashes in pathnames.  However, this support does _not_ include the
wildcard expansion, where backslash is a quote character.  Therefore,
you _must_ use Unix-style slashes in these cases.


File: make.info,  Node: Wildcard Function,  Prev: Wildcard Pitfall,  Up: Wildcards

4.4.3 The Function `wildcard'
-----------------------------

Wildcard expansion happens automatically in rules.  But wildcard
expansion does not normally take place when a variable is set, or
inside the arguments of a function.  If you want to do wildcard
expansion in such places, you need to use the `wildcard' function, like
this:

     $(wildcard PATTERN...)

This string, used anywhere in a makefile, is replaced by a
space-separated list of names of existing files that match one of the
given file name patterns.  If no existing file name matches a pattern,
then that pattern is omitted from the output of the `wildcard'
function.  Note that this is different from how unmatched wildcards
behave in rules, where they are used verbatim rather than ignored
(*note Wildcard Pitfall::).

   One use of the `wildcard' function is to get a list of all the C
source files in a directory, like this:

     $(wildcard *.c)

   We can change the list of C source files into a list of object files
by replacing the `.c' suffix with `.o' in the result, like this:

     $(patsubst %.c,%.o,$(wildcard *.c))

(Here we have used another function, `patsubst'.  *Note Functions for
String Substitution and Analysis: Text Functions.)

   Thus, a makefile to compile all C source files in the directory and
then link them together could be written as follows:

     objects := $(patsubst %.c,%.o,$(wildcard *.c))

     foo : $(objects)
             cc -o foo $(objects)

(This takes advantage of the implicit rule for compiling C programs, so
there is no need to write explicit rules for compiling the files.
*Note The Two Flavors of Variables: Flavors, for an explanation of
`:=', which is a variant of `='.)


File: make.info,  Node: Directory Search,  Next: Phony Targets,  Prev: Wildcards,  Up: Rules

4.5 Searching Directories for Prerequisites
===========================================

For large systems, it is often desirable to put sources in a separate
directory from the binaries.  The "directory search" features of `make'
facilitate this by searching several directories automatically to find
a prerequisite.  When you redistribute the files among directories, you
do not need to change the individual rules, just the search paths.

* Menu:

* General Search::              Specifying a search path that applies
                                  to every prerequisite.
* Selective Search::            Specifying a search path
                                  for a specified class of names.
* Search Algorithm::            When and how search paths are applied.
* Commands/Search::             How to write shell commands that work together
                                  with search paths.
* Implicit/Search::             How search paths affect implicit rules.
* Libraries/Search::            Directory search for link libraries.


File: make.info,  Node: General Search,  Next: Selective Search,  Prev: Directory Search,  Up: Directory Search

4.5.1 `VPATH': Search Path for All Prerequisites
------------------------------------------------

The value of the `make' variable `VPATH' specifies a list of
directories that `make' should search.  Most often, the directories are
expected to contain prerequisite files that are not in the current
directory; however, `make' uses `VPATH' as a search list for both
prerequisites and targets of rules.

   Thus, if a file that is listed as a target or prerequisite does not
exist in the current directory, `make' searches the directories listed
in `VPATH' for a file with that name.  If a file is found in one of
them, that file may become the prerequisite (see below).  Rules may then
specify the names of files in the prerequisite list as if they all
existed in the current directory.  *Note Writing Shell Commands with
Directory Search: Commands/Search.

   In the `VPATH' variable, directory names are separated by colons or
blanks.  The order in which directories are listed is the order followed
by `make' in its search.  (On MS-DOS and MS-Windows, semi-colons are
used as separators of directory names in `VPATH', since the colon can
be used in the pathname itself, after the drive letter.)

   For example,

     VPATH = src:../headers

specifies a path containing two directories, `src' and `../headers',
which `make' searches in that order.

   With this value of `VPATH', the following rule,

     foo.o : foo.c

is interpreted as if it were written like this:

     foo.o : src/foo.c

assuming the file `foo.c' does not exist in the current directory but
is found in the directory `src'.


File: make.info,  Node: Selective Search,  Next: Search Algorithm,  Prev: General Search,  Up: Directory Search

4.5.2 The `vpath' Directive
---------------------------

Similar to the `VPATH' variable, but more selective, is the `vpath'
directive (note lower case), which allows you to specify a search path
for a particular class of file names: those that match a particular
pattern.  Thus you can supply certain search directories for one class
of file names and other directories (or none) for other file names.

   There are three forms of the `vpath' directive:

`vpath PATTERN DIRECTORIES'
     Specify the search path DIRECTORIES for file names that match
     PATTERN.

     The search path, DIRECTORIES, is a list of directories to be
     searched, separated by colons (semi-colons on MS-DOS and
     MS-Windows) or blanks, just like the search path used in the
     `VPATH' variable.

`vpath PATTERN'
     Clear out the search path associated with PATTERN.

`vpath'
     Clear all search paths previously specified with `vpath'
     directives.

   A `vpath' pattern is a string containing a `%' character.  The
string must match the file name of a prerequisite that is being searched
for, the `%' character matching any sequence of zero or more characters
(as in pattern rules; *note Defining and Redefining Pattern Rules:
Pattern Rules.).  For example, `%.h' matches files that end in `.h'.
(If there is no `%', the pattern must match the prerequisite exactly,
which is not useful very often.)

   `%' characters in a `vpath' directive's pattern can be quoted with
preceding backslashes (`\').  Backslashes that would otherwise quote
`%' characters can be quoted with more backslashes.  Backslashes that
quote `%' characters or other backslashes are removed from the pattern
before it is compared to file names.  Backslashes that are not in
danger of quoting `%' characters go unmolested.

   When a prerequisite fails to exist in the current directory, if the
PATTERN in a `vpath' directive matches the name of the prerequisite
file, then the DIRECTORIES in that directive are searched just like
(and before) the directories in the `VPATH' variable.

   For example,

     vpath %.h ../headers

tells `make' to look for any prerequisite whose name ends in `.h' in
the directory `../headers' if the file is not found in the current
directory.

   If several `vpath' patterns match the prerequisite file's name, then
`make' processes each matching `vpath' directive one by one, searching
all the directories mentioned in each directive.  `make' handles
multiple `vpath' directives in the order in which they appear in the
makefile; multiple directives with the same pattern are independent of
each other.

   Thus,

     vpath %.c foo
     vpath %   blish
     vpath %.c bar

will look for a file ending in `.c' in `foo', then `blish', then `bar',
while

     vpath %.c foo:bar
     vpath %   blish

will look for a file ending in `.c' in `foo', then `bar', then `blish'.


File: make.info,  Node: Search Algorithm,  Next: Commands/Search,  Prev: Selective Search,  Up: Directory Search

4.5.3 How Directory Searches are Performed
------------------------------------------

When a prerequisite is found through directory search, regardless of
type (general or selective), the pathname located may not be the one
that `make' actually provides you in the prerequisite list.  Sometimes
the path discovered through directory search is thrown away.

   The algorithm `make' uses to decide whether to keep or abandon a
path found via directory search is as follows:

  1. If a target file does not exist at the path specified in the
     makefile, directory search is performed.

  2. If the directory search is successful, that path is kept and this
     file is tentatively stored as the target.

  3. All prerequisites of this target are examined using this same
     method.

  4. After processing the prerequisites, the target may or may not need
     to be rebuilt:

       a. If the target does _not_ need to be rebuilt, the path to the
          file found during directory search is used for any
          prerequisite lists which contain this target.  In short, if
          `make' doesn't need to rebuild the target then you use the
          path found via directory search.

       b. If the target _does_ need to be rebuilt (is out-of-date), the
          pathname found during directory search is _thrown away_, and
          the target is rebuilt using the file name specified in the
          makefile.  In short, if `make' must rebuild, then the target
          is rebuilt locally, not in the directory found via directory
          search.

   This algorithm may seem complex, but in practice it is quite often
exactly what you want.

   Other versions of `make' use a simpler algorithm: if the file does
not exist, and it is found via directory search, then that pathname is
always used whether or not the target needs to be built.  Thus, if the
target is rebuilt it is created at the pathname discovered during
directory search.

   If, in fact, this is the behavior you want for some or all of your
directories, you can use the `GPATH' variable to indicate this to
`make'.

   `GPATH' has the same syntax and format as `VPATH' (that is, a space-
or colon-delimited list of pathnames).  If an out-of-date target is
found by directory search in a directory that also appears in `GPATH',
then that pathname is not thrown away.  The target is rebuilt using the
expanded path.


File: make.info,  Node: Commands/Search,  Next: Implicit/Search,  Prev: Search Algorithm,  Up: Directory Search

4.5.4 Writing Shell Commands with Directory Search
--------------------------------------------------

When a prerequisite is found in another directory through directory
search, this cannot change the commands of the rule; they will execute
as written.  Therefore, you must write the commands with care so that
they will look for the prerequisite in the directory where `make' finds
it.

   This is done with the "automatic variables" such as `$^' (*note
Automatic Variables::).  For instance, the value of `$^' is a list of
all the prerequisites of the rule, including the names of the
directories in which they were found, and the value of `$@' is the
target.  Thus:

     foo.o : foo.c
             cc -c $(CFLAGS) $^ -o $@

(The variable `CFLAGS' exists so you can specify flags for C
compilation by implicit rules; we use it here for consistency so it will
affect all C compilations uniformly; *note Variables Used by Implicit
Rules: Implicit Variables.)

   Often the prerequisites include header files as well, which you do
not want to mention in the commands.  The automatic variable `$<' is
just the first prerequisite:

     VPATH = src:../headers
     foo.o : foo.c defs.h hack.h
             cc -c $(CFLAGS) $< -o $@


File: make.info,  Node: Implicit/Search,  Next: Libraries/Search,  Prev: Commands/Search,  Up: Directory Search

4.5.5 Directory Search and Implicit Rules
-----------------------------------------

The search through the directories specified in `VPATH' or with `vpath'
also happens during consideration of implicit rules (*note Using
Implicit Rules: Implicit Rules.).

   For example, when a file `foo.o' has no explicit rule, `make'
considers implicit rules, such as the built-in rule to compile `foo.c'
if that file exists.  If such a file is lacking in the current
directory, the appropriate directories are searched for it.  If `foo.c'
exists (or is mentioned in the makefile) in any of the directories, the
implicit rule for C compilation is applied.

   The commands of implicit rules normally use automatic variables as a
matter of necessity; consequently they will use the file names found by
directory search with no extra effort.


File: make.info,  Node: Libraries/Search,  Prev: Implicit/Search,  Up: Directory Search

4.5.6 Directory Search for Link Libraries
-----------------------------------------

Directory search applies in a special way to libraries used with the
linker.  This special feature comes into play when you write a
prerequisite whose name is of the form `-lNAME'.  (You can tell
something strange is going on here because the prerequisite is normally
the name of a file, and the _file name_ of a library generally looks
like `libNAME.a', not like `-lNAME'.)

   When a prerequisite's name has the form `-lNAME', `make' handles it
specially by searching for the file `libNAME.so' in the current
directory, in directories specified by matching `vpath' search paths
and the `VPATH' search path, and then in the directories `/lib',
`/usr/lib', and `PREFIX/lib' (normally `/usr/local/lib', but
MS-DOS/MS-Windows versions of `make' behave as if PREFIX is defined to
be the root of the DJGPP installation tree).

   If that file is not found, then the file `libNAME.a' is searched
for, in the same directories as above.

   For example, if there is a `/usr/lib/libcurses.a' library on your
system (and no `/usr/lib/libcurses.so' file), then

     foo : foo.c -lcurses
             cc $^ -o $@

would cause the command `cc foo.c /usr/lib/libcurses.a -o foo' to be
executed when `foo' is older than `foo.c' or than
`/usr/lib/libcurses.a'.

   Although the default set of files to be searched for is `libNAME.so'
and `libNAME.a', this is customizable via the `.LIBPATTERNS' variable.
Each word in the value of this variable is a pattern string.  When a
prerequisite like `-lNAME' is seen, `make' will replace the percent in
each pattern in the list with NAME and perform the above directory
searches using that library filename.  If no library is found, the next
word in the list will be used.

   The default value for `.LIBPATTERNS' is `lib%.so lib%.a', which
provides the default behavior described above.

   You can turn off link library expansion completely by setting this
variable to an empty value.


File: make.info,  Node: Phony Targets,  Next: Force Targets,  Prev: Directory Search,  Up: Rules

4.6 Phony Targets
=================

A phony target is one that is not really the name of a file.  It is
just a name for some commands to be executed when you make an explicit
request.  There are two reasons to use a phony target: to avoid a
conflict with a file of the same name, and to improve performance.

   If you write a rule whose commands will not create the target file,
the commands will be executed every time the target comes up for
remaking.  Here is an example:

     clean:
             rm *.o temp

Because the `rm' command does not create a file named `clean', probably
no such file will ever exist.  Therefore, the `rm' command will be
executed every time you say `make clean'.  

   The phony target will cease to work if anything ever does create a
file named `clean' in this directory.  Since it has no prerequisites,
the file `clean' would inevitably be considered up to date, and its
commands would not be executed.  To avoid this problem, you can
explicitly declare the target to be phony, using the special target
`.PHONY' (*note Special Built-in Target Names: Special Targets.) as
follows:

     .PHONY : clean

Once this is done, `make clean' will run the commands regardless of
whether there is a file named `clean'.

   Since it knows that phony targets do not name actual files that
could be remade from other files, `make' skips the implicit rule search
for phony targets (*note Implicit Rules::).  This is why declaring a
target phony is good for performance, even if you are not worried about
the actual file existing.

   Thus, you first write the line that states that `clean' is a phony
target, then you write the rule, like this:

     .PHONY: clean
     clean:
             rm *.o temp

   Another example of the usefulness of phony targets is in conjunction
with recursive invocations of `make' (for more information, see *Note
Recursive Use of `make': Recursion.).  In this case the makefile will
often contain a variable which lists a number of subdirectories to be
built.  One way to handle this is with one rule whose command is a
shell loop over the subdirectories, like this:

     SUBDIRS = foo bar baz

     subdirs:
             for dir in $(SUBDIRS); do \
               $(MAKE) -C $$dir; \
             done

   There are a few problems with this method, however.  First, any error
detected in a submake is not noted by this rule, so it will continue to
build the rest of the directories even when one fails.  This can be
overcome by adding shell commands to note the error and exit, but then
it will do so even if `make' is invoked with the `-k' option, which is
unfortunate.  Second, and perhaps more importantly, you cannot take
advantage of `make''s ability to build targets in parallel (*note
Parallel Execution: Parallel.), since there is only one rule.

   By declaring the subdirectories as phony targets (you must do this as
the subdirectory obviously always exists; otherwise it won't be built)
you can remove these problems:

     SUBDIRS = foo bar baz

     .PHONY: subdirs $(SUBDIRS)

     subdirs: $(SUBDIRS)

     $(SUBDIRS):
             $(MAKE) -C $@

     foo: baz

   Here we've also declared that the `foo' subdirectory cannot be built
until after the `baz' subdirectory is complete; this kind of
relationship declaration is particularly important when attempting
parallel builds.

   A phony target should not be a prerequisite of a real target file;
if it is, its commands are run every time `make' goes to update that
file.  As long as a phony target is never a prerequisite of a real
target, the phony target commands will be executed only when the phony
target is a specified goal (*note Arguments to Specify the Goals:
Goals.).

   Phony targets can have prerequisites.  When one directory contains
multiple programs, it is most convenient to describe all of the
programs in one makefile `./Makefile'.  Since the target remade by
default will be the first one in the makefile, it is common to make
this a phony target named `all' and give it, as prerequisites, all the
individual programs.  For example:

     all : prog1 prog2 prog3
     .PHONY : all

     prog1 : prog1.o utils.o
             cc -o prog1 prog1.o utils.o

     prog2 : prog2.o
             cc -o prog2 prog2.o

     prog3 : prog3.o sort.o utils.o
             cc -o prog3 prog3.o sort.o utils.o

Now you can say just `make' to remake all three programs, or specify as
arguments the ones to remake (as in `make prog1 prog3').  Phoniness is
not inherited: the prerequisites of a phony target are not themselves
phony, unless explicitly declared to be so.

   When one phony target is a prerequisite of another, it serves as a
subroutine of the other.  For example, here `make cleanall' will delete
the object files, the difference files, and the file `program':

     .PHONY: cleanall cleanobj cleandiff

     cleanall : cleanobj cleandiff
             rm program

     cleanobj :
             rm *.o

     cleandiff :
             rm *.diff


File: make.info,  Node: Force Targets,  Next: Empty Targets,  Prev: Phony Targets,  Up: Rules

4.7 Rules without Commands or Prerequisites
===========================================

If a rule has no prerequisites or commands, and the target of the rule
is a nonexistent file, then `make' imagines this target to have been
updated whenever its rule is run.  This implies that all targets
depending on this one will always have their commands run.

   An example will illustrate this:

     clean: FORCE
             rm $(objects)
     FORCE:

   Here the target `FORCE' satisfies the special conditions, so the
target `clean' that depends on it is forced to run its commands.  There
is nothing special about the name `FORCE', but that is one name
commonly used this way.

   As you can see, using `FORCE' this way has the same results as using
`.PHONY: clean'.

   Using `.PHONY' is more explicit and more efficient.  However, other
versions of `make' do not support `.PHONY'; thus `FORCE' appears in
many makefiles.  *Note Phony Targets::.


File: make.info,  Node: Empty Targets,  Next: Special Targets,  Prev: Force Targets,  Up: Rules

4.8 Empty Target Files to Record Events
=======================================

The "empty target" is a variant of the phony target; it is used to hold
commands for an action that you request explicitly from time to time.
Unlike a phony target, this target file can really exist; but the file's
contents do not matter, and usually are empty.

   The purpose of the empty target file is to record, with its
last-modification time, when the rule's commands were last executed.  It
does so because one of the commands is a `touch' command to update the
target file.

   The empty target file should have some prerequisites (otherwise it
doesn't make sense).  When you ask to remake the empty target, the
commands are executed if any prerequisite is more recent than the
target; in other words, if a prerequisite has changed since the last
time you remade the target.  Here is an example:

     print: foo.c bar.c
             lpr -p $?
             touch print
   
With this rule, `make print' will execute the `lpr' command if either
source file has changed since the last `make print'.  The automatic
variable `$?' is used to print only those files that have changed
(*note Automatic Variables::).


File: make.info,  Node: Special Targets,  Next: Multiple Targets,  Prev: Empty Targets,  Up: Rules

4.9 Special Built-in Target Names
=================================

Certain names have special meanings if they appear as targets.

`.PHONY'
     The prerequisites of the special target `.PHONY' are considered to
     be phony targets.  When it is time to consider such a target,
     `make' will run its commands unconditionally, regardless of
     whether a file with that name exists or what its last-modification
     time is.  *Note Phony Targets: Phony Targets.

`.SUFFIXES'
     The prerequisites of the special target `.SUFFIXES' are the list
     of suffixes to be used in checking for suffix rules.  *Note
     Old-Fashioned Suffix Rules: Suffix Rules.

`.DEFAULT'
     The commands specified for `.DEFAULT' are used for any target for
     which no rules are found (either explicit rules or implicit rules).
     *Note Last Resort::.  If `.DEFAULT' commands are specified, every
     file mentioned as a prerequisite, but not as a target in a rule,
     will have these commands executed on its behalf.  *Note Implicit
     Rule Search Algorithm: Implicit Rule Search.

`.PRECIOUS'
     The targets which `.PRECIOUS' depends on are given the following
     special treatment: if `make' is killed or interrupted during the
     execution of their commands, the target is not deleted.  *Note
     Interrupting or Killing `make': Interrupts.  Also, if the target
     is an intermediate file, it will not be deleted after it is no
     longer needed, as is normally done.  *Note Chains of Implicit
     Rules: Chained Rules.  In this latter respect it overlaps with the
     `.SECONDARY' special target.

     You can also list the target pattern of an implicit rule (such as
     `%.o') as a prerequisite file of the special target `.PRECIOUS' to
     preserve intermediate files created by rules whose target patterns
     match that file's name.

`.INTERMEDIATE'
     The targets which `.INTERMEDIATE' depends on are treated as
     intermediate files.  *Note Chains of Implicit Rules: Chained Rules.
     `.INTERMEDIATE' with no prerequisites has no effect.

`.SECONDARY'
     The targets which `.SECONDARY' depends on are treated as
     intermediate files, except that they are never automatically
     deleted.  *Note Chains of Implicit Rules: Chained Rules.

     `.SECONDARY' with no prerequisites causes all targets to be treated
     as secondary (i.e., no target is removed because it is considered
     intermediate).

`.SECONDEXPANSION'
     If `.SECONDEXPANSION' is mentioned as a target anywhere in the
     makefile, then all prerequisite lists defined _after_ it appears
     will be expanded a second time after all makefiles have been read
     in.  *Note Secondary Expansion: Secondary Expansion.

     The prerequisites of the special target `.SUFFIXES' are the list
     of suffixes to be used in checking for suffix rules.  *Note
     Old-Fashioned Suffix Rules: Suffix Rules.

`.DELETE_ON_ERROR'
     If `.DELETE_ON_ERROR' is mentioned as a target anywhere in the
     makefile, then `make' will delete the target of a rule if it has
     changed and its commands exit with a nonzero exit status, just as
     it does when it receives a signal.  *Note Errors in Commands:
     Errors.

`.IGNORE'
     If you specify prerequisites for `.IGNORE', then `make' will
     ignore errors in execution of the commands run for those particular
     files.  The commands for `.IGNORE' are not meaningful.

     If mentioned as a target with no prerequisites, `.IGNORE' says to
     ignore errors in execution of commands for all files.  This usage
     of `.IGNORE' is supported only for historical compatibility.  Since
     this affects every command in the makefile, it is not very useful;
     we recommend you use the more selective ways to ignore errors in
     specific commands.  *Note Errors in Commands: Errors.

`.LOW_RESOLUTION_TIME'
     If you specify prerequisites for `.LOW_RESOLUTION_TIME', `make'
     assumes that these files are created by commands that generate low
     resolution time stamps.  The commands for `.LOW_RESOLUTION_TIME'
     are not meaningful.

     The high resolution file time stamps of many modern hosts lessen
     the chance of `make' incorrectly concluding that a file is up to
     date.  Unfortunately, these hosts provide no way to set a high
     resolution file time stamp, so commands like `cp -p' that
     explicitly set a file's time stamp must discard its subsecond
     part.  If a file is created by such a command, you should list it
     as a prerequisite of `.LOW_RESOLUTION_TIME' so that `make' does
     not mistakenly conclude that the file is out of date.  For example:

          .LOW_RESOLUTION_TIME: dst
          dst: src
                  cp -p src dst

     Since `cp -p' discards the subsecond part of `src''s time stamp,
     `dst' is typically slightly older than `src' even when it is up to
     date.  The `.LOW_RESOLUTION_TIME' line causes `make' to consider
     `dst' to be up to date if its time stamp is at the start of the
     same second that `src''s time stamp is in.

     Due to a limitation of the archive format, archive member time
     stamps are always low resolution.  You need not list archive
     members as prerequisites of `.LOW_RESOLUTION_TIME', as `make' does
     this automatically.

`.SILENT'
     If you specify prerequisites for `.SILENT', then `make' will not
     print the commands to remake those particular files before
     executing them.  The commands for `.SILENT' are not meaningful.

     If mentioned as a target with no prerequisites, `.SILENT' says not
     to print any commands before executing them.  This usage of
     `.SILENT' is supported only for historical compatibility.  We
     recommend you use the more selective ways to silence specific
     commands.  *Note Command Echoing: Echoing.  If you want to silence
     all commands for a particular run of `make', use the `-s' or
     `--silent' option (*note Options Summary::).

`.EXPORT_ALL_VARIABLES'
     Simply by being mentioned as a target, this tells `make' to export
     all variables to child processes by default.  *Note Communicating
     Variables to a Sub-`make': Variables/Recursion.

`.NOTPARALLEL'
     If `.NOTPARALLEL' is mentioned as a target, then this invocation of
     `make' will be run serially, even if the `-j' option is given.
     Any recursively invoked `make' command will still be run in
     parallel (unless its makefile contains this target).  Any
     prerequisites on this target are ignored.

   Any defined implicit rule suffix also counts as a special target if
it appears as a target, and so does the concatenation of two suffixes,
such as `.c.o'.  These targets are suffix rules, an obsolete way of
defining implicit rules (but a way still widely used).  In principle,
any target name could be special in this way if you break it in two and
add both pieces to the suffix list.  In practice, suffixes normally
begin with `.', so these special target names also begin with `.'.
*Note Old-Fashioned Suffix Rules: Suffix Rules.


File: make.info,  Node: Multiple Targets,  Next: Multiple Rules,  Prev: Special Targets,  Up: Rules

4.10 Multiple Targets in a Rule
===============================

A rule with multiple targets is equivalent to writing many rules, each
with one target, and all identical aside from that.  The same commands
apply to all the targets, but their effects may vary because you can
substitute the actual target name into the command using `$@'.  The
rule contributes the same prerequisites to all the targets also.

   This is useful in two cases.

   * You want just prerequisites, no commands.  For example:

          kbd.o command.o files.o: command.h

     gives an additional prerequisite to each of the three object files
     mentioned.

   * Similar commands work for all the targets.  The commands do not
     need to be absolutely identical, since the automatic variable `$@'
     can be used to substitute the particular target to be remade into
     the commands (*note Automatic Variables::).  For example:

          bigoutput littleoutput : text.g
                  generate text.g -$(subst output,,$@) > $@
     
     is equivalent to

          bigoutput : text.g
                  generate text.g -big > bigoutput
          littleoutput : text.g
                  generate text.g -little > littleoutput

     Here we assume the hypothetical program `generate' makes two types
     of output, one if given `-big' and one if given `-little'.  *Note
     Functions for String Substitution and Analysis: Text Functions,
     for an explanation of the `subst' function.

   Suppose you would like to vary the prerequisites according to the
target, much as the variable `$@' allows you to vary the commands.  You
cannot do this with multiple targets in an ordinary rule, but you can
do it with a "static pattern rule".  *Note Static Pattern Rules: Static
Pattern.


File: make.info,  Node: Multiple Rules,  Next: Static Pattern,  Prev: Multiple Targets,  Up: Rules

4.11 Multiple Rules for One Target
==================================

One file can be the target of several rules.  All the prerequisites
mentioned in all the rules are merged into one list of prerequisites for
the target.  If the target is older than any prerequisite from any rule,
the commands are executed.

   There can only be one set of commands to be executed for a file.  If
more than one rule gives commands for the same file, `make' uses the
last set given and prints an error message.  (As a special case, if the
file's name begins with a dot, no error message is printed.  This odd
behavior is only for compatibility with other implementations of
`make'... you should avoid using it).  Occasionally it is useful to
have the same target invoke multiple commands which are defined in
different parts of your makefile; you can use "double-colon rules"
(*note Double-Colon::) for this.

   An extra rule with just prerequisites can be used to give a few extra
prerequisites to many files at once.  For example, makefiles often have
a variable, such as `objects', containing a list of all the compiler
output files in the system being made.  An easy way to say that all of
them must be recompiled if `config.h' changes is to write the following:

     objects = foo.o bar.o
     foo.o : defs.h
     bar.o : defs.h test.h
     $(objects) : config.h

   This could be inserted or taken out without changing the rules that
really specify how to make the object files, making it a convenient
form to use if you wish to add the additional prerequisite
intermittently.

   Another wrinkle is that the additional prerequisites could be
specified with a variable that you set with a command argument to `make'
(*note Overriding Variables: Overriding.).  For example,

     extradeps=
     $(objects) : $(extradeps)

means that the command `make extradeps=foo.h' will consider `foo.h' as
a prerequisite of each object file, but plain `make' will not.

   If none of the explicit rules for a target has commands, then `make'
searches for an applicable implicit rule to find some commands *note
Using Implicit Rules: Implicit Rules.).


File: make.info,  Node: Static Pattern,  Next: Double-Colon,  Prev: Multiple Rules,  Up: Rules

4.12 Static Pattern Rules
=========================

"Static pattern rules" are rules which specify multiple targets and
construct the prerequisite names for each target based on the target
name.  They are more general than ordinary rules with multiple targets
because the targets do not have to have identical prerequisites.  Their
prerequisites must be _analogous_, but not necessarily _identical_.

* Menu:

* Static Usage::                The syntax of static pattern rules.
* Static versus Implicit::      When are they better than implicit rules?


File: make.info,  Node: Static Usage,  Next: Static versus Implicit,  Prev: Static Pattern,  Up: Static Pattern

4.12.1 Syntax of Static Pattern Rules
-------------------------------------

Here is the syntax of a static pattern rule:

     TARGETS ...: TARGET-PATTERN: PREREQ-PATTERNS ...
             COMMANDS
             ...

The TARGETS list specifies the targets that the rule applies to.  The
targets can contain wildcard characters, just like the targets of
ordinary rules (*note Using Wildcard Characters in File Names:
Wildcards.).

   The TARGET-PATTERN and PREREQ-PATTERNS say how to compute the
prerequisites of each target.  Each target is matched against the
TARGET-PATTERN to extract a part of the target name, called the "stem".
This stem is substituted into each of the PREREQ-PATTERNS to make the
prerequisite names (one from each PREREQ-PATTERN).

   Each pattern normally contains the character `%' just once.  When the
TARGET-PATTERN matches a target, the `%' can match any part of the
target name; this part is called the "stem".  The rest of the pattern
must match exactly.  For example, the target `foo.o' matches the
pattern `%.o', with `foo' as the stem.  The targets `foo.c' and
`foo.out' do not match that pattern.

   The prerequisite names for each target are made by substituting the
stem for the `%' in each prerequisite pattern.  For example, if one
prerequisite pattern is `%.c', then substitution of the stem `foo'
gives the prerequisite name `foo.c'.  It is legitimate to write a
prerequisite pattern that does not contain `%'; then this prerequisite
is the same for all targets.

   `%' characters in pattern rules can be quoted with preceding
backslashes (`\').  Backslashes that would otherwise quote `%'
characters can be quoted with more backslashes.  Backslashes that quote
`%' characters or other backslashes are removed from the pattern before
it is compared to file names or has a stem substituted into it.
Backslashes that are not in danger of quoting `%' characters go
unmolested.  For example, the pattern `the\%weird\\%pattern\\' has
`the%weird\' preceding the operative `%' character, and `pattern\\'
following it.  The final two backslashes are left alone because they
cannot affect any `%' character.

   Here is an example, which compiles each of `foo.o' and `bar.o' from
the corresponding `.c' file:

     objects = foo.o bar.o

     all: $(objects)

     $(objects): %.o: %.c
             $(CC) -c $(CFLAGS) $< -o $@

Here `$<' is the automatic variable that holds the name of the
prerequisite and `$@' is the automatic variable that holds the name of
the target; see *Note Automatic Variables::.

   Each target specified must match the target pattern; a warning is
issued for each target that does not.  If you have a list of files,
only some of which will match the pattern, you can use the `filter'
function to remove nonmatching file names (*note Functions for String
Substitution and Analysis: Text Functions.):

     files = foo.elc bar.o lose.o

     $(filter %.o,$(files)): %.o: %.c
             $(CC) -c $(CFLAGS) $< -o $@
     $(filter %.elc,$(files)): %.elc: %.el
             emacs -f batch-byte-compile $<

In this example the result of `$(filter %.o,$(files))' is `bar.o
lose.o', and the first static pattern rule causes each of these object
files to be updated by compiling the corresponding C source file.  The
result of `$(filter %.elc,$(files))' is `foo.elc', so that file is made
from `foo.el'.

   Another example shows how to use `$*' in static pattern rules: 

     bigoutput littleoutput : %output : text.g
             generate text.g -$* > $@

When the `generate' command is run, `$*' will expand to the stem,
either `big' or `little'.


File: make.info,  Node: Static versus Implicit,  Prev: Static Usage,  Up: Static Pattern

4.12.2 Static Pattern Rules versus Implicit Rules
-------------------------------------------------

A static pattern rule has much in common with an implicit rule defined
as a pattern rule (*note Defining and Redefining Pattern Rules: Pattern
Rules.).  Both have a pattern for the target and patterns for
constructing the names of prerequisites.  The difference is in how
`make' decides _when_ the rule applies.

   An implicit rule _can_ apply to any target that matches its pattern,
but it _does_ apply only when the target has no commands otherwise
specified, and only when the prerequisites can be found.  If more than
one implicit rule appears applicable, only one applies; the choice
depends on the order of rules.

   By contrast, a static pattern rule applies to the precise list of
targets that you specify in the rule.  It cannot apply to any other
target and it invariably does apply to each of the targets specified.
If two conflicting rules apply, and both have commands, that's an error.

   The static pattern rule can be better than an implicit rule for these
reasons:

   * You may wish to override the usual implicit rule for a few files
     whose names cannot be categorized syntactically but can be given
     in an explicit list.

   * If you cannot be sure of the precise contents of the directories
     you are using, you may not be sure which other irrelevant files
     might lead `make' to use the wrong implicit rule.  The choice
     might depend on the order in which the implicit rule search is
     done.  With static pattern rules, there is no uncertainty: each
     rule applies to precisely the targets specified.


File: make.info,  Node: Double-Colon,  Next: Automatic Prerequisites,  Prev: Static Pattern,  Up: Rules

4.13 Double-Colon Rules
=======================

"Double-colon" rules are rules written with `::' instead of `:' after
the target names.  They are handled differently from ordinary rules
when the same target appears in more than one rule.

   When a target appears in multiple rules, all the rules must be the
same type: all ordinary, or all double-colon.  If they are
double-colon, each of them is independent of the others.  Each
double-colon rule's commands are executed if the target is older than
any prerequisites of that rule.  If there are no prerequisites for that
rule, its commands are always executed (even if the target already
exists).  This can result in executing none, any, or all of the
double-colon rules.

   Double-colon rules with the same target are in fact completely
separate from one another.  Each double-colon rule is processed
individually, just as rules with different targets are processed.

   The double-colon rules for a target are executed in the order they
appear in the makefile.  However, the cases where double-colon rules
really make sense are those where the order of executing the commands
would not matter.

   Double-colon rules are somewhat obscure and not often very useful;
they provide a mechanism for cases in which the method used to update a
target differs depending on which prerequisite files caused the update,
and such cases are rare.

   Each double-colon rule should specify commands; if it does not, an
implicit rule will be used if one applies.  *Note Using Implicit Rules:
Implicit Rules.


File: make.info,  Node: Automatic Prerequisites,  Prev: Double-Colon,  Up: Rules

4.14 Generating Prerequisites Automatically
===========================================

In the makefile for a program, many of the rules you need to write often
say only that some object file depends on some header file.  For
example, if `main.c' uses `defs.h' via an `#include', you would write:

     main.o: defs.h

You need this rule so that `make' knows that it must remake `main.o'
whenever `defs.h' changes.  You can see that for a large program you
would have to write dozens of such rules in your makefile.  And, you
must always be very careful to update the makefile every time you add
or remove an `#include'.  

   To avoid this hassle, most modern C compilers can write these rules
for you, by looking at the `#include' lines in the source files.
Usually this is done with the `-M' option to the compiler.  For
example, the command:

     cc -M main.c

generates the output:

     main.o : main.c defs.h

Thus you no longer have to write all those rules yourself.  The
compiler will do it for you.

   Note that such a prerequisite constitutes mentioning `main.o' in a
makefile, so it can never be considered an intermediate file by implicit
rule search.  This means that `make' won't ever remove the file after
using it; *note Chains of Implicit Rules: Chained Rules.

   With old `make' programs, it was traditional practice to use this
compiler feature to generate prerequisites on demand with a command like
`make depend'.  That command would create a file `depend' containing
all the automatically-generated prerequisites; then the makefile could
use `include' to read them in (*note Include::).

   In GNU `make', the feature of remaking makefiles makes this practice
obsolete--you need never tell `make' explicitly to regenerate the
prerequisites, because it always regenerates any makefile that is out
of date.  *Note Remaking Makefiles::.

   The practice we recommend for automatic prerequisite generation is
to have one makefile corresponding to each source file.  For each
source file `NAME.c' there is a makefile `NAME.d' which lists what
files the object file `NAME.o' depends on.  That way only the source
files that have changed need to be rescanned to produce the new
prerequisites.

   Here is the pattern rule to generate a file of prerequisites (i.e.,
a makefile) called `NAME.d' from a C source file called `NAME.c':

     %.d: %.c
             @set -e; rm -f $@; \
              $(CC) -M $(CPPFLAGS) $< > $@.$$$$; \
              sed 's,\($*\)\.o[ :]*,\1.o $@ : ,g' < $@.$$$$ > $@; \
              rm -f $@.$$$$

*Note Pattern Rules::, for information on defining pattern rules.  The
`-e' flag to the shell causes it to exit immediately if the `$(CC)'
command (or any other command) fails (exits with a nonzero status).  

   With the GNU C compiler, you may wish to use the `-MM' flag instead
of `-M'.  This omits prerequisites on system header files.  *Note
Options Controlling the Preprocessor: (gcc.info)Preprocessor Options,
for details.

   The purpose of the `sed' command is to translate (for example):

     main.o : main.c defs.h

into:

     main.o main.d : main.c defs.h

This makes each `.d' file depend on all the source and header files
that the corresponding `.o' file depends on.  `make' then knows it must
regenerate the prerequisites whenever any of the source or header files
changes.

   Once you've defined the rule to remake the `.d' files, you then use
the `include' directive to read them all in.  *Note Include::.  For
example:

     sources = foo.c bar.c

     include $(sources:.c=.d)

(This example uses a substitution variable reference to translate the
list of source files `foo.c bar.c' into a list of prerequisite
makefiles, `foo.d bar.d'.  *Note Substitution Refs::, for full
information on substitution references.)  Since the `.d' files are
makefiles like any others, `make' will remake them as necessary with no
further work from you.  *Note Remaking Makefiles::.

   Note that the `.d' files contain target definitions; you should be
sure to place the `include' directive _after_ the first, default goal
in your makefiles or run the risk of having a random object file become
the default goal.  *Note How Make Works::.


File: make.info,  Node: Commands,  Next: Using Variables,  Prev: Rules,  Up: Top

5 Writing the Commands in Rules
*******************************

The commands of a rule consist of one or more shell command lines to be
executed, one at a time, in the order they appear.  Typically, the
result of executing these commands is that the target of the rule is
brought up to date.

   Users use many different shell programs, but commands in makefiles
are always interpreted by `/bin/sh' unless the makefile specifies
otherwise.  *Note Command Execution: Execution.

* Menu:

* Command Syntax::              Command syntax features and pitfalls.
* Echoing::                     How to control when commands are echoed.
* Execution::                   How commands are executed.
* Parallel::                    How commands can be executed in parallel.
* Errors::                      What happens after a command execution error.
* Interrupts::                  What happens when a command is interrupted.
* Recursion::                   Invoking `make' from makefiles.
* Sequences::                   Defining canned sequences of commands.
* Empty Commands::              Defining useful, do-nothing commands.


File: make.info,  Node: Command Syntax,  Next: Echoing,  Prev: Commands,  Up: Commands

5.1 Command Syntax
==================

Makefiles have the unusual property that there are really two distinct
syntaxes in one file.  Most of the makefile uses `make' syntax (*note
Writing Makefiles: Makefiles.).  However, commands are meant to be
interpreted by the shell and so they are written using shell syntax.
The `make' program does not try to understand shell syntax: it performs
only a very few specific translations on the content of the command
before handing it to the shell.

   Each command line must start with a tab, except that the first
command line may be attached to the target-and-prerequisites line with a
semicolon in between.  _Any_ line in the makefile that begins with a
tab and appears in a "rule context" (that is, after a rule has been
started until another rule or variable definition) will be considered a
command line for that rule.  Blank lines and lines of just comments may
appear among the command lines; they are ignored.

   Some consequences of these rules include:

   * A blank line that begins with a tab is not blank: it's an empty
     command (*note Empty Commands::).

   * A comment in a command line is not a `make' comment; it will be
     passed to the shell as-is.  Whether the shell treats it as a
     comment or not depends on your shell.

   * A variable definition in a "rule context" which is indented by a
     tab as the first character on the line, will be considered a
     command line, not a `make' variable definition, and passed to the
     shell.

   * A conditional expression (`ifdef', `ifeq', etc. *note Syntax of
     Conditionals: Conditional Syntax.) in a "rule context" which is
     indented by a tab as the first character on the line, will be
     considered a command line and be passed to the shell.


* Menu:

* Splitting Lines::             Breaking long command lines for readability.
* Variables in Commands::       Using `make' variables in commands.


File: make.info,  Node: Splitting Lines,  Next: Variables in Commands,  Prev: Command Syntax,  Up: Command Syntax

5.1.1 Splitting Command Lines
-----------------------------

One of the few ways in which `make' does interpret command lines is
checking for a backslash just before the newline.  As in normal
makefile syntax, a single command can be split into multiple lines in
the makefile by placing a backslash before each newline.  A sequence of
lines like this is considered a single command, and one instance of the
shell will be invoked to run it.

   However, in contrast to how they are treated in other places in a
makefile, backslash-newline pairs are _not_ removed from the command.
Both the backslash and the newline characters are preserved and passed
to the shell.  How the backslash-newline is interpreted depends on your
shell.  If the first character of the next line after the
backslash-newline is a tab, then that tab (and only that tab) is
removed.  Whitespace is never added to the command.

   For example, this makefile:

     all :
             @echo no\
     space
             @echo no\
             space
             @echo one \
             space
             @echo one\
              space

consists of four separate shell commands where the output is:

     nospace
     nospace
     one space
     one space

   As a more complex example, this makefile:

     all : ; @echo 'hello \
             world' ; echo "hello \
         world"

will run one shell with a command script of:

     echo 'hello \
     world' ; echo "hello \
         world"

which, according to shell quoting rules, will yield the following
output:

     hello \
     world
     hello     world

Notice how the backslash/newline pair was removed inside the string
quoted with double quotes (`"..."'), but not from the string quoted
with single quotes (`'...'').  This is the way the default shell
(`/bin/sh') handles backslash/newline pairs.  If you specify a
different shell in your makefiles it may treat them differently.

   Sometimes you want to split a long line inside of single quotes, but
you don't want the backslash-newline to appear in the quoted content.
This is often the case when passing scripts to languages such as Perl,
where extraneous backslashes inside the script can change its meaning
or even be a syntax error.  One simple way of handling this is to place
the quoted string, or even the entire command, into a `make' variable
then use the variable in the command.  In this situation the newline
quoting rules for makefiles will be used, and the backslash-newline
will be removed.  If we rewrite our example above using this method:

     HELLO = 'hello \
     world'

     all : ; @echo $(HELLO)

we will get output like this:

     hello world

   If you like, you can also use target-specific variables (*note
Target-specific Variable Values: Target-specific.) to obtain a tighter
correspondence between the variable and the command that uses it.


File: make.info,  Node: Variables in Commands,  Prev: Splitting Lines,  Up: Command Syntax

5.1.2 Using Variables in Commands
---------------------------------

The other way in which `make' processes commands is by expanding any
variable references in them (*note Basics of Variable References:
Reference.).  This occurs after make has finished reading all the
makefiles and the target is determined to be out of date; so, the
commands for targets which are not rebuilt are never expanded.

   Variable and function references in commands have identical syntax
and semantics to references elsewhere in the makefile.  They also have
the same quoting rules: if you want a dollar sign to appear in your
command, you must double it (`$$').  For shells like the default shell,
that use dollar signs to introduce variables, it's important to keep
clear in your mind whether the variable you want to reference is a
`make' variable (use a single dollar sign) or a shell variable (use two
dollar signs).  For example:

     LIST = one two three
     all:
             for i in $(LIST); do \
                 echo $$i; \
             done

results in the following command being passed to the shell:

     for i in one two three; do \
         echo $i; \
     done

which generates the expected result:

     one
     two
     three


File: make.info,  Node: Echoing,  Next: Execution,  Prev: Command Syntax,  Up: Commands

5.2 Command Echoing
===================

Normally `make' prints each command line before it is executed.  We
call this "echoing" because it gives the appearance that you are typing
the commands yourself.

   When a line starts with `@', the echoing of that line is suppressed.
The `@' is discarded before the command is passed to the shell.
Typically you would use this for a command whose only effect is to print
something, such as an `echo' command to indicate progress through the
makefile:

     @echo About to make distribution files

   When `make' is given the flag `-n' or `--just-print' it only echoes
commands, it won't execute them.  *Note Summary of Options: Options
Summary.  In this case and only this case, even the commands starting
with `@' are printed.  This flag is useful for finding out which
commands `make' thinks are necessary without actually doing them.

   The `-s' or `--silent' flag to `make' prevents all echoing, as if
all commands started with `@'.  A rule in the makefile for the special
target `.SILENT' without prerequisites has the same effect (*note
Special Built-in Target Names: Special Targets.).  `.SILENT' is
essentially obsolete since `@' is more flexible.


File: make.info,  Node: Execution,  Next: Parallel,  Prev: Echoing,  Up: Commands

5.3 Command Execution
=====================

When it is time to execute commands to update a target, they are
executed by invoking a new subshell for each command line.  (In
practice, `make' may take shortcuts that do not affect the results.)

   *Please note:* this implies that setting shell variables and
invoking shell commands such as `cd' that set a context local to each
process will not affect the following command lines.(1)  If you want to
use `cd' to affect the next statement, put both statements in a single
command line.  Then `make' will invoke one shell to run the entire
line, and the shell will execute the statements in sequence.  For
example:

     foo : bar/lose
             cd $(@D) && gobble $(@F) > ../$@

Here we use the shell AND operator (`&&') so that if the `cd' command
fails, the script will fail without trying to invoke the `gobble'
command in the wrong directory, which could cause problems (in this
case it would certainly cause `../foo' to be truncated, at least).

* Menu:

* Choosing the Shell::          How `make' chooses the shell used
                                  to run commands.

   ---------- Footnotes ----------

   (1) On MS-DOS, the value of current working directory is *global*, so
changing it _will_ affect the following command lines on those systems.


File: make.info,  Node: Choosing the Shell,  Prev: Execution,  Up: Execution

5.3.1 Choosing the Shell
------------------------

The program used as the shell is taken from the variable `SHELL'.  If
this variable is not set in your makefile, the program `/bin/sh' is
used as the shell.

   Unlike most variables, the variable `SHELL' is never set from the
environment.  This is because the `SHELL' environment variable is used
to specify your personal choice of shell program for interactive use.
It would be very bad for personal choices like this to affect the
functioning of makefiles.  *Note Variables from the Environment:
Environment.

   Furthermore, when you do set `SHELL' in your makefile that value is
_not_ exported in the environment to commands that `make' invokes.
Instead, the value inherited from the user's environment, if any, is
exported.  You can override this behavior by explicitly exporting
`SHELL' (*note Communicating Variables to a Sub-`make':
Variables/Recursion.), forcing it to be passed in the environment to
commands.

   However, on MS-DOS and MS-Windows the value of `SHELL' in the
environment *is* used, since on those systems most users do not set
this variable, and therefore it is most likely set specifically to be
used by `make'.  On MS-DOS, if the setting of `SHELL' is not suitable
for `make', you can set the variable `MAKESHELL' to the shell that
`make' should use; if set it will be used as the shell instead of the
value of `SHELL'.

Choosing a Shell in DOS and Windows
...................................

Choosing a shell in MS-DOS and MS-Windows is much more complex than on
other systems.

   On MS-DOS, if `SHELL' is not set, the value of the variable
`COMSPEC' (which is always set) is used instead.

   The processing of lines that set the variable `SHELL' in Makefiles
is different on MS-DOS.  The stock shell, `command.com', is
ridiculously limited in its functionality and many users of `make' tend
to install a replacement shell.  Therefore, on MS-DOS, `make' examines
the value of `SHELL', and changes its behavior based on whether it
points to a Unix-style or DOS-style shell.  This allows reasonable
functionality even if `SHELL' points to `command.com'.

   If `SHELL' points to a Unix-style shell, `make' on MS-DOS
additionally checks whether that shell can indeed be found; if not, it
ignores the line that sets `SHELL'.  In MS-DOS, GNU `make' searches for
the shell in the following places:

  1. In the precise place pointed to by the value of `SHELL'.  For
     example, if the makefile specifies `SHELL = /bin/sh', `make' will
     look in the directory `/bin' on the current drive.

  2. In the current directory.

  3. In each of the directories in the `PATH' variable, in order.


   In every directory it examines, `make' will first look for the
specific file (`sh' in the example above).  If this is not found, it
will also look in that directory for that file with one of the known
extensions which identify executable files.  For example `.exe',
`.com', `.bat', `.btm', `.sh', and some others.

   If any of these attempts is successful, the value of `SHELL' will be
set to the full pathname of the shell as found.  However, if none of
these is found, the value of `SHELL' will not be changed, and thus the
line that sets it will be effectively ignored.  This is so `make' will
only support features specific to a Unix-style shell if such a shell is
actually installed on the system where `make' runs.

   Note that this extended search for the shell is limited to the cases
where `SHELL' is set from the Makefile; if it is set in the environment
or command line, you are expected to set it to the full pathname of the
shell, exactly as things are on Unix.

   The effect of the above DOS-specific processing is that a Makefile
that contains `SHELL = /bin/sh' (as many Unix makefiles do), will work
on MS-DOS unaltered if you have e.g. `sh.exe' installed in some
directory along your `PATH'.


File: make.info,  Node: Parallel,  Next: Errors,  Prev: Execution,  Up: Commands

5.4 Parallel Execution
======================

GNU `make' knows how to execute several commands at once.  Normally,
`make' will execute only one command at a time, waiting for it to
finish before executing the next.  However, the `-j' or `--jobs' option
tells `make' to execute many commands simultaneously.

   On MS-DOS, the `-j' option has no effect, since that system doesn't
support multi-processing.

   If the `-j' option is followed by an integer, this is the number of
commands to execute at once; this is called the number of "job slots".
If there is nothing looking like an integer after the `-j' option,
there is no limit on the number of job slots.  The default number of job
slots is one, which means serial execution (one thing at a time).

   One unpleasant consequence of running several commands
simultaneously is that output generated by the commands appears
whenever each command sends it, so messages from different commands may
be interspersed.

   Another problem is that two processes cannot both take input from the
same device; so to make sure that only one command tries to take input
from the terminal at once, `make' will invalidate the standard input
streams of all but one running command.  This means that attempting to
read from standard input will usually be a fatal error (a `Broken pipe'
signal) for most child processes if there are several.  

   It is unpredictable which command will have a valid standard input
stream (which will come from the terminal, or wherever you redirect the
standard input of `make').  The first command run will always get it
first, and the first command started after that one finishes will get
it next, and so on.

   We will change how this aspect of `make' works if we find a better
alternative.  In the mean time, you should not rely on any command using
standard input at all if you are using the parallel execution feature;
but if you are not using this feature, then standard input works
normally in all commands.

   Finally, handling recursive `make' invocations raises issues.  For
more information on this, see *Note Communicating Options to a
Sub-`make': Options/Recursion.

   If a command fails (is killed by a signal or exits with a nonzero
status), and errors are not ignored for that command (*note Errors in
Commands: Errors.), the remaining command lines to remake the same
target will not be run.  If a command fails and the `-k' or
`--keep-going' option was not given (*note Summary of Options: Options
Summary.), `make' aborts execution.  If make terminates for any reason
(including a signal) with child processes running, it waits for them to
finish before actually exiting.

   When the system is heavily loaded, you will probably want to run
fewer jobs than when it is lightly loaded.  You can use the `-l' option
to tell `make' to limit the number of jobs to run at once, based on the
load average.  The `-l' or `--max-load' option is followed by a
floating-point number.  For example,

     -l 2.5

will not let `make' start more than one job if the load average is
above 2.5.  The `-l' option with no following number removes the load
limit, if one was given with a previous `-l' option.

   More precisely, when `make' goes to start up a job, and it already
has at least one job running, it checks the current load average; if it
is not lower than the limit given with `-l', `make' waits until the load
average goes below that limit, or until all the other jobs finish.

   By default, there is no load limit.


File: make.info,  Node: Errors,  Next: Interrupts,  Prev: Parallel,  Up: Commands

5.5 Errors in Commands
======================

After each shell command returns, `make' looks at its exit status.  If
the command completed successfully, the next command line is executed
in a new shell; after the last command line is finished, the rule is
finished.

   If there is an error (the exit status is nonzero), `make' gives up on
the current rule, and perhaps on all rules.

   Sometimes the failure of a certain command does not indicate a
problem.  For example, you may use the `mkdir' command to ensure that a
directory exists.  If the directory already exists, `mkdir' will report
an error, but you probably want `make' to continue regardless.

   To ignore errors in a command line, write a `-' at the beginning of
the line's text (after the initial tab).  The `-' is discarded before
the command is passed to the shell for execution.

   For example,

     clean:
             -rm -f *.o

This causes `rm' to continue even if it is unable to remove a file.

   When you run `make' with the `-i' or `--ignore-errors' flag, errors
are ignored in all commands of all rules.  A rule in the makefile for
the special target `.IGNORE' has the same effect, if there are no
prerequisites.  These ways of ignoring errors are obsolete because `-'
is more flexible.

   When errors are to be ignored, because of either a `-' or the `-i'
flag, `make' treats an error return just like success, except that it
prints out a message that tells you the status code the command exited
with, and says that the error has been ignored.

   When an error happens that `make' has not been told to ignore, it
implies that the current target cannot be correctly remade, and neither
can any other that depends on it either directly or indirectly.  No
further commands will be executed for these targets, since their
preconditions have not been achieved.

   Normally `make' gives up immediately in this circumstance, returning
a nonzero status.  However, if the `-k' or `--keep-going' flag is
specified, `make' continues to consider the other prerequisites of the
pending targets, remaking them if necessary, before it gives up and
returns nonzero status.  For example, after an error in compiling one
object file, `make -k' will continue compiling other object files even
though it already knows that linking them will be impossible.  *Note
Summary of Options: Options Summary.

   The usual behavior assumes that your purpose is to get the specified
targets up to date; once `make' learns that this is impossible, it
might as well report the failure immediately.  The `-k' option says
that the real purpose is to test as many of the changes made in the
program as possible, perhaps to find several independent problems so
that you can correct them all before the next attempt to compile.  This
is why Emacs' `compile' command passes the `-k' flag by default.  

   Usually when a command fails, if it has changed the target file at
all, the file is corrupted and cannot be used--or at least it is not
completely updated.  Yet the file's time stamp says that it is now up to
date, so the next time `make' runs, it will not try to update that
file.  The situation is just the same as when the command is killed by a
signal; *note Interrupts::.  So generally the right thing to do is to
delete the target file if the command fails after beginning to change
the file.  `make' will do this if `.DELETE_ON_ERROR' appears as a
target.  This is almost always what you want `make' to do, but it is
not historical practice; so for compatibility, you must explicitly
request it.


File: make.info,  Node: Interrupts,  Next: Recursion,  Prev: Errors,  Up: Commands

5.6 Interrupting or Killing `make'
==================================

If `make' gets a fatal signal while a command is executing, it may
delete the target file that the command was supposed to update.  This is
done if the target file's last-modification time has changed since
`make' first checked it.

   The purpose of deleting the target is to make sure that it is remade
from scratch when `make' is next run.  Why is this?  Suppose you type
`Ctrl-c' while a compiler is running, and it has begun to write an
object file `foo.o'.  The `Ctrl-c' kills the compiler, resulting in an
incomplete file whose last-modification time is newer than the source
file `foo.c'.  But `make' also receives the `Ctrl-c' signal and deletes
this incomplete file.  If `make' did not do this, the next invocation
of `make' would think that `foo.o' did not require updating--resulting
in a strange error message from the linker when it tries to link an
object file half of which is missing.

   You can prevent the deletion of a target file in this way by making
the special target `.PRECIOUS' depend on it.  Before remaking a target,
`make' checks to see whether it appears on the prerequisites of
`.PRECIOUS', and thereby decides whether the target should be deleted
if a signal happens.  Some reasons why you might do this are that the
target is updated in some atomic fashion, or exists only to record a
modification-time (its contents do not matter), or must exist at all
times to prevent other sorts of trouble.


File: make.info,  Node: Recursion,  Next: Sequences,  Prev: Interrupts,  Up: Commands

5.7 Recursive Use of `make'
===========================

Recursive use of `make' means using `make' as a command in a makefile.
This technique is useful when you want separate makefiles for various
subsystems that compose a larger system.  For example, suppose you have
a subdirectory `subdir' which has its own makefile, and you would like
the containing directory's makefile to run `make' on the subdirectory.
You can do it by writing this:

     subsystem:
             cd subdir && $(MAKE)

or, equivalently, this (*note Summary of Options: Options Summary.):

     subsystem:
             $(MAKE) -C subdir
   
   You can write recursive `make' commands just by copying this example,
but there are many things to know about how they work and why, and about
how the sub-`make' relates to the top-level `make'.  You may also find
it useful to declare targets that invoke recursive `make' commands as
`.PHONY' (for more discussion on when this is useful, see *Note Phony
Targets::).

   For your convenience, when GNU `make' starts (after it has processed
any `-C' options) it sets the variable `CURDIR' to the pathname of the
current working directory.  This value is never touched by `make'
again: in particular note that if you include files from other
directories the value of `CURDIR' does not change.  The value has the
same precedence it would have if it were set in the makefile (by
default, an environment variable `CURDIR' will not override this
value).  Note that setting this variable has no impact on the operation
of `make' (it does not cause `make' to change its working directory,
for example).

* Menu:

* MAKE Variable::               The special effects of using `$(MAKE)'.
* Variables/Recursion::         How to communicate variables to a sub-`make'.
* Options/Recursion::           How to communicate options to a sub-`make'.
* -w Option::                   How the `-w' or `--print-directory' option
                                  helps debug use of recursive `make' commands.


File: make.info,  Node: MAKE Variable,  Next: Variables/Recursion,  Prev: Recursion,  Up: Recursion

5.7.1 How the `MAKE' Variable Works
-----------------------------------

Recursive `make' commands should always use the variable `MAKE', not
the explicit command name `make', as shown here:

     subsystem:
             cd subdir && $(MAKE)

   The value of this variable is the file name with which `make' was
invoked.  If this file name was `/bin/make', then the command executed
is `cd subdir && /bin/make'.  If you use a special version of `make' to
run the top-level makefile, the same special version will be executed
for recursive invocations.  

   As a special feature, using the variable `MAKE' in the commands of a
rule alters the effects of the `-t' (`--touch'), `-n' (`--just-print'),
or `-q' (`--question') option.  Using the `MAKE' variable has the same
effect as using a `+' character at the beginning of the command line.
*Note Instead of Executing the Commands: Instead of Execution.  This
special feature is only enabled if the `MAKE' variable appears directly
in the command script: it does not apply if the `MAKE' variable is
referenced through expansion of another variable.  In the latter case
you must use the `+' token to get these special effects.

   Consider the command `make -t' in the above example.  (The `-t'
option marks targets as up to date without actually running any
commands; see *Note Instead of Execution::.)  Following the usual
definition of `-t', a `make -t' command in the example would create a
file named `subsystem' and do nothing else.  What you really want it to
do is run `cd subdir && make -t'; but that would require executing the
command, and `-t' says not to execute commands.  

   The special feature makes this do what you want: whenever a command
line of a rule contains the variable `MAKE', the flags `-t', `-n' and
`-q' do not apply to that line.  Command lines containing `MAKE' are
executed normally despite the presence of a flag that causes most
commands not to be run.  The usual `MAKEFLAGS' mechanism passes the
flags to the sub-`make' (*note Communicating Options to a Sub-`make':
Options/Recursion.), so your request to touch the files, or print the
commands, is propagated to the subsystem.


File: make.info,  Node: Variables/Recursion,  Next: Options/Recursion,  Prev: MAKE Variable,  Up: Recursion

5.7.2 Communicating Variables to a Sub-`make'
---------------------------------------------

Variable values of the top-level `make' can be passed to the sub-`make'
through the environment by explicit request.  These variables are
defined in the sub-`make' as defaults, but do not override what is
specified in the makefile used by the sub-`make' makefile unless you
use the `-e' switch (*note Summary of Options: Options Summary.).

   To pass down, or "export", a variable, `make' adds the variable and
its value to the environment for running each command.  The sub-`make',
in turn, uses the environment to initialize its table of variable
values.  *Note Variables from the Environment: Environment.

   Except by explicit request, `make' exports a variable only if it is
either defined in the environment initially or set on the command line,
and if its name consists only of letters, numbers, and underscores.
Some shells cannot cope with environment variable names consisting of
characters other than letters, numbers, and underscores.

   The value of the `make' variable `SHELL' is not exported.  Instead,
the value of the `SHELL' variable from the invoking environment is
passed to the sub-`make'.  You can force `make' to export its value for
`SHELL' by using the `export' directive, described below.  *Note
Choosing the Shell::.

   The special variable `MAKEFLAGS' is always exported (unless you
unexport it).  `MAKEFILES' is exported if you set it to anything.

   `make' automatically passes down variable values that were defined
on the command line, by putting them in the `MAKEFLAGS' variable.
*Note Options/Recursion::.

   Variables are _not_ normally passed down if they were created by
default by `make' (*note Variables Used by Implicit Rules: Implicit
Variables.).  The sub-`make' will define these for itself.

   If you want to export specific variables to a sub-`make', use the
`export' directive, like this:

     export VARIABLE ...

If you want to _prevent_ a variable from being exported, use the
`unexport' directive, like this:

     unexport VARIABLE ...

In both of these forms, the arguments to `export' and `unexport' are
expanded, and so could be variables or functions which expand to a
(list of) variable names to be (un)exported.

   As a convenience, you can define a variable and export it at the same
time by doing:

     export VARIABLE = value

has the same result as:

     VARIABLE = value
     export VARIABLE

and

     export VARIABLE := value

has the same result as:

     VARIABLE := value
     export VARIABLE

   Likewise,

     export VARIABLE += value

is just like:

     VARIABLE += value
     export VARIABLE

*Note Appending More Text to Variables: Appending.

   You may notice that the `export' and `unexport' directives work in
`make' in the same way they work in the shell, `sh'.

   If you want all variables to be exported by default, you can use
`export' by itself:

     export

This tells `make' that variables which are not explicitly mentioned in
an `export' or `unexport' directive should be exported.  Any variable
given in an `unexport' directive will still _not_ be exported.  If you
use `export' by itself to export variables by default, variables whose
names contain characters other than alphanumerics and underscores will
not be exported unless specifically mentioned in an `export' directive.

   The behavior elicited by an `export' directive by itself was the
default in older versions of GNU `make'.  If your makefiles depend on
this behavior and you want to be compatible with old versions of
`make', you can write a rule for the special target
`.EXPORT_ALL_VARIABLES' instead of using the `export' directive.  This
will be ignored by old `make's, while the `export' directive will cause
a syntax error.  

   Likewise, you can use `unexport' by itself to tell `make' _not_ to
export variables by default.  Since this is the default behavior, you
would only need to do this if `export' had been used by itself earlier
(in an included makefile, perhaps).  You *cannot* use `export' and
`unexport' by themselves to have variables exported for some commands
and not for others.  The last `export' or `unexport' directive that
appears by itself determines the behavior for the entire run of `make'.

   As a special feature, the variable `MAKELEVEL' is changed when it is
passed down from level to level.  This variable's value is a string
which is the depth of the level as a decimal number.  The value is `0'
for the top-level `make'; `1' for a sub-`make', `2' for a
sub-sub-`make', and so on.  The incrementation happens when `make' sets
up the environment for a command.

   The main use of `MAKELEVEL' is to test it in a conditional directive
(*note Conditional Parts of Makefiles: Conditionals.); this way you can
write a makefile that behaves one way if run recursively and another
way if run directly by you.

   You can use the variable `MAKEFILES' to cause all sub-`make'
commands to use additional makefiles.  The value of `MAKEFILES' is a
whitespace-separated list of file names.  This variable, if defined in
the outer-level makefile, is passed down through the environment; then
it serves as a list of extra makefiles for the sub-`make' to read
before the usual or specified ones.  *Note The Variable `MAKEFILES':
MAKEFILES Variable.


File: make.info,  Node: Options/Recursion,  Next: -w Option,  Prev: Variables/Recursion,  Up: Recursion

5.7.3 Communicating Options to a Sub-`make'
-------------------------------------------

Flags such as `-s' and `-k' are passed automatically to the sub-`make'
through the variable `MAKEFLAGS'.  This variable is set up
automatically by `make' to contain the flag letters that `make'
received.  Thus, if you do `make -ks' then `MAKEFLAGS' gets the value
`ks'.

   As a consequence, every sub-`make' gets a value for `MAKEFLAGS' in
its environment.  In response, it takes the flags from that value and
processes them as if they had been given as arguments.  *Note Summary
of Options: Options Summary.

   Likewise variables defined on the command line are passed to the
sub-`make' through `MAKEFLAGS'.  Words in the value of `MAKEFLAGS' that
contain `=', `make' treats as variable definitions just as if they
appeared on the command line.  *Note Overriding Variables: Overriding.

   The options `-C', `-f', `-o', and `-W' are not put into `MAKEFLAGS';
these options are not passed down.

   The `-j' option is a special case (*note Parallel Execution:
Parallel.).  If you set it to some numeric value `N' and your operating
system supports it (most any UNIX system will; others typically won't),
the parent `make' and all the sub-`make's will communicate to ensure
that there are only `N' jobs running at the same time between them all.
Note that any job that is marked recursive (*note Instead of Executing
the Commands: Instead of Execution.)  doesn't count against the total
jobs (otherwise we could get `N' sub-`make's running and have no slots
left over for any real work!)

   If your operating system doesn't support the above communication,
then `-j 1' is always put into `MAKEFLAGS' instead of the value you
specified.  This is because if the `-j' option were passed down to
sub-`make's, you would get many more jobs running in parallel than you
asked for.  If you give `-j' with no numeric argument, meaning to run
as many jobs as possible in parallel, this is passed down, since
multiple infinities are no more than one.

   If you do not want to pass the other flags down, you must change the
value of `MAKEFLAGS', like this:

     subsystem:
             cd subdir && $(MAKE) MAKEFLAGS=

   The command line variable definitions really appear in the variable
`MAKEOVERRIDES', and `MAKEFLAGS' contains a reference to this variable.
If you do want to pass flags down normally, but don't want to pass
down the command line variable definitions, you can reset
`MAKEOVERRIDES' to empty, like this:

     MAKEOVERRIDES =

This is not usually useful to do.  However, some systems have a small
fixed limit on the size of the environment, and putting so much
information into the value of `MAKEFLAGS' can exceed it.  If you see
the error message `Arg list too long', this may be the problem.  (For
strict compliance with POSIX.2, changing `MAKEOVERRIDES' does not
affect `MAKEFLAGS' if the special target `.POSIX' appears in the
makefile.  You probably do not care about this.)

   A similar variable `MFLAGS' exists also, for historical
compatibility.  It has the same value as `MAKEFLAGS' except that it
does not contain the command line variable definitions, and it always
begins with a hyphen unless it is empty (`MAKEFLAGS' begins with a
hyphen only when it begins with an option that has no single-letter
version, such as `--warn-undefined-variables').  `MFLAGS' was
traditionally used explicitly in the recursive `make' command, like
this:

     subsystem:
             cd subdir && $(MAKE) $(MFLAGS)

but now `MAKEFLAGS' makes this usage redundant.  If you want your
makefiles to be compatible with old `make' programs, use this
technique; it will work fine with more modern `make' versions too.

   The `MAKEFLAGS' variable can also be useful if you want to have
certain options, such as `-k' (*note Summary of Options: Options
Summary.), set each time you run `make'.  You simply put a value for
`MAKEFLAGS' in your environment.  You can also set `MAKEFLAGS' in a
makefile, to specify additional flags that should also be in effect for
that makefile.  (Note that you cannot use `MFLAGS' this way.  That
variable is set only for compatibility; `make' does not interpret a
value you set for it in any way.)

   When `make' interprets the value of `MAKEFLAGS' (either from the
environment or from a makefile), it first prepends a hyphen if the value
does not already begin with one.  Then it chops the value into words
separated by blanks, and parses these words as if they were options
given on the command line (except that `-C', `-f', `-h', `-o', `-W',
and their long-named versions are ignored; and there is no error for an
invalid option).

   If you do put `MAKEFLAGS' in your environment, you should be sure not
to include any options that will drastically affect the actions of
`make' and undermine the purpose of makefiles and of `make' itself.
For instance, the `-t', `-n', and `-q' options, if put in one of these
variables, could have disastrous consequences and would certainly have
at least surprising and probably annoying effects.


File: make.info,  Node: -w Option,  Prev: Options/Recursion,  Up: Recursion

5.7.4 The `--print-directory' Option
------------------------------------

If you use several levels of recursive `make' invocations, the `-w' or
`--print-directory' option can make the output a lot easier to
understand by showing each directory as `make' starts processing it and
as `make' finishes processing it.  For example, if `make -w' is run in
the directory `/u/gnu/make', `make' will print a line of the form:

     make: Entering directory `/u/gnu/make'.

before doing anything else, and a line of the form:

     make: Leaving directory `/u/gnu/make'.

when processing is completed.

   Normally, you do not need to specify this option because `make' does
it for you: `-w' is turned on automatically when you use the `-C'
option, and in sub-`make's.  `make' will not automatically turn on `-w'
if you also use `-s', which says to be silent, or if you use
`--no-print-directory' to explicitly disable it.


File: make.info,  Node: Sequences,  Next: Empty Commands,  Prev: Recursion,  Up: Commands

5.8 Defining Canned Command Sequences
=====================================

When the same sequence of commands is useful in making various targets,
you can define it as a canned sequence with the `define' directive, and
refer to the canned sequence from the rules for those targets.  The
canned sequence is actually a variable, so the name must not conflict
with other variable names.

   Here is an example of defining a canned sequence of commands:

     define run-yacc
     yacc $(firstword $^)
     mv y.tab.c $@
     endef
   
Here `run-yacc' is the name of the variable being defined; `endef'
marks the end of the definition; the lines in between are the commands.
The `define' directive does not expand variable references and
function calls in the canned sequence; the `$' characters, parentheses,
variable names, and so on, all become part of the value of the variable
you are defining.  *Note Defining Variables Verbatim: Defining, for a
complete explanation of `define'.

   The first command in this example runs Yacc on the first
prerequisite of whichever rule uses the canned sequence.  The output
file from Yacc is always named `y.tab.c'.  The second command moves the
output to the rule's target file name.

   To use the canned sequence, substitute the variable into the
commands of a rule.  You can substitute it like any other variable
(*note Basics of Variable References: Reference.).  Because variables
defined by `define' are recursively expanded variables, all the
variable references you wrote inside the `define' are expanded now.
For example:

     foo.c : foo.y
             $(run-yacc)

`foo.y' will be substituted for the variable `$^' when it occurs in
`run-yacc''s value, and `foo.c' for `$@'.

   This is a realistic example, but this particular one is not needed in
practice because `make' has an implicit rule to figure out these
commands based on the file names involved (*note Using Implicit Rules:
Implicit Rules.).

   In command execution, each line of a canned sequence is treated just
as if the line appeared on its own in the rule, preceded by a tab.  In
particular, `make' invokes a separate subshell for each line.  You can
use the special prefix characters that affect command lines (`@', `-',
and `+') on each line of a canned sequence.  *Note Writing the Commands
in Rules: Commands.  For example, using this canned sequence:

     define frobnicate
     @echo "frobnicating target $@"
     frob-step-1 $< -o $@-step-1
     frob-step-2 $@-step-1 -o $@
     endef

`make' will not echo the first line, the `echo' command.  But it _will_
echo the following two command lines.

   On the other hand, prefix characters on the command line that refers
to a canned sequence apply to every line in the sequence.  So the rule:

     frob.out: frob.in
             @$(frobnicate)

does not echo _any_ commands.  (*Note Command Echoing: Echoing, for a
full explanation of `@'.)


File: make.info,  Node: Empty Commands,  Prev: Sequences,  Up: Commands

5.9 Using Empty Commands
========================

It is sometimes useful to define commands which do nothing.  This is
done simply by giving a command that consists of nothing but
whitespace.  For example:

     target: ;

defines an empty command string for `target'.  You could also use a
line beginning with a tab character to define an empty command string,
but this would be confusing because such a line looks empty.

   You may be wondering why you would want to define a command string
that does nothing.  The only reason this is useful is to prevent a
target from getting implicit commands (from implicit rules or the
`.DEFAULT' special target; *note Implicit Rules:: and *note Defining
Last-Resort Default Rules: Last Resort.).

   You may be inclined to define empty command strings for targets that
are not actual files, but only exist so that their prerequisites can be
remade.  However, this is not the best way to do that, because the
prerequisites may not be remade properly if the target file actually
does exist.  *Note Phony Targets: Phony Targets, for a better way to do
this.


File: make.info,  Node: Using Variables,  Next: Conditionals,  Prev: Commands,  Up: Top

6 How to Use Variables
**********************

A "variable" is a name defined in a makefile to represent a string of
text, called the variable's "value".  These values are substituted by
explicit request into targets, prerequisites, commands, and other parts
of the makefile.  (In some other versions of `make', variables are
called "macros".)  

   Variables and functions in all parts of a makefile are expanded when
read, except for the shell commands in rules, the right-hand sides of
variable definitions using `=', and the bodies of variable definitions
using the `define' directive.

   Variables can represent lists of file names, options to pass to
compilers, programs to run, directories to look in for source files,
directories to write output in, or anything else you can imagine.

   A variable name may be any sequence of characters not containing `:',
`#', `=', or leading or trailing whitespace.  However, variable names
containing characters other than letters, numbers, and underscores
should be avoided, as they may be given special meanings in the future,
and with some shells they cannot be passed through the environment to a
sub-`make' (*note Communicating Variables to a Sub-`make':
Variables/Recursion.).

   Variable names are case-sensitive.  The names `foo', `FOO', and
`Foo' all refer to different variables.

   It is traditional to use upper case letters in variable names, but we
recommend using lower case letters for variable names that serve
internal purposes in the makefile, and reserving upper case for
parameters that control implicit rules or for parameters that the user
should override with command options (*note Overriding Variables:
Overriding.).

   A few variables have names that are a single punctuation character or
just a few characters.  These are the "automatic variables", and they
have particular specialized uses.  *Note Automatic Variables::.

* Menu:

* Reference::                   How to use the value of a variable.
* Flavors::                     Variables come in two flavors.
* Advanced::                    Advanced features for referencing a variable.
* Values::                      All the ways variables get their values.
* Setting::                     How to set a variable in the makefile.
* Appending::                   How to append more text to the old value
                                  of a variable.
* Override Directive::          How to set a variable in the makefile even if
                                  the user has set it with a command argument.
* Defining::                    An alternate way to set a variable
                                  to a verbatim string.
* Environment::                 Variable values can come from the environment.
* Target-specific::             Variable values can be defined on a per-target
                                  basis.
* Pattern-specific::            Target-specific variable values can be applied
                                  to a group of targets that match a pattern.


File: make.info,  Node: Reference,  Next: Flavors,  Prev: Using Variables,  Up: Using Variables

6.1 Basics of Variable References
=================================

To substitute a variable's value, write a dollar sign followed by the
name of the variable in parentheses or braces: either `$(foo)' or
`${foo}' is a valid reference to the variable `foo'.  This special
significance of `$' is why you must write `$$' to have the effect of a
single dollar sign in a file name or command.

   Variable references can be used in any context: targets,
prerequisites, commands, most directives, and new variable values.
Here is an example of a common case, where a variable holds the names
of all the object files in a program:

     objects = program.o foo.o utils.o
     program : $(objects)
             cc -o program $(objects)

     $(objects) : defs.h

   Variable references work by strict textual substitution.  Thus, the
rule

     foo = c
     prog.o : prog.$(foo)
             $(foo)$(foo) -$(foo) prog.$(foo)

could be used to compile a C program `prog.c'.  Since spaces before the
variable value are ignored in variable assignments, the value of `foo'
is precisely `c'.  (Don't actually write your makefiles this way!)

   A dollar sign followed by a character other than a dollar sign,
open-parenthesis or open-brace treats that single character as the
variable name.  Thus, you could reference the variable `x' with `$x'.
However, this practice is strongly discouraged, except in the case of
the automatic variables (*note Automatic Variables::).


File: make.info,  Node: Flavors,  Next: Advanced,  Prev: Reference,  Up: Using Variables

6.2 The Two Flavors of Variables
================================

There are two ways that a variable in GNU `make' can have a value; we
call them the two "flavors" of variables.  The two flavors are
distinguished in how they are defined and in what they do when expanded.

   The first flavor of variable is a "recursively expanded" variable.
Variables of this sort are defined by lines using `=' (*note Setting
Variables: Setting.) or by the `define' directive (*note Defining
Variables Verbatim: Defining.).  The value you specify is installed
verbatim; if it contains references to other variables, these
references are expanded whenever this variable is substituted (in the
course of expanding some other string).  When this happens, it is
called "recursive expansion".

   For example,

     foo = $(bar)
     bar = $(ugh)
     ugh = Huh?

     all:;echo $(foo)

will echo `Huh?': `$(foo)' expands to `$(bar)' which expands to
`$(ugh)' which finally expands to `Huh?'.

   This flavor of variable is the only sort supported by other versions
of `make'.  It has its advantages and its disadvantages.  An advantage
(most would say) is that:

     CFLAGS = $(include_dirs) -O
     include_dirs = -Ifoo -Ibar

will do what was intended: when `CFLAGS' is expanded in a command, it
will expand to `-Ifoo -Ibar -O'.  A major disadvantage is that you
cannot append something on the end of a variable, as in

     CFLAGS = $(CFLAGS) -O

because it will cause an infinite loop in the variable expansion.
(Actually `make' detects the infinite loop and reports an error.)  

   Another disadvantage is that any functions (*note Functions for
Transforming Text: Functions.)  referenced in the definition will be
executed every time the variable is expanded.  This makes `make' run
slower; worse, it causes the `wildcard' and `shell' functions to give
unpredictable results because you cannot easily control when they are
called, or even how many times.

   To avoid all the problems and inconveniences of recursively expanded
variables, there is another flavor: simply expanded variables.

   "Simply expanded variables" are defined by lines using `:=' (*note
Setting Variables: Setting.).  The value of a simply expanded variable
is scanned once and for all, expanding any references to other
variables and functions, when the variable is defined.  The actual
value of the simply expanded variable is the result of expanding the
text that you write.  It does not contain any references to other
variables; it contains their values _as of the time this variable was
defined_.  Therefore,

     x := foo
     y := $(x) bar
     x := later

is equivalent to

     y := foo bar
     x := later

   When a simply expanded variable is referenced, its value is
substituted verbatim.

   Here is a somewhat more complicated example, illustrating the use of
`:=' in conjunction with the `shell' function.  (*Note The `shell'
Function: Shell Function.)  This example also shows use of the variable
`MAKELEVEL', which is changed when it is passed down from level to
level.  (*Note Communicating Variables to a Sub-`make':
Variables/Recursion, for information about `MAKELEVEL'.)

     ifeq (0,${MAKELEVEL})
     whoami    := $(shell whoami)
     host-type := $(shell arch)
     MAKE := ${MAKE} host-type=${host-type} whoami=${whoami}
     endif

An advantage of this use of `:=' is that a typical `descend into a
directory' command then looks like this:

     ${subdirs}:
             ${MAKE} -C $@ all

   Simply expanded variables generally make complicated makefile
programming more predictable because they work like variables in most
programming languages.  They allow you to redefine a variable using its
own value (or its value processed in some way by one of the expansion
functions) and to use the expansion functions much more efficiently
(*note Functions for Transforming Text: Functions.).

   You can also use them to introduce controlled leading whitespace into
variable values.  Leading whitespace characters are discarded from your
input before substitution of variable references and function calls;
this means you can include leading spaces in a variable value by
protecting them with variable references, like this:

     nullstring :=
     space := $(nullstring) # end of the line

Here the value of the variable `space' is precisely one space.  The
comment `# end of the line' is included here just for clarity.  Since
trailing space characters are _not_ stripped from variable values, just
a space at the end of the line would have the same effect (but be
rather hard to read).  If you put whitespace at the end of a variable
value, it is a good idea to put a comment like that at the end of the
line to make your intent clear.  Conversely, if you do _not_ want any
whitespace characters at the end of your variable value, you must
remember not to put a random comment on the end of the line after some
whitespace, such as this:

     dir := /foo/bar    # directory to put the frobs in

Here the value of the variable `dir' is `/foo/bar    ' (with four
trailing spaces), which was probably not the intention.  (Imagine
something like `$(dir)/file' with this definition!)

   There is another assignment operator for variables, `?='.  This is
called a conditional variable assignment operator, because it only has
an effect if the variable is not yet defined.  This statement:

     FOO ?= bar

is exactly equivalent to this (*note The `origin' Function: Origin
Function.):

     ifeq ($(origin FOO), undefined)
       FOO = bar
     endif

   Note that a variable set to an empty value is still defined, so `?='
will not set that variable.


File: make.info,  Node: Advanced,  Next: Values,  Prev: Flavors,  Up: Using Variables

6.3 Advanced Features for Reference to Variables
================================================

This section describes some advanced features you can use to reference
variables in more flexible ways.

* Menu:

* Substitution Refs::           Referencing a variable with
                                  substitutions on the value.
* Computed Names::              Computing the name of the variable to refer to.


File: make.info,  Node: Substitution Refs,  Next: Computed Names,  Prev: Advanced,  Up: Advanced

6.3.1 Substitution References
-----------------------------

A "substitution reference" substitutes the value of a variable with
alterations that you specify.  It has the form `$(VAR:A=B)' (or
`${VAR:A=B}') and its meaning is to take the value of the variable VAR,
replace every A at the end of a word with B in that value, and
substitute the resulting string.

   When we say "at the end of a word", we mean that A must appear
either followed by whitespace or at the end of the value in order to be
replaced; other occurrences of A in the value are unaltered.  For
example:

     foo := a.o b.o c.o
     bar := $(foo:.o=.c)

sets `bar' to `a.c b.c c.c'.  *Note Setting Variables: Setting.

   A substitution reference is actually an abbreviation for use of the
`patsubst' expansion function (*note Functions for String Substitution
and Analysis: Text Functions.).  We provide substitution references as
well as `patsubst' for compatibility with other implementations of
`make'.

   Another type of substitution reference lets you use the full power of
the `patsubst' function.  It has the same form `$(VAR:A=B)' described
above, except that now A must contain a single `%' character.  This
case is equivalent to `$(patsubst A,B,$(VAR))'.  *Note Functions for
String Substitution and Analysis: Text Functions, for a description of
the `patsubst' function.

For example:

     foo := a.o b.o c.o
     bar := $(foo:%.o=%.c)

sets `bar' to `a.c b.c c.c'.


File: make.info,  Node: Computed Names,  Prev: Substitution Refs,  Up: Advanced

6.3.2 Computed Variable Names
-----------------------------

Computed variable names are a complicated concept needed only for
sophisticated makefile programming.  For most purposes you need not
consider them, except to know that making a variable with a dollar sign
in its name might have strange results.  However, if you are the type
that wants to understand everything, or you are actually interested in
what they do, read on.

   Variables may be referenced inside the name of a variable.  This is
called a "computed variable name" or a "nested variable reference".
For example,

     x = y
     y = z
     a := $($(x))

defines `a' as `z': the `$(x)' inside `$($(x))' expands to `y', so
`$($(x))' expands to `$(y)' which in turn expands to `z'.  Here the
name of the variable to reference is not stated explicitly; it is
computed by expansion of `$(x)'.  The reference `$(x)' here is nested
within the outer variable reference.

   The previous example shows two levels of nesting, but any number of
levels is possible.  For example, here are three levels:

     x = y
     y = z
     z = u
     a := $($($(x)))

Here the innermost `$(x)' expands to `y', so `$($(x))' expands to
`$(y)' which in turn expands to `z'; now we have `$(z)', which becomes
`u'.

   References to recursively-expanded variables within a variable name
are reexpanded in the usual fashion.  For example:

     x = $(y)
     y = z
     z = Hello
     a := $($(x))

defines `a' as `Hello': `$($(x))' becomes `$($(y))' which becomes
`$(z)' which becomes `Hello'.

   Nested variable references can also contain modified references and
function invocations (*note Functions for Transforming Text:
Functions.), just like any other reference.  For example, using the
`subst' function (*note Functions for String Substitution and Analysis:
Text Functions.):

     x = variable1
     variable2 := Hello
     y = $(subst 1,2,$(x))
     z = y
     a := $($($(z)))

eventually defines `a' as `Hello'.  It is doubtful that anyone would
ever want to write a nested reference as convoluted as this one, but it
works: `$($($(z)))' expands to `$($(y))' which becomes `$($(subst
1,2,$(x)))'.  This gets the value `variable1' from `x' and changes it
by substitution to `variable2', so that the entire string becomes
`$(variable2)', a simple variable reference whose value is `Hello'.

   A computed variable name need not consist entirely of a single
variable reference.  It can contain several variable references, as
well as some invariant text.  For example,

     a_dirs := dira dirb
     1_dirs := dir1 dir2

     a_files := filea fileb
     1_files := file1 file2

     ifeq "$(use_a)" "yes"
     a1 := a
     else
     a1 := 1
     endif

     ifeq "$(use_dirs)" "yes"
     df := dirs
     else
     df := files
     endif

     dirs := $($(a1)_$(df))

will give `dirs' the same value as `a_dirs', `1_dirs', `a_files' or
`1_files' depending on the settings of `use_a' and `use_dirs'.

   Computed variable names can also be used in substitution references:

     a_objects := a.o b.o c.o
     1_objects := 1.o 2.o 3.o

     sources := $($(a1)_objects:.o=.c)

defines `sources' as either `a.c b.c c.c' or `1.c 2.c 3.c', depending
on the value of `a1'.

   The only restriction on this sort of use of nested variable
references is that they cannot specify part of the name of a function
to be called.  This is because the test for a recognized function name
is done before the expansion of nested references.  For example,

     ifdef do_sort
     func := sort
     else
     func := strip
     endif

     bar := a d b g q c

     foo := $($(func) $(bar))

attempts to give `foo' the value of the variable `sort a d b g q c' or
`strip a d b g q c', rather than giving `a d b g q c' as the argument
to either the `sort' or the `strip' function.  This restriction could
be removed in the future if that change is shown to be a good idea.

   You can also use computed variable names in the left-hand side of a
variable assignment, or in a `define' directive, as in:

     dir = foo
     $(dir)_sources := $(wildcard $(dir)/*.c)
     define $(dir)_print
     lpr $($(dir)_sources)
     endef

This example defines the variables `dir', `foo_sources', and
`foo_print'.

   Note that "nested variable references" are quite different from
"recursively expanded variables" (*note The Two Flavors of Variables:
Flavors.), though both are used together in complex ways when doing
makefile programming.


File: make.info,  Node: Values,  Next: Setting,  Prev: Advanced,  Up: Using Variables

6.4 How Variables Get Their Values
==================================

Variables can get values in several different ways:

   * You can specify an overriding value when you run `make'.  *Note
     Overriding Variables: Overriding.

   * You can specify a value in the makefile, either with an assignment
     (*note Setting Variables: Setting.) or with a verbatim definition
     (*note Defining Variables Verbatim: Defining.).

   * Variables in the environment become `make' variables.  *Note
     Variables from the Environment: Environment.

   * Several "automatic" variables are given new values for each rule.
     Each of these has a single conventional use.  *Note Automatic
     Variables::.

   * Several variables have constant initial values.  *Note Variables
     Used by Implicit Rules: Implicit Variables.


File: make.info,  Node: Setting,  Next: Appending,  Prev: Values,  Up: Using Variables

6.5 Setting Variables
=====================

To set a variable from the makefile, write a line starting with the
variable name followed by `=' or `:='.  Whatever follows the `=' or
`:=' on the line becomes the value.  For example,

     objects = main.o foo.o bar.o utils.o

defines a variable named `objects'.  Whitespace around the variable
name and immediately after the `=' is ignored.

   Variables defined with `=' are "recursively expanded" variables.
Variables defined with `:=' are "simply expanded" variables; these
definitions can contain variable references which will be expanded
before the definition is made.  *Note The Two Flavors of Variables:
Flavors.

   The variable name may contain function and variable references, which
are expanded when the line is read to find the actual variable name to
use.

   There is no limit on the length of the value of a variable except the
amount of swapping space on the computer.  When a variable definition is
long, it is a good idea to break it into several lines by inserting
backslash-newline at convenient places in the definition.  This will not
affect the functioning of `make', but it will make the makefile easier
to read.

   Most variable names are considered to have the empty string as a
value if you have never set them.  Several variables have built-in
initial values that are not empty, but you can set them in the usual
ways (*note Variables Used by Implicit Rules: Implicit Variables.).
Several special variables are set automatically to a new value for each
rule; these are called the "automatic" variables (*note Automatic
Variables::).

   If you'd like a variable to be set to a value only if it's not
already set, then you can use the shorthand operator `?=' instead of
`='.  These two settings of the variable `FOO' are identical (*note The
`origin' Function: Origin Function.):

     FOO ?= bar

and

     ifeq ($(origin FOO), undefined)
     FOO = bar
     endif


File: make.info,  Node: Appending,  Next: Override Directive,  Prev: Setting,  Up: Using Variables

6.6 Appending More Text to Variables
====================================

Often it is useful to add more text to the value of a variable already
defined.  You do this with a line containing `+=', like this:

     objects += another.o

This takes the value of the variable `objects', and adds the text
`another.o' to it (preceded by a single space).  Thus:

     objects = main.o foo.o bar.o utils.o
     objects += another.o

sets `objects' to `main.o foo.o bar.o utils.o another.o'.

   Using `+=' is similar to:

     objects = main.o foo.o bar.o utils.o
     objects := $(objects) another.o

but differs in ways that become important when you use more complex
values.

   When the variable in question has not been defined before, `+=' acts
just like normal `=': it defines a recursively-expanded variable.
However, when there _is_ a previous definition, exactly what `+=' does
depends on what flavor of variable you defined originally.  *Note The
Two Flavors of Variables: Flavors, for an explanation of the two
flavors of variables.

   When you add to a variable's value with `+=', `make' acts
essentially as if you had included the extra text in the initial
definition of the variable.  If you defined it first with `:=', making
it a simply-expanded variable, `+=' adds to that simply-expanded
definition, and expands the new text before appending it to the old
value just as `:=' does (see *Note Setting Variables: Setting, for a
full explanation of `:=').  In fact,

     variable := value
     variable += more

is exactly equivalent to:


     variable := value
     variable := $(variable) more

   On the other hand, when you use `+=' with a variable that you defined
first to be recursively-expanded using plain `=', `make' does something
a bit different.  Recall that when you define a recursively-expanded
variable, `make' does not expand the value you set for variable and
function references immediately.  Instead it stores the text verbatim,
and saves these variable and function references to be expanded later,
when you refer to the new variable (*note The Two Flavors of Variables:
Flavors.).  When you use `+=' on a recursively-expanded variable, it is
this unexpanded text to which `make' appends the new text you specify.

     variable = value
     variable += more

is roughly equivalent to:

     temp = value
     variable = $(temp) more

except that of course it never defines a variable called `temp'.  The
importance of this comes when the variable's old value contains
variable references.  Take this common example:

     CFLAGS = $(includes) -O
     ...
     CFLAGS += -pg # enable profiling

The first line defines the `CFLAGS' variable with a reference to another
variable, `includes'.  (`CFLAGS' is used by the rules for C
compilation; *note Catalogue of Implicit Rules: Catalogue of Rules.)
Using `=' for the definition makes `CFLAGS' a recursively-expanded
variable, meaning `$(includes) -O' is _not_ expanded when `make'
processes the definition of `CFLAGS'.  Thus, `includes' need not be
defined yet for its value to take effect.  It only has to be defined
before any reference to `CFLAGS'.  If we tried to append to the value
of `CFLAGS' without using `+=', we might do it like this:

     CFLAGS := $(CFLAGS) -pg # enable profiling

This is pretty close, but not quite what we want.  Using `:=' redefines
`CFLAGS' as a simply-expanded variable; this means `make' expands the
text `$(CFLAGS) -pg' before setting the variable.  If `includes' is not
yet defined, we get ` -O -pg', and a later definition of `includes'
will have no effect.  Conversely, by using `+=' we set `CFLAGS' to the
_unexpanded_ value `$(includes) -O -pg'.  Thus we preserve the
reference to `includes', so if that variable gets defined at any later
point, a reference like `$(CFLAGS)' still uses its value.


File: make.info,  Node: Override Directive,  Next: Defining,  Prev: Appending,  Up: Using Variables

6.7 The `override' Directive
============================

If a variable has been set with a command argument (*note Overriding
Variables: Overriding.), then ordinary assignments in the makefile are
ignored.  If you want to set the variable in the makefile even though
it was set with a command argument, you can use an `override'
directive, which is a line that looks like this:

     override VARIABLE = VALUE

or

     override VARIABLE := VALUE

   To append more text to a variable defined on the command line, use:

     override VARIABLE += MORE TEXT

*Note Appending More Text to Variables: Appending.

   The `override' directive was not invented for escalation in the war
between makefiles and command arguments.  It was invented so you can
alter and add to values that the user specifies with command arguments.

   For example, suppose you always want the `-g' switch when you run the
C compiler, but you would like to allow the user to specify the other
switches with a command argument just as usual.  You could use this
`override' directive:

     override CFLAGS += -g

   You can also use `override' directives with `define' directives.
This is done as you might expect:

     override define foo
     bar
     endef

*Note Defining Variables Verbatim: Defining.


File: make.info,  Node: Defining,  Next: Environment,  Prev: Override Directive,  Up: Using Variables

6.8 Defining Variables Verbatim
===============================

Another way to set the value of a variable is to use the `define'
directive.  This directive has an unusual syntax which allows newline
characters to be included in the value, which is convenient for defining
both canned sequences of commands (*note Defining Canned Command
Sequences: Sequences.), and also sections of makefile syntax to use
with `eval' (*note Eval Function::).

   The `define' directive is followed on the same line by the name of
the variable and nothing more.  The value to give the variable appears
on the following lines.  The end of the value is marked by a line
containing just the word `endef'.  Aside from this difference in
syntax, `define' works just like `=': it creates a recursively-expanded
variable (*note The Two Flavors of Variables: Flavors.).  The variable
name may contain function and variable references, which are expanded
when the directive is read to find the actual variable name to use.

   You may nest `define' directives: `make' will keep track of nested
directives and report an error if they are not all properly closed with
`endef'.  Note that lines beginning with tab characters are considered
part of a command script, so any `define' or `endef' strings appearing
on such a line will not be considered `make' operators.

     define two-lines
     echo foo
     echo $(bar)
     endef

   The value in an ordinary assignment cannot contain a newline; but the
newlines that separate the lines of the value in a `define' become part
of the variable's value (except for the final newline which precedes
the `endef' and is not considered part of the value).

   When used in a command script, the previous example is functionally
equivalent to this:

     two-lines = echo foo; echo $(bar)

since two commands separated by semicolon behave much like two separate
shell commands.  However, note that using two separate lines means
`make' will invoke the shell twice, running an independent subshell for
each line.  *Note Command Execution: Execution.

   If you want variable definitions made with `define' to take
precedence over command-line variable definitions, you can use the
`override' directive together with `define':

     override define two-lines
     foo
     $(bar)
     endef

*Note The `override' Directive: Override Directive.


File: make.info,  Node: Environment,  Next: Target-specific,  Prev: Defining,  Up: Using Variables

6.9 Variables from the Environment
==================================

Variables in `make' can come from the environment in which `make' is
run.  Every environment variable that `make' sees when it starts up is
transformed into a `make' variable with the same name and value.
However, an explicit assignment in the makefile, or with a command
argument, overrides the environment.  (If the `-e' flag is specified,
then values from the environment override assignments in the makefile.
*Note Summary of Options: Options Summary.  But this is not recommended
practice.)

   Thus, by setting the variable `CFLAGS' in your environment, you can
cause all C compilations in most makefiles to use the compiler switches
you prefer.  This is safe for variables with standard or conventional
meanings because you know that no makefile will use them for other
things.  (Note this is not totally reliable; some makefiles set
`CFLAGS' explicitly and therefore are not affected by the value in the
environment.)

   When `make' runs a command script, variables defined in the makefile
are placed into the environment of that command.  This allows you to
pass values to sub-`make' invocations (*note Recursive Use of `make':
Recursion.).  By default, only variables that came from the environment
or the command line are passed to recursive invocations.  You can use
the `export' directive to pass other variables.  *Note Communicating
Variables to a Sub-`make': Variables/Recursion, for full details.

   Other use of variables from the environment is not recommended.  It
is not wise for makefiles to depend for their functioning on
environment variables set up outside their control, since this would
cause different users to get different results from the same makefile.
This is against the whole purpose of most makefiles.

   Such problems would be especially likely with the variable `SHELL',
which is normally present in the environment to specify the user's
choice of interactive shell.  It would be very undesirable for this
choice to affect `make'; so, `make' handles the `SHELL' environment
variable in a special way; see *Note Choosing the Shell::.


File: make.info,  Node: Target-specific,  Next: Pattern-specific,  Prev: Environment,  Up: Using Variables

6.10 Target-specific Variable Values
====================================

Variable values in `make' are usually global; that is, they are the
same regardless of where they are evaluated (unless they're reset, of
course).  One exception to that is automatic variables (*note Automatic
Variables::).

   The other exception is "target-specific variable values".  This
feature allows you to define different values for the same variable,
based on the target that `make' is currently building.  As with
automatic variables, these values are only available within the context
of a target's command script (and in other target-specific assignments).

   Set a target-specific variable value like this:

     TARGET ... : VARIABLE-ASSIGNMENT

or like this:

     TARGET ... : override VARIABLE-ASSIGNMENT

or like this:

     TARGET ... : export VARIABLE-ASSIGNMENT

   Multiple TARGET values create a target-specific variable value for
each member of the target list individually.

   The VARIABLE-ASSIGNMENT can be any valid form of assignment;
recursive (`='), static (`:='), appending (`+='), or conditional
(`?=').  All variables that appear within the VARIABLE-ASSIGNMENT are
evaluated within the context of the target: thus, any
previously-defined target-specific variable values will be in effect.
Note that this variable is actually distinct from any "global" value:
the two variables do not have to have the same flavor (recursive vs.
static).

   Target-specific variables have the same priority as any other
makefile variable.  Variables provided on the command-line (and in the
environment if the `-e' option is in force) will take precedence.
Specifying the `override' directive will allow the target-specific
variable value to be preferred.

   There is one more special feature of target-specific variables: when
you define a target-specific variable that variable value is also in
effect for all prerequisites of this target, and all their
prerequisites, etc. (unless those prerequisites override that variable
with their own target-specific variable value).  So, for example, a
statement like this:

     prog : CFLAGS = -g
     prog : prog.o foo.o bar.o

will set `CFLAGS' to `-g' in the command script for `prog', but it will
also set `CFLAGS' to `-g' in the command scripts that create `prog.o',
`foo.o', and `bar.o', and any command scripts which create their
prerequisites.

   Be aware that a given prerequisite will only be built once per
invocation of make, at most.  If the same file is a prerequisite of
multiple targets, and each of those targets has a different value for
the same target-specific variable, then the first target to be built
will cause that prerequisite to be built and the prerequisite will
inherit the target-specific value from the first target.  It will
ignore the target-specific values from any other targets.


File: make.info,  Node: Pattern-specific,  Prev: Target-specific,  Up: Using Variables

6.11 Pattern-specific Variable Values
=====================================

In addition to target-specific variable values (*note Target-specific
Variable Values: Target-specific.), GNU `make' supports
pattern-specific variable values.  In this form, the variable is
defined for any target that matches the pattern specified.  If a target
matches more than one pattern, all the matching pattern-specific
variables are interpreted in the order in which they were defined in
the makefile, and collected together into one set.  Variables defined
in this way are searched after any target-specific variables defined
explicitly for that target, and before target-specific variables
defined for the parent target.

   Set a pattern-specific variable value like this:

     PATTERN ... : VARIABLE-ASSIGNMENT

or like this:

     PATTERN ... : override VARIABLE-ASSIGNMENT

where PATTERN is a %-pattern.  As with target-specific variable values,
multiple PATTERN values create a pattern-specific variable value for
each pattern individually.  The VARIABLE-ASSIGNMENT can be any valid
form of assignment.  Any command-line variable setting will take
precedence, unless `override' is specified.

   For example:

     %.o : CFLAGS = -O

will assign `CFLAGS' the value of `-O' for all targets matching the
pattern `%.o'.


File: make.info,  Node: Conditionals,  Next: Functions,  Prev: Using Variables,  Up: Top

7 Conditional Parts of Makefiles
********************************

A "conditional" causes part of a makefile to be obeyed or ignored
depending on the values of variables.  Conditionals can compare the
value of one variable to another, or the value of a variable to a
constant string.  Conditionals control what `make' actually "sees" in
the makefile, so they _cannot_ be used to control shell commands at the
time of execution.

* Menu:

* Conditional Example::         Example of a conditional
* Conditional Syntax::          The syntax of conditionals.
* Testing Flags::               Conditionals that test flags.


File: make.info,  Node: Conditional Example,  Next: Conditional Syntax,  Prev: Conditionals,  Up: Conditionals

7.1 Example of a Conditional
============================

The following example of a conditional tells `make' to use one set of
libraries if the `CC' variable is `gcc', and a different set of
libraries otherwise.  It works by controlling which of two command
lines will be used as the command for a rule.  The result is that
`CC=gcc' as an argument to `make' changes not only which compiler is
used but also which libraries are linked.

     libs_for_gcc = -lgnu
     normal_libs =

     foo: $(objects)
     ifeq ($(CC),gcc)
             $(CC) -o foo $(objects) $(libs_for_gcc)
     else
             $(CC) -o foo $(objects) $(normal_libs)
     endif

   This conditional uses three directives: one `ifeq', one `else' and
one `endif'.

   The `ifeq' directive begins the conditional, and specifies the
condition.  It contains two arguments, separated by a comma and
surrounded by parentheses.  Variable substitution is performed on both
arguments and then they are compared.  The lines of the makefile
following the `ifeq' are obeyed if the two arguments match; otherwise
they are ignored.

   The `else' directive causes the following lines to be obeyed if the
previous conditional failed.  In the example above, this means that the
second alternative linking command is used whenever the first
alternative is not used.  It is optional to have an `else' in a
conditional.

   The `endif' directive ends the conditional.  Every conditional must
end with an `endif'.  Unconditional makefile text follows.

   As this example illustrates, conditionals work at the textual level:
the lines of the conditional are treated as part of the makefile, or
ignored, according to the condition.  This is why the larger syntactic
units of the makefile, such as rules, may cross the beginning or the
end of the conditional.

   When the variable `CC' has the value `gcc', the above example has
this effect:

     foo: $(objects)
             $(CC) -o foo $(objects) $(libs_for_gcc)

When the variable `CC' has any other value, the effect is this:

     foo: $(objects)
             $(CC) -o foo $(objects) $(normal_libs)

   Equivalent results can be obtained in another way by
conditionalizing a variable assignment and then using the variable
unconditionally:

     libs_for_gcc = -lgnu
     normal_libs =

     ifeq ($(CC),gcc)
       libs=$(libs_for_gcc)
     else
       libs=$(normal_libs)
     endif

     foo: $(objects)
             $(CC) -o foo $(objects) $(libs)


File: make.info,  Node: Conditional Syntax,  Next: Testing Flags,  Prev: Conditional Example,  Up: Conditionals

7.2 Syntax of Conditionals
==========================

The syntax of a simple conditional with no `else' is as follows:

     CONDITIONAL-DIRECTIVE
     TEXT-IF-TRUE
     endif

The TEXT-IF-TRUE may be any lines of text, to be considered as part of
the makefile if the condition is true.  If the condition is false, no
text is used instead.

   The syntax of a complex conditional is as follows:

     CONDITIONAL-DIRECTIVE
     TEXT-IF-TRUE
     else
     TEXT-IF-FALSE
     endif

   or:

     CONDITIONAL-DIRECTIVE
     TEXT-IF-ONE-IS-TRUE
     else CONDITIONAL-DIRECTIVE
     TEXT-IF-TRUE
     else
     TEXT-IF-FALSE
     endif

There can be as many "`else' CONDITIONAL-DIRECTIVE" clauses as
necessary.  Once a given condition is true, TEXT-IF-TRUE is used and no
other clause is used; if no condition is true then TEXT-IF-FALSE is
used.  The TEXT-IF-TRUE and TEXT-IF-FALSE can be any number of lines of
text.

   The syntax of the CONDITIONAL-DIRECTIVE is the same whether the
conditional is simple or complex; after an `else' or not.  There are
four different directives that test different conditions.  Here is a
table of them:

`ifeq (ARG1, ARG2)'
`ifeq 'ARG1' 'ARG2''
`ifeq "ARG1" "ARG2"'
`ifeq "ARG1" 'ARG2''
`ifeq 'ARG1' "ARG2"'
     Expand all variable references in ARG1 and ARG2 and compare them.
     If they are identical, the TEXT-IF-TRUE is effective; otherwise,
     the TEXT-IF-FALSE, if any, is effective.

     Often you want to test if a variable has a non-empty value.  When
     the value results from complex expansions of variables and
     functions, expansions you would consider empty may actually
     contain whitespace characters and thus are not seen as empty.
     However, you can use the `strip' function (*note Text Functions::)
     to avoid interpreting whitespace as a non-empty value.  For
     example:

          ifeq ($(strip $(foo)),)
          TEXT-IF-EMPTY
          endif

     will evaluate TEXT-IF-EMPTY even if the expansion of `$(foo)'
     contains whitespace characters.

`ifneq (ARG1, ARG2)'
`ifneq 'ARG1' 'ARG2''
`ifneq "ARG1" "ARG2"'
`ifneq "ARG1" 'ARG2''
`ifneq 'ARG1' "ARG2"'
     Expand all variable references in ARG1 and ARG2 and compare them.
     If they are different, the TEXT-IF-TRUE is effective; otherwise,
     the TEXT-IF-FALSE, if any, is effective.

`ifdef VARIABLE-NAME'
     The `ifdef' form takes the _name_ of a variable as its argument,
     not a reference to a variable.  The value of that variable has a
     non-empty value, the TEXT-IF-TRUE is effective; otherwise, the
     TEXT-IF-FALSE, if any, is effective.  Variables that have never
     been defined have an empty value.  The text VARIABLE-NAME is
     expanded, so it could be a variable or function that expands to
     the name of a variable.  For example:

          bar = true
          foo = bar
          ifdef $(foo)
          frobozz = yes
          endif

     The variable reference `$(foo)' is expanded, yielding `bar', which
     is considered to be the name of a variable.  The variable `bar' is
     not expanded, but its value is examined to determine if it is
     non-empty.

     Note that `ifdef' only tests whether a variable has a value.  It
     does not expand the variable to see if that value is nonempty.
     Consequently, tests using `ifdef' return true for all definitions
     except those like `foo ='.  To test for an empty value, use
     `ifeq ($(foo),)'.  For example,

          bar =
          foo = $(bar)
          ifdef foo
          frobozz = yes
          else
          frobozz = no
          endif

     sets `frobozz' to `yes', while:

          foo =
          ifdef foo
          frobozz = yes
          else
          frobozz = no
          endif

     sets `frobozz' to `no'.

`ifndef VARIABLE-NAME'
     If the variable VARIABLE-NAME has an empty value, the TEXT-IF-TRUE
     is effective; otherwise, the TEXT-IF-FALSE, if any, is effective.
     The rules for expansion and testing of VARIABLE-NAME are identical
     to the `ifdef' directive.

   Extra spaces are allowed and ignored at the beginning of the
conditional directive line, but a tab is not allowed.  (If the line
begins with a tab, it will be considered a command for a rule.)  Aside
from this, extra spaces or tabs may be inserted with no effect anywhere
except within the directive name or within an argument.  A comment
starting with `#' may appear at the end of the line.

   The other two directives that play a part in a conditional are `else'
and `endif'.  Each of these directives is written as one word, with no
arguments.  Extra spaces are allowed and ignored at the beginning of the
line, and spaces or tabs at the end.  A comment starting with `#' may
appear at the end of the line.

   Conditionals affect which lines of the makefile `make' uses.  If the
condition is true, `make' reads the lines of the TEXT-IF-TRUE as part
of the makefile; if the condition is false, `make' ignores those lines
completely.  It follows that syntactic units of the makefile, such as
rules, may safely be split across the beginning or the end of the
conditional.

   `make' evaluates conditionals when it reads a makefile.
Consequently, you cannot use automatic variables in the tests of
conditionals because they are not defined until commands are run (*note
Automatic Variables::).

   To prevent intolerable confusion, it is not permitted to start a
conditional in one makefile and end it in another.  However, you may
write an `include' directive within a conditional, provided you do not
attempt to terminate the conditional inside the included file.


File: make.info,  Node: Testing Flags,  Prev: Conditional Syntax,  Up: Conditionals

7.3 Conditionals that Test Flags
================================

You can write a conditional that tests `make' command flags such as
`-t' by using the variable `MAKEFLAGS' together with the `findstring'
function (*note Functions for String Substitution and Analysis: Text
Functions.).  This is useful when `touch' is not enough to make a file
appear up to date.

   The `findstring' function determines whether one string appears as a
substring of another.  If you want to test for the `-t' flag, use `t'
as the first string and the value of `MAKEFLAGS' as the other.

   For example, here is how to arrange to use `ranlib -t' to finish
marking an archive file up to date:

     archive.a: ...
     ifneq (,$(findstring t,$(MAKEFLAGS)))
             +touch archive.a
             +ranlib -t archive.a
     else
             ranlib archive.a
     endif

The `+' prefix marks those command lines as "recursive" so that they
will be executed despite use of the `-t' flag.  *Note Recursive Use of
`make': Recursion.


File: make.info,  Node: Functions,  Next: Running,  Prev: Conditionals,  Up: Top

8 Functions for Transforming Text
*********************************

"Functions" allow you to do text processing in the makefile to compute
the files to operate on or the commands to use.  You use a function in a
"function call", where you give the name of the function and some text
(the "arguments") for the function to operate on.  The result of the
function's processing is substituted into the makefile at the point of
the call, just as a variable might be substituted.

* Menu:

* Syntax of Functions::         How to write a function call.
* Text Functions::              General-purpose text manipulation functions.
* File Name Functions::         Functions for manipulating file names.
* Conditional Functions::       Functions that implement conditions.
* Foreach Function::            Repeat some text with controlled variation.
* Call Function::               Expand a user-defined function.
* Value Function::              Return the un-expanded value of a variable.
* Eval Function::               Evaluate the arguments as makefile syntax.
* Origin Function::             Find where a variable got its value.
* Flavor Function::             Find out the flavor of a variable.
* Shell Function::              Substitute the output of a shell command.
* Make Control Functions::      Functions that control how make runs.


File: make.info,  Node: Syntax of Functions,  Next: Text Functions,  Prev: Functions,  Up: Functions

8.1 Function Call Syntax
========================

A function call resembles a variable reference.  It looks like this:

     $(FUNCTION ARGUMENTS)

or like this:

     ${FUNCTION ARGUMENTS}

   Here FUNCTION is a function name; one of a short list of names that
are part of `make'.  You can also essentially create your own functions
by using the `call' builtin function.

   The ARGUMENTS are the arguments of the function.  They are separated
from the function name by one or more spaces or tabs, and if there is
more than one argument, then they are separated by commas.  Such
whitespace and commas are not part of an argument's value.  The
delimiters which you use to surround the function call, whether
parentheses or braces, can appear in an argument only in matching pairs;
the other kind of delimiters may appear singly.  If the arguments
themselves contain other function calls or variable references, it is
wisest to use the same kind of delimiters for all the references; write
`$(subst a,b,$(x))', not `$(subst a,b,${x})'.  This is because it is
clearer, and because only one type of delimiter is matched to find the
end of the reference.

   The text written for each argument is processed by substitution of
variables and function calls to produce the argument value, which is
the text on which the function acts.  The substitution is done in the
order in which the arguments appear.

   Commas and unmatched parentheses or braces cannot appear in the text
of an argument as written; leading spaces cannot appear in the text of
the first argument as written.  These characters can be put into the
argument value by variable substitution.  First define variables
`comma' and `space' whose values are isolated comma and space
characters, then substitute these variables where such characters are
wanted, like this:

     comma:= ,
     empty:=
     space:= $(empty) $(empty)
     foo:= a b c
     bar:= $(subst $(space),$(comma),$(foo))
     # bar is now `a,b,c'.

Here the `subst' function replaces each space with a comma, through the
value of `foo', and substitutes the result.


File: make.info,  Node: Text Functions,  Next: File Name Functions,  Prev: Syntax of Functions,  Up: Functions

8.2 Functions for String Substitution and Analysis
==================================================

Here are some functions that operate on strings:

`$(subst FROM,TO,TEXT)'
     Performs a textual replacement on the text TEXT: each occurrence
     of FROM is replaced by TO.  The result is substituted for the
     function call.  For example,

          $(subst ee,EE,feet on the street)

     substitutes the string `fEEt on the strEEt'.

`$(patsubst PATTERN,REPLACEMENT,TEXT)'
     Finds whitespace-separated words in TEXT that match PATTERN and
     replaces them with REPLACEMENT.  Here PATTERN may contain a `%'
     which acts as a wildcard, matching any number of any characters
     within a word.  If REPLACEMENT also contains a `%', the `%' is
     replaced by the text that matched the `%' in PATTERN.  Only the
     first `%' in the PATTERN and REPLACEMENT is treated this way; any
     subsequent `%' is unchanged.

     `%' characters in `patsubst' function invocations can be quoted
     with preceding backslashes (`\').  Backslashes that would
     otherwise quote `%' characters can be quoted with more backslashes.
     Backslashes that quote `%' characters or other backslashes are
     removed from the pattern before it is compared file names or has a
     stem substituted into it.  Backslashes that are not in danger of
     quoting `%' characters go unmolested.  For example, the pattern
     `the\%weird\\%pattern\\' has `the%weird\' preceding the operative
     `%' character, and `pattern\\' following it.  The final two
     backslashes are left alone because they cannot affect any `%'
     character.

     Whitespace between words is folded into single space characters;
     leading and trailing whitespace is discarded.

     For example,

          $(patsubst %.c,%.o,x.c.c bar.c)

     produces the value `x.c.o bar.o'.

     Substitution references (*note Substitution References:
     Substitution Refs.) are a simpler way to get the effect of the
     `patsubst' function:

          $(VAR:PATTERN=REPLACEMENT)

     is equivalent to

          $(patsubst PATTERN,REPLACEMENT,$(VAR))

     The second shorthand simplifies one of the most common uses of
     `patsubst': replacing the suffix at the end of file names.

          $(VAR:SUFFIX=REPLACEMENT)

     is equivalent to

          $(patsubst %SUFFIX,%REPLACEMENT,$(VAR))

     For example, you might have a list of object files:

          objects = foo.o bar.o baz.o

     To get the list of corresponding source files, you could simply
     write:

          $(objects:.o=.c)

     instead of using the general form:

          $(patsubst %.o,%.c,$(objects))

`$(strip STRING)'
     Removes leading and trailing whitespace from STRING and replaces
     each internal sequence of one or more whitespace characters with a
     single space.  Thus, `$(strip a b  c )' results in `a b c'.

     The function `strip' can be very useful when used in conjunction
     with conditionals.  When comparing something with the empty string
     `' using `ifeq' or `ifneq', you usually want a string of just
     whitespace to match the empty string (*note Conditionals::).

     Thus, the following may fail to have the desired results:

          .PHONY: all
          ifneq   "$(needs_made)" ""
          all: $(needs_made)
          else
          all:;@echo 'Nothing to make!'
          endif

     Replacing the variable reference `$(needs_made)' with the function
     call `$(strip $(needs_made))' in the `ifneq' directive would make
     it more robust.

`$(findstring FIND,IN)'
     Searches IN for an occurrence of FIND.  If it occurs, the value is
     FIND; otherwise, the value is empty.  You can use this function in
     a conditional to test for the presence of a specific substring in
     a given string.  Thus, the two examples,

          $(findstring a,a b c)
          $(findstring a,b c)

     produce the values `a' and `' (the empty string), respectively.
     *Note Testing Flags::, for a practical application of `findstring'.

`$(filter PATTERN...,TEXT)'
     Returns all whitespace-separated words in TEXT that _do_ match any
     of the PATTERN words, removing any words that _do not_ match.  The
     patterns are written using `%', just like the patterns used in the
     `patsubst' function above.

     The `filter' function can be used to separate out different types
     of strings (such as file names) in a variable.  For example:

          sources := foo.c bar.c baz.s ugh.h
          foo: $(sources)
                  cc $(filter %.c %.s,$(sources)) -o foo

     says that `foo' depends of `foo.c', `bar.c', `baz.s' and `ugh.h'
     but only `foo.c', `bar.c' and `baz.s' should be specified in the
     command to the compiler.

`$(filter-out PATTERN...,TEXT)'
     Returns all whitespace-separated words in TEXT that _do not_ match
     any of the PATTERN words, removing the words that _do_ match one
     or more.  This is the exact opposite of the `filter' function.

     For example, given:

          objects=main1.o foo.o main2.o bar.o
          mains=main1.o main2.o

     the following generates a list which contains all the object files
     not in `mains':

          $(filter-out $(mains),$(objects))

`$(sort LIST)'
     Sorts the words of LIST in lexical order, removing duplicate
     words.  The output is a list of words separated by single spaces.
     Thus,

          $(sort foo bar lose)

     returns the value `bar foo lose'.

     Incidentally, since `sort' removes duplicate words, you can use it
     for this purpose even if you don't care about the sort order.

`$(word N,TEXT)'
     Returns the Nth word of TEXT.  The legitimate values of N start
     from 1.  If N is bigger than the number of words in TEXT, the
     value is empty.  For example,

          $(word 2, foo bar baz)

     returns `bar'.

`$(wordlist S,E,TEXT)'
     Returns the list of words in TEXT starting with word S and ending
     with word E (inclusive).  The legitimate values of S start from 1;
     E may start from 0.  If S is bigger than the number of words in
     TEXT, the value is empty.  If E is bigger than the number of words
     in TEXT, words up to the end of TEXT are returned.  If S is
     greater than E, nothing is returned.  For example,

          $(wordlist 2, 3, foo bar baz)

     returns `bar baz'.

`$(words TEXT)'
     Returns the number of words in TEXT.  Thus, the last word of TEXT
     is `$(word $(words TEXT),TEXT)'.

`$(firstword NAMES...)'
     The argument NAMES is regarded as a series of names, separated by
     whitespace.  The value is the first name in the series.  The rest
     of the names are ignored.

     For example,

          $(firstword foo bar)

     produces the result `foo'.  Although `$(firstword TEXT)' is the
     same as `$(word 1,TEXT)', the `firstword' function is retained for
     its simplicity.

`$(lastword NAMES...)'
     The argument NAMES is regarded as a series of names, separated by
     whitespace.  The value is the last name in the series.

     For example,

          $(lastword foo bar)

     produces the result `bar'.  Although `$(lastword TEXT)' is the
     same as `$(word $(words TEXT),TEXT)', the `lastword' function was
     added for its simplicity and better performance.

   Here is a realistic example of the use of `subst' and `patsubst'.
Suppose that a makefile uses the `VPATH' variable to specify a list of
directories that `make' should search for prerequisite files (*note
`VPATH' Search Path for All Prerequisites: General Search.).  This
example shows how to tell the C compiler to search for header files in
the same list of directories.

   The value of `VPATH' is a list of directories separated by colons,
such as `src:../headers'.  First, the `subst' function is used to
change the colons to spaces:

     $(subst :, ,$(VPATH))

This produces `src ../headers'.  Then `patsubst' is used to turn each
directory name into a `-I' flag.  These can be added to the value of
the variable `CFLAGS', which is passed automatically to the C compiler,
like this:

     override CFLAGS += $(patsubst %,-I%,$(subst :, ,$(VPATH)))

The effect is to append the text `-Isrc -I../headers' to the previously
given value of `CFLAGS'.  The `override' directive is used so that the
new value is assigned even if the previous value of `CFLAGS' was
specified with a command argument (*note The `override' Directive:
Override Directive.).


File: make.info,  Node: File Name Functions,  Next: Conditional Functions,  Prev: Text Functions,  Up: Functions

8.3 Functions for File Names
============================

Several of the built-in expansion functions relate specifically to
taking apart file names or lists of file names.

   Each of the following functions performs a specific transformation
on a file name.  The argument of the function is regarded as a series
of file names, separated by whitespace.  (Leading and trailing
whitespace is ignored.)  Each file name in the series is transformed in
the same way and the results are concatenated with single spaces
between them.

`$(dir NAMES...)'
     Extracts the directory-part of each file name in NAMES.  The
     directory-part of the file name is everything up through (and
     including) the last slash in it.  If the file name contains no
     slash, the directory part is the string `./'.  For example,

          $(dir src/foo.c hacks)

     produces the result `src/ ./'.

`$(notdir NAMES...)'
     Extracts all but the directory-part of each file name in NAMES.
     If the file name contains no slash, it is left unchanged.
     Otherwise, everything through the last slash is removed from it.

     A file name that ends with a slash becomes an empty string.  This
     is unfortunate, because it means that the result does not always
     have the same number of whitespace-separated file names as the
     argument had; but we do not see any other valid alternative.

     For example,

          $(notdir src/foo.c hacks)

     produces the result `foo.c hacks'.

`$(suffix NAMES...)'
     Extracts the suffix of each file name in NAMES.  If the file name
     contains a period, the suffix is everything starting with the last
     period.  Otherwise, the suffix is the empty string.  This
     frequently means that the result will be empty when NAMES is not,
     and if NAMES contains multiple file names, the result may contain
     fewer file names.

     For example,

          $(suffix src/foo.c src-1.0/bar.c hacks)

     produces the result `.c .c'.

`$(basename NAMES...)'
     Extracts all but the suffix of each file name in NAMES.  If the
     file name contains a period, the basename is everything starting
     up to (and not including) the last period.  Periods in the
     directory part are ignored.  If there is no period, the basename
     is the entire file name.  For example,

          $(basename src/foo.c src-1.0/bar hacks)

     produces the result `src/foo src-1.0/bar hacks'.

`$(addsuffix SUFFIX,NAMES...)'
     The argument NAMES is regarded as a series of names, separated by
     whitespace; SUFFIX is used as a unit.  The value of SUFFIX is
     appended to the end of each individual name and the resulting
     larger names are concatenated with single spaces between them.
     For example,

          $(addsuffix .c,foo bar)

     produces the result `foo.c bar.c'.

`$(addprefix PREFIX,NAMES...)'
     The argument NAMES is regarded as a series of names, separated by
     whitespace; PREFIX is used as a unit.  The value of PREFIX is
     prepended to the front of each individual name and the resulting
     larger names are concatenated with single spaces between them.
     For example,

          $(addprefix src/,foo bar)

     produces the result `src/foo src/bar'.

`$(join LIST1,LIST2)'
     Concatenates the two arguments word by word: the two first words
     (one from each argument) concatenated form the first word of the
     result, the two second words form the second word of the result,
     and so on.  So the Nth word of the result comes from the Nth word
     of each argument.  If one argument has more words that the other,
     the extra words are copied unchanged into the result.

     For example, `$(join a b,.c .o)' produces `a.c b.o'.

     Whitespace between the words in the lists is not preserved; it is
     replaced with a single space.

     This function can merge the results of the `dir' and `notdir'
     functions, to produce the original list of files which was given
     to those two functions.

`$(wildcard PATTERN)'
     The argument PATTERN is a file name pattern, typically containing
     wildcard characters (as in shell file name patterns).  The result
     of `wildcard' is a space-separated list of the names of existing
     files that match the pattern.  *Note Using Wildcard Characters in
     File Names: Wildcards.

`$(realpath NAMES...)'
     For each file name in NAMES return the canonical absolute name.  A
     canonical name does not contain any `.' or `..' components, nor
     any repeated path separators (`/') or symlinks.  In case of a
     failure the empty string is returned.  Consult the `realpath(3)'
     documentation for a list of possible failure causes.

`$(abspath NAMES...)'
     For each file name in NAMES return an absolute name that does not
     contain any `.' or `..' components, nor any repeated path
     separators (`/').  Note that, in contrast to `realpath' function,
     `abspath' does not resolve symlinks and does not require the file
     names to refer to an existing file or directory.  Use the
     `wildcard' function to test for existence.


File: make.info,  Node: Conditional Functions,  Next: Foreach Function,  Prev: File Name Functions,  Up: Functions

8.4 Functions for Conditionals
==============================

There are three functions that provide conditional expansion.  A key
aspect of these functions is that not all of the arguments are expanded
initially.  Only those arguments which need to be expanded, will be
expanded.

`$(if CONDITION,THEN-PART[,ELSE-PART])'
     The `if' function provides support for conditional expansion in a
     functional context (as opposed to the GNU `make' makefile
     conditionals such as `ifeq' (*note Syntax of Conditionals:
     Conditional Syntax.).

     The first argument, CONDITION, first has all preceding and
     trailing whitespace stripped, then is expanded.  If it expands to
     any non-empty string, then the condition is considered to be true.
     If it expands to an empty string, the condition is considered to
     be false.

     If the condition is true then the second argument, THEN-PART, is
     evaluated and this is used as the result of the evaluation of the
     entire `if' function.

     If the condition is false then the third argument, ELSE-PART, is
     evaluated and this is the result of the `if' function.  If there is
     no third argument, the `if' function evaluates to nothing (the
     empty string).

     Note that only one of the THEN-PART or the ELSE-PART will be
     evaluated, never both.  Thus, either can contain side-effects
     (such as `shell' function calls, etc.)

`$(or CONDITION1[,CONDITION2[,CONDITION3...]])'
     The `or' function provides a "short-circuiting" OR operation.
     Each argument is expanded, in order.  If an argument expands to a
     non-empty string the processing stops and the result of the
     expansion is that string.  If, after all arguments are expanded,
     all of them are false (empty), then the result of the expansion is
     the empty string.

`$(and CONDITION1[,CONDITION2[,CONDITION3...]])'
     The `and' function provides a "short-circuiting" AND operation.
     Each argument is expanded, in order.  If an argument expands to an
     empty string the processing stops and the result of the expansion
     is the empty string.  If all arguments expand to a non-empty
     string then the result of the expansion is the expansion of the
     last argument.



File: make.info,  Node: Foreach Function,  Next: Call Function,  Prev: Conditional Functions,  Up: Functions

8.5 The `foreach' Function
==========================

The `foreach' function is very different from other functions.  It
causes one piece of text to be used repeatedly, each time with a
different substitution performed on it.  It resembles the `for' command
in the shell `sh' and the `foreach' command in the C-shell `csh'.

   The syntax of the `foreach' function is:

     $(foreach VAR,LIST,TEXT)

The first two arguments, VAR and LIST, are expanded before anything
else is done; note that the last argument, TEXT, is *not* expanded at
the same time.  Then for each word of the expanded value of LIST, the
variable named by the expanded value of VAR is set to that word, and
TEXT is expanded.  Presumably TEXT contains references to that
variable, so its expansion will be different each time.

   The result is that TEXT is expanded as many times as there are
whitespace-separated words in LIST.  The multiple expansions of TEXT
are concatenated, with spaces between them, to make the result of
`foreach'.

   This simple example sets the variable `files' to the list of all
files in the directories in the list `dirs':

     dirs := a b c d
     files := $(foreach dir,$(dirs),$(wildcard $(dir)/*))

   Here TEXT is `$(wildcard $(dir)/*)'.  The first repetition finds the
value `a' for `dir', so it produces the same result as `$(wildcard
a/*)'; the second repetition produces the result of `$(wildcard b/*)';
and the third, that of `$(wildcard c/*)'.

   This example has the same result (except for setting `dirs') as the
following example:

     files := $(wildcard a/* b/* c/* d/*)

   When TEXT is complicated, you can improve readability by giving it a
name, with an additional variable:

     find_files = $(wildcard $(dir)/*)
     dirs := a b c d
     files := $(foreach dir,$(dirs),$(find_files))

Here we use the variable `find_files' this way.  We use plain `=' to
define a recursively-expanding variable, so that its value contains an
actual function call to be reexpanded under the control of `foreach'; a
simply-expanded variable would not do, since `wildcard' would be called
only once at the time of defining `find_files'.

   The `foreach' function has no permanent effect on the variable VAR;
its value and flavor after the `foreach' function call are the same as
they were beforehand.  The other values which are taken from LIST are
in effect only temporarily, during the execution of `foreach'.  The
variable VAR is a simply-expanded variable during the execution of
`foreach'.  If VAR was undefined before the `foreach' function call, it
is undefined after the call.  *Note The Two Flavors of Variables:
Flavors.

   You must take care when using complex variable expressions that
result in variable names because many strange things are valid variable
names, but are probably not what you intended.  For example,

     files := $(foreach Esta escrito en espanol!,b c ch,$(find_files))

might be useful if the value of `find_files' references the variable
whose name is `Esta escrito en espanol!' (es un nombre bastante largo,
no?), but it is more likely to be a mistake.


File: make.info,  Node: Call Function,  Next: Value Function,  Prev: Foreach Function,  Up: Functions

8.6 The `call' Function
=======================

The `call' function is unique in that it can be used to create new
parameterized functions.  You can write a complex expression as the
value of a variable, then use `call' to expand it with different values.

   The syntax of the `call' function is:

     $(call VARIABLE,PARAM,PARAM,...)

   When `make' expands this function, it assigns each PARAM to
temporary variables `$(1)', `$(2)', etc.  The variable `$(0)' will
contain VARIABLE.  There is no maximum number of parameter arguments.
There is no minimum, either, but it doesn't make sense to use `call'
with no parameters.

   Then VARIABLE is expanded as a `make' variable in the context of
these temporary assignments.  Thus, any reference to `$(1)' in the
value of VARIABLE will resolve to the first PARAM in the invocation of
`call'.

   Note that VARIABLE is the _name_ of a variable, not a _reference_ to
that variable.  Therefore you would not normally use a `$' or
parentheses when writing it.  (You can, however, use a variable
reference in the name if you want the name not to be a constant.)

   If VARIABLE is the name of a builtin function, the builtin function
is always invoked (even if a `make' variable by that name also exists).

   The `call' function expands the PARAM arguments before assigning
them to temporary variables.  This means that VARIABLE values
containing references to builtin functions that have special expansion
rules, like `foreach' or `if', may not work as you expect.

   Some examples may make this clearer.

   This macro simply reverses its arguments:

     reverse = $(2) $(1)

     foo = $(call reverse,a,b)

Here FOO will contain `b a'.

   This one is slightly more interesting: it defines a macro to search
for the first instance of a program in `PATH':

     pathsearch = $(firstword $(wildcard $(addsuffix /$(1),$(subst :, ,$(PATH)))))

     LS := $(call pathsearch,ls)

Now the variable LS contains `/bin/ls' or similar.

   The `call' function can be nested.  Each recursive invocation gets
its own local values for `$(1)', etc. that mask the values of
higher-level `call'.  For example, here is an implementation of a "map"
function:

     map = $(foreach a,$(2),$(call $(1),$(a)))

   Now you can MAP a function that normally takes only one argument,
such as `origin', to multiple values in one step:

     o = $(call map,origin,o map MAKE)

   and end up with O containing something like `file file default'.

   A final caution: be careful when adding whitespace to the arguments
to `call'.  As with other functions, any whitespace contained in the
second and subsequent arguments is kept; this can cause strange
effects.  It's generally safest to remove all extraneous whitespace when
providing parameters to `call'.


File: make.info,  Node: Value Function,  Next: Eval Function,  Prev: Call Function,  Up: Functions

8.7 The `value' Function
========================

The `value' function provides a way for you to use the value of a
variable _without_ having it expanded.  Please note that this does not
undo expansions which have already occurred; for example if you create
a simply expanded variable its value is expanded during the definition;
in that case the `value' function will return the same result as using
the variable directly.

   The syntax of the `value' function is:

     $(value VARIABLE)

   Note that VARIABLE is the _name_ of a variable; not a _reference_ to
that variable.  Therefore you would not normally use a `$' or
parentheses when writing it.  (You can, however, use a variable
reference in the name if you want the name not to be a constant.)

   The result of this function is a string containing the value of
VARIABLE, without any expansion occurring.  For example, in this
makefile:

     FOO = $PATH

     all:
             @echo $(FOO)
             @echo $(value FOO)

The first output line would be `ATH', since the "$P" would be expanded
as a `make' variable, while the second output line would be the current
value of your `$PATH' environment variable, since the `value' function
avoided the expansion.

   The `value' function is most often used in conjunction with the
`eval' function (*note Eval Function::).


File: make.info,  Node: Eval Function,  Next: Origin Function,  Prev: Value Function,  Up: Functions

8.8 The `eval' Function
=======================

The `eval' function is very special: it allows you to define new
makefile constructs that are not constant; which are the result of
evaluating other variables and functions.  The argument to the `eval'
function is expanded, then the results of that expansion are parsed as
makefile syntax.  The expanded results can define new `make' variables,
targets, implicit or explicit rules, etc.

   The result of the `eval' function is always the empty string; thus,
it can be placed virtually anywhere in a makefile without causing
syntax errors.

   It's important to realize that the `eval' argument is expanded
_twice_; first by the `eval' function, then the results of that
expansion are expanded again when they are parsed as makefile syntax.
This means you may need to provide extra levels of escaping for "$"
characters when using `eval'.  The `value' function (*note Value
Function::) can sometimes be useful in these situations, to circumvent
unwanted expansions.

   Here is an example of how `eval' can be used; this example combines
a number of concepts and other functions.  Although it might seem
overly complex to use `eval' in this example, rather than just writing
out the rules, consider two things: first, the template definition (in
`PROGRAM_template') could need to be much more complex than it is here;
and second, you might put the complex, "generic" part of this example
into another makefile, then include it in all the individual makefiles.
Now your individual makefiles are quite straightforward.

     PROGRAMS    = server client

     server_OBJS = server.o server_priv.o server_access.o
     server_LIBS = priv protocol

     client_OBJS = client.o client_api.o client_mem.o
     client_LIBS = protocol

     # Everything after this is generic

     .PHONY: all
     all: $(PROGRAMS)

     define PROGRAM_template
      $(1): $$($(1)_OBJS) $$($(1)_LIBS:%=-l%)
      ALL_OBJS   += $$($(1)_OBJS)
     endef

     $(foreach prog,$(PROGRAMS),$(eval $(call PROGRAM_template,$(prog))))

     $(PROGRAMS):
             $(LINK.o) $^ $(LDLIBS) -o $@

     clean:
             rm -f $(ALL_OBJS) $(PROGRAMS)


File: make.info,  Node: Origin Function,  Next: Flavor Function,  Prev: Eval Function,  Up: Functions

8.9 The `origin' Function
=========================

The `origin' function is unlike most other functions in that it does
not operate on the values of variables; it tells you something _about_
a variable.  Specifically, it tells you where it came from.

   The syntax of the `origin' function is:

     $(origin VARIABLE)

   Note that VARIABLE is the _name_ of a variable to inquire about; not
a _reference_ to that variable.  Therefore you would not normally use a
`$' or parentheses when writing it.  (You can, however, use a variable
reference in the name if you want the name not to be a constant.)

   The result of this function is a string telling you how the variable
VARIABLE was defined:

`undefined'
     if VARIABLE was never defined.

`default'
     if VARIABLE has a default definition, as is usual with `CC' and so
     on.  *Note Variables Used by Implicit Rules: Implicit Variables.
     Note that if you have redefined a default variable, the `origin'
     function will return the origin of the later definition.

`environment'
     if VARIABLE was defined as an environment variable and the `-e'
     option is _not_ turned on (*note Summary of Options: Options
     Summary.).

`environment override'
     if VARIABLE was defined as an environment variable and the `-e'
     option _is_ turned on (*note Summary of Options: Options Summary.).

`file'
     if VARIABLE was defined in a makefile.

`command line'
     if VARIABLE was defined on the command line.

`override'
     if VARIABLE was defined with an `override' directive in a makefile
     (*note The `override' Directive: Override Directive.).

`automatic'
     if VARIABLE is an automatic variable defined for the execution of
     the commands for each rule (*note Automatic Variables::).

   This information is primarily useful (other than for your curiosity)
to determine if you want to believe the value of a variable.  For
example, suppose you have a makefile `foo' that includes another
makefile `bar'.  You want a variable `bletch' to be defined in `bar' if
you run the command `make -f bar', even if the environment contains a
definition of `bletch'.  However, if `foo' defined `bletch' before
including `bar', you do not want to override that definition.  This
could be done by using an `override' directive in `foo', giving that
definition precedence over the later definition in `bar';
unfortunately, the `override' directive would also override any command
line definitions.  So, `bar' could include:

     ifdef bletch
     ifeq "$(origin bletch)" "environment"
     bletch = barf, gag, etc.
     endif
     endif

If `bletch' has been defined from the environment, this will redefine
it.

   If you want to override a previous definition of `bletch' if it came
from the environment, even under `-e', you could instead write:

     ifneq "$(findstring environment,$(origin bletch))" ""
     bletch = barf, gag, etc.
     endif

   Here the redefinition takes place if `$(origin bletch)' returns
either `environment' or `environment override'.  *Note Functions for
String Substitution and Analysis: Text Functions.


File: make.info,  Node: Flavor Function,  Next: Shell Function,  Prev: Origin Function,  Up: Functions

8.10 The `flavor' Function
==========================

The `flavor' function is unlike most other functions (and like `origin'
function) in that it does not operate on the values of variables; it
tells you something _about_ a variable.  Specifically, it tells you the
flavor of a variable (*note The Two Flavors of Variables: Flavors.).

   The syntax of the `flavor' function is:

     $(flavor VARIABLE)

   Note that VARIABLE is the _name_ of a variable to inquire about; not
a _reference_ to that variable.  Therefore you would not normally use a
`$' or parentheses when writing it.  (You can, however, use a variable
reference in the name if you want the name not to be a constant.)

   The result of this function is a string that identifies the flavor
of the variable VARIABLE:

`undefined'
     if VARIABLE was never defined.

`recursive'
     if VARIABLE is a recursively expanded variable.

`simple'
     if VARIABLE is a simply expanded variable.



File: make.info,  Node: Shell Function,  Next: Make Control Functions,  Prev: Flavor Function,  Up: Functions

8.11 The `shell' Function
=========================

The `shell' function is unlike any other function other than the
`wildcard' function (*note The Function `wildcard': Wildcard Function.)
in that it communicates with the world outside of `make'.

   The `shell' function performs the same function that backquotes
(``') perform in most shells: it does "command expansion".  This means
that it takes as an argument a shell command and evaluates to the
output of the command.  The only processing `make' does on the result
is to convert each newline (or carriage-return / newline pair) to a
single space.  If there is a trailing (carriage-return and) newline it
will simply be removed.

   The commands run by calls to the `shell' function are run when the
function calls are expanded (*note How `make' Reads a Makefile: Reading
Makefiles.).  Because this function involves spawning a new shell, you
should carefully consider the performance implications of using the
`shell' function within recursively expanded variables vs. simply
expanded variables (*note The Two Flavors of Variables: Flavors.).

   Here are some examples of the use of the `shell' function:

     contents := $(shell cat foo)

sets `contents' to the contents of the file `foo', with a space (rather
than a newline) separating each line.

     files := $(shell echo *.c)

sets `files' to the expansion of `*.c'.  Unless `make' is using a very
strange shell, this has the same result as `$(wildcard *.c)' (as long
as at least one `.c' file exists).


File: make.info,  Node: Make Control Functions,  Prev: Shell Function,  Up: Functions

8.12 Functions That Control Make
================================

These functions control the way make runs.  Generally, they are used to
provide information to the user of the makefile or to cause make to stop
if some sort of environmental error is detected.

`$(error TEXT...)'
     Generates a fatal error where the message is TEXT.  Note that the
     error is generated whenever this function is evaluated.  So, if
     you put it inside a command script or on the right side of a
     recursive variable assignment, it won't be evaluated until later.
     The TEXT will be expanded before the error is generated.

     For example,

          ifdef ERROR1
          $(error error is $(ERROR1))
          endif

     will generate a fatal error during the read of the makefile if the
     `make' variable `ERROR1' is defined.  Or,

          ERR = $(error found an error!)

          .PHONY: err
          err: ; $(ERR)

     will generate a fatal error while `make' is running, if the `err'
     target is invoked.

`$(warning TEXT...)'
     This function works similarly to the `error' function, above,
     except that `make' doesn't exit.  Instead, TEXT is expanded and
     the resulting message is displayed, but processing of the makefile
     continues.

     The result of the expansion of this function is the empty string.

`$(info TEXT...)'
     This function does nothing more than print its (expanded)
     argument(s) to standard output.  No makefile name or line number
     is added.  The result of the expansion of this function is the
     empty string.


File: make.info,  Node: Running,  Next: Implicit Rules,  Prev: Functions,  Up: Top

9 How to Run `make'
*******************

A makefile that says how to recompile a program can be used in more
than one way.  The simplest use is to recompile every file that is out
of date.  Usually, makefiles are written so that if you run `make' with
no arguments, it does just that.

   But you might want to update only some of the files; you might want
to use a different compiler or different compiler options; you might
want just to find out which files are out of date without changing them.

   By giving arguments when you run `make', you can do any of these
things and many others.

   The exit status of `make' is always one of three values:
`0'
     The exit status is zero if `make' is successful.

`2'
     The exit status is two if `make' encounters any errors.  It will
     print messages describing the particular errors.

`1'
     The exit status is one if you use the `-q' flag and `make'
     determines that some target is not already up to date.  *Note
     Instead of Executing the Commands: Instead of Execution.

* Menu:

* Makefile Arguments::          How to specify which makefile to use.
* Goals::                       How to use goal arguments to specify which
                                  parts of the makefile to use.
* Instead of Execution::        How to use mode flags to specify what
                                  kind of thing to do with the commands
                                  in the makefile other than simply
                                  execute them.
* Avoiding Compilation::        How to avoid recompiling certain files.
* Overriding::                  How to override a variable to specify
                                  an alternate compiler and other things.
* Testing::                     How to proceed past some errors, to
                                  test compilation.
* Options Summary::             Summary of Options


File: make.info,  Node: Makefile Arguments,  Next: Goals,  Prev: Running,  Up: Running

9.1 Arguments to Specify the Makefile
=====================================

The way to specify the name of the makefile is with the `-f' or
`--file' option (`--makefile' also works).  For example, `-f altmake'
says to use the file `altmake' as the makefile.

   If you use the `-f' flag several times and follow each `-f' with an
argument, all the specified files are used jointly as makefiles.

   If you do not use the `-f' or `--file' flag, the default is to try
`GNUmakefile', `makefile', and `Makefile', in that order, and use the
first of these three which exists or can be made (*note Writing
Makefiles: Makefiles.).


File: make.info,  Node: Goals,  Next: Instead of Execution,  Prev: Makefile Arguments,  Up: Running

9.2 Arguments to Specify the Goals
==================================

The "goals" are the targets that `make' should strive ultimately to
update.  Other targets are updated as well if they appear as
prerequisites of goals, or prerequisites of prerequisites of goals, etc.

   By default, the goal is the first target in the makefile (not
counting targets that start with a period).  Therefore, makefiles are
usually written so that the first target is for compiling the entire
program or programs they describe.  If the first rule in the makefile
has several targets, only the first target in the rule becomes the
default goal, not the whole list.  You can manage the selection of the
default goal from within your makefile using the `.DEFAULT_GOAL'
variable (*note Other Special Variables: Special Variables.).

   You can also specify a different goal or goals with command-line
arguments to `make'.  Use the name of the goal as an argument.  If you
specify several goals, `make' processes each of them in turn, in the
order you name them.

   Any target in the makefile may be specified as a goal (unless it
starts with `-' or contains an `=', in which case it will be parsed as
a switch or variable definition, respectively).  Even targets not in
the makefile may be specified, if `make' can find implicit rules that
say how to make them.

   `Make' will set the special variable `MAKECMDGOALS' to the list of
goals you specified on the command line.  If no goals were given on the
command line, this variable is empty.  Note that this variable should
be used only in special circumstances.

   An example of appropriate use is to avoid including `.d' files
during `clean' rules (*note Automatic Prerequisites::), so `make' won't
create them only to immediately remove them again:

     sources = foo.c bar.c

     ifneq ($(MAKECMDGOALS),clean)
     include $(sources:.c=.d)
     endif

   One use of specifying a goal is if you want to compile only a part of
the program, or only one of several programs.  Specify as a goal each
file that you wish to remake.  For example, consider a directory
containing several programs, with a makefile that starts like this:

     .PHONY: all
     all: size nm ld ar as

   If you are working on the program `size', you might want to say
`make size' so that only the files of that program are recompiled.

   Another use of specifying a goal is to make files that are not
normally made.  For example, there may be a file of debugging output,
or a version of the program that is compiled specially for testing,
which has a rule in the makefile but is not a prerequisite of the
default goal.

   Another use of specifying a goal is to run the commands associated
with a phony target (*note Phony Targets::) or empty target (*note
Empty Target Files to Record Events: Empty Targets.).  Many makefiles
contain a phony target named `clean' which deletes everything except
source files.  Naturally, this is done only if you request it
explicitly with `make clean'.  Following is a list of typical phony and
empty target names.  *Note Standard Targets::, for a detailed list of
all the standard target names which GNU software packages use.

`all'
     Make all the top-level targets the makefile knows about.

`clean'
     Delete all files that are normally created by running `make'.

`mostlyclean'
     Like `clean', but may refrain from deleting a few files that people
     normally don't want to recompile.  For example, the `mostlyclean'
     target for GCC does not delete `libgcc.a', because recompiling it
     is rarely necessary and takes a lot of time.

`distclean'
`realclean'
`clobber'
     Any of these targets might be defined to delete _more_ files than
     `clean' does.  For example, this would delete configuration files
     or links that you would normally create as preparation for
     compilation, even if the makefile itself cannot create these files.

`install'
     Copy the executable file into a directory that users typically
     search for commands; copy any auxiliary files that the executable
     uses into the directories where it will look for them.

`print'
     Print listings of the source files that have changed.

`tar'
     Create a tar file of the source files.

`shar'
     Create a shell archive (shar file) of the source files.

`dist'
     Create a distribution file of the source files.  This might be a
     tar file, or a shar file, or a compressed version of one of the
     above, or even more than one of the above.

`TAGS'
     Update a tags table for this program.

`check'
`test'
     Perform self tests on the program this makefile builds.


File: make.info,  Node: Instead of Execution,  Next: Avoiding Compilation,  Prev: Goals,  Up: Running

9.3 Instead of Executing the Commands
=====================================

The makefile tells `make' how to tell whether a target is up to date,
and how to update each target.  But updating the targets is not always
what you want.  Certain options specify other activities for `make'.

`-n'
`--just-print'
`--dry-run'
`--recon'
     "No-op".  The activity is to print what commands would be used to
     make the targets up to date, but not actually execute them.

`-t'
`--touch'
     "Touch".  The activity is to mark the targets as up to date without
     actually changing them.  In other words, `make' pretends to compile
     the targets but does not really change their contents.

`-q'
`--question'
     "Question".  The activity is to find out silently whether the
     targets are up to date already; but execute no commands in either
     case.  In other words, neither compilation nor output will occur.

`-W FILE'
`--what-if=FILE'
`--assume-new=FILE'
`--new-file=FILE'
     "What if".  Each `-W' flag is followed by a file name.  The given
     files' modification times are recorded by `make' as being the
     present time, although the actual modification times remain the
     same.  You can use the `-W' flag in conjunction with the `-n' flag
     to see what would happen if you were to modify specific files.

   With the `-n' flag, `make' prints the commands that it would
normally execute but does not execute them.

   With the `-t' flag, `make' ignores the commands in the rules and
uses (in effect) the command `touch' for each target that needs to be
remade.  The `touch' command is also printed, unless `-s' or `.SILENT'
is used.  For speed, `make' does not actually invoke the program
`touch'.  It does the work directly.

   With the `-q' flag, `make' prints nothing and executes no commands,
but the exit status code it returns is zero if and only if the targets
to be considered are already up to date.  If the exit status is one,
then some updating needs to be done.  If `make' encounters an error,
the exit status is two, so you can distinguish an error from a target
that is not up to date.

   It is an error to use more than one of these three flags in the same
invocation of `make'.

   The `-n', `-t', and `-q' options do not affect command lines that
begin with `+' characters or contain the strings `$(MAKE)' or
`${MAKE}'.  Note that only the line containing the `+' character or the
strings `$(MAKE)' or `${MAKE}' is run regardless of these options.
Other lines in the same rule are not run unless they too begin with `+'
or contain `$(MAKE)' or `${MAKE}' (*Note How the `MAKE' Variable Works:
MAKE Variable.)

   The `-W' flag provides two features:

   * If you also use the `-n' or `-q' flag, you can see what `make'
     would do if you were to modify some files.

   * Without the `-n' or `-q' flag, when `make' is actually executing
     commands, the `-W' flag can direct `make' to act as if some files
     had been modified, without actually modifying the files.

   Note that the options `-p' and `-v' allow you to obtain other
information about `make' or about the makefiles in use (*note Summary
of Options: Options Summary.).


File: make.info,  Node: Avoiding Compilation,  Next: Overriding,  Prev: Instead of Execution,  Up: Running

9.4 Avoiding Recompilation of Some Files
========================================

Sometimes you may have changed a source file but you do not want to
recompile all the files that depend on it.  For example, suppose you add
a macro or a declaration to a header file that many other files depend
on.  Being conservative, `make' assumes that any change in the header
file requires recompilation of all dependent files, but you know that
they do not need to be recompiled and you would rather not waste the
time waiting for them to compile.

   If you anticipate the problem before changing the header file, you
can use the `-t' flag.  This flag tells `make' not to run the commands
in the rules, but rather to mark the target up to date by changing its
last-modification date.  You would follow this procedure:

  1. Use the command `make' to recompile the source files that really
     need recompilation, ensuring that the object files are up-to-date
     before you begin.

  2. Make the changes in the header files.

  3. Use the command `make -t' to mark all the object files as up to
     date.  The next time you run `make', the changes in the header
     files will not cause any recompilation.

   If you have already changed the header file at a time when some files
do need recompilation, it is too late to do this.  Instead, you can use
the `-o FILE' flag, which marks a specified file as "old" (*note
Summary of Options: Options Summary.).  This means that the file itself
will not be remade, and nothing else will be remade on its account.
Follow this procedure:

  1. Recompile the source files that need compilation for reasons
     independent of the particular header file, with `make -o
     HEADERFILE'.  If several header files are involved, use a separate
     `-o' option for each header file.

  2. Touch all the object files with `make -t'.


File: make.info,  Node: Overriding,  Next: Testing,  Prev: Avoiding Compilation,  Up: Running

9.5 Overriding Variables
========================

An argument that contains `=' specifies the value of a variable: `V=X'
sets the value of the variable V to X.  If you specify a value in this
way, all ordinary assignments of the same variable in the makefile are
ignored; we say they have been "overridden" by the command line
argument.

   The most common way to use this facility is to pass extra flags to
compilers.  For example, in a properly written makefile, the variable
`CFLAGS' is included in each command that runs the C compiler, so a
file `foo.c' would be compiled something like this:

     cc -c $(CFLAGS) foo.c

   Thus, whatever value you set for `CFLAGS' affects each compilation
that occurs.  The makefile probably specifies the usual value for
`CFLAGS', like this:

     CFLAGS=-g

   Each time you run `make', you can override this value if you wish.
For example, if you say `make CFLAGS='-g -O'', each C compilation will
be done with `cc -c -g -O'.  (This also illustrates how you can use
quoting in the shell to enclose spaces and other special characters in
the value of a variable when you override it.)

   The variable `CFLAGS' is only one of many standard variables that
exist just so that you can change them this way.  *Note Variables Used
by Implicit Rules: Implicit Variables, for a complete list.

   You can also program the makefile to look at additional variables of
your own, giving the user the ability to control other aspects of how
the makefile works by changing the variables.

   When you override a variable with a command argument, you can define
either a recursively-expanded variable or a simply-expanded variable.
The examples shown above make a recursively-expanded variable; to make a
simply-expanded variable, write `:=' instead of `='.  But, unless you
want to include a variable reference or function call in the _value_
that you specify, it makes no difference which kind of variable you
create.

   There is one way that the makefile can change a variable that you
have overridden.  This is to use the `override' directive, which is a
line that looks like this: `override VARIABLE = VALUE' (*note The
`override' Directive: Override Directive.).


File: make.info,  Node: Testing,  Next: Options Summary,  Prev: Overriding,  Up: Running

9.6 Testing the Compilation of a Program
========================================

Normally, when an error happens in executing a shell command, `make'
gives up immediately, returning a nonzero status.  No further commands
are executed for any target.  The error implies that the goal cannot be
correctly remade, and `make' reports this as soon as it knows.

   When you are compiling a program that you have just changed, this is
not what you want.  Instead, you would rather that `make' try compiling
every file that can be tried, to show you as many compilation errors as
possible.

   On these occasions, you should use the `-k' or `--keep-going' flag.
This tells `make' to continue to consider the other prerequisites of
the pending targets, remaking them if necessary, before it gives up and
returns nonzero status.  For example, after an error in compiling one
object file, `make -k' will continue compiling other object files even
though it already knows that linking them will be impossible.  In
addition to continuing after failed shell commands, `make -k' will
continue as much as possible after discovering that it does not know
how to make a target or prerequisite file.  This will always cause an
error message, but without `-k', it is a fatal error (*note Summary of
Options: Options Summary.).

   The usual behavior of `make' assumes that your purpose is to get the
goals up to date; once `make' learns that this is impossible, it might
as well report the failure immediately.  The `-k' flag says that the
real purpose is to test as much as possible of the changes made in the
program, perhaps to find several independent problems so that you can
correct them all before the next attempt to compile.  This is why Emacs'
`M-x compile' command passes the `-k' flag by default.


File: make.info,  Node: Options Summary,  Prev: Testing,  Up: Running

9.7 Summary of Options
======================

Here is a table of all the options `make' understands:

`-b'
`-m'
     These options are ignored for compatibility with other versions of
     `make'.

`-B'
`--always-make'
     Consider all targets out-of-date.  GNU `make' proceeds to consider
     targets and their prerequisites using the normal algorithms;
     however, all targets so considered are always remade regardless of
     the status of their prerequisites.  To avoid infinite recursion, if
     `MAKE_RESTARTS' (*note Other Special Variables: Special
     Variables.) is set to a number greater than 0 this option is
     disabled when considering whether to remake makefiles (*note How
     Makefiles Are Remade: Remaking Makefiles.).

`-C DIR'
`--directory=DIR'
     Change to directory DIR before reading the makefiles.  If multiple
     `-C' options are specified, each is interpreted relative to the
     previous one: `-C / -C etc' is equivalent to `-C /etc'.  This is
     typically used with recursive invocations of `make' (*note
     Recursive Use of `make': Recursion.).

`-d'
     Print debugging information in addition to normal processing.  The
     debugging information says which files are being considered for
     remaking, which file-times are being compared and with what
     results, which files actually need to be remade, which implicit
     rules are considered and which are applied--everything interesting
     about how `make' decides what to do.  The `-d' option is
     equivalent to `--debug=a' (see below).

`--debug[=OPTIONS]'
     Print debugging information in addition to normal processing.
     Various levels and types of output can be chosen.  With no
     arguments, print the "basic" level of debugging.  Possible
     arguments are below; only the first character is considered, and
     values must be comma- or space-separated.

    `a (all)'
          All types of debugging output are enabled.  This is
          equivalent to using `-d'.

    `b (basic)'
          Basic debugging prints each target that was found to be
          out-of-date, and whether the build was successful or not.

    `v (verbose)'
          A level above `basic'; includes messages about which
          makefiles were parsed, prerequisites that did not need to be
          rebuilt, etc.  This option also enables `basic' messages.

    `i (implicit)'
          Prints messages describing the implicit rule searches for
          each target.  This option also enables `basic' messages.

    `j (jobs)'
          Prints messages giving details on the invocation of specific
          subcommands.

    `m (makefile)'
          By default, the above messages are not enabled while trying
          to remake the makefiles.  This option enables messages while
          rebuilding makefiles, too.  Note that the `all' option does
          enable this option.  This option also enables `basic'
          messages.

`-e'
`--environment-overrides'
     Give variables taken from the environment precedence over
     variables from makefiles.  *Note Variables from the Environment:
     Environment.

`-f FILE'
`--file=FILE'
`--makefile=FILE'
     Read the file named FILE as a makefile.  *Note Writing Makefiles:
     Makefiles.

`-h'
`--help'
     Remind you of the options that `make' understands and then exit.

`-i'
`--ignore-errors'
     Ignore all errors in commands executed to remake files.  *Note
     Errors in Commands: Errors.

`-I DIR'
`--include-dir=DIR'
     Specifies a directory DIR to search for included makefiles.  *Note
     Including Other Makefiles: Include.  If several `-I' options are
     used to specify several directories, the directories are searched
     in the order specified.

`-j [JOBS]'
`--jobs[=JOBS]'
     Specifies the number of jobs (commands) to run simultaneously.
     With no argument, `make' runs as many jobs simultaneously as
     possible.  If there is more than one `-j' option, the last one is
     effective.  *Note Parallel Execution: Parallel, for more
     information on how commands are run.  Note that this option is
     ignored on MS-DOS.

`-k'
`--keep-going'
     Continue as much as possible after an error.  While the target that
     failed, and those that depend on it, cannot be remade, the other
     prerequisites of these targets can be processed all the same.
     *Note Testing the Compilation of a Program: Testing.

`-l [LOAD]'
`--load-average[=LOAD]'
`--max-load[=LOAD]'
     Specifies that no new jobs (commands) should be started if there
     are other jobs running and the load average is at least LOAD (a
     floating-point number).  With no argument, removes a previous load
     limit.  *Note Parallel Execution: Parallel.

`-L'
`--check-symlink-times'
     On systems that support symbolic links, this option causes `make'
     to consider the timestamps on any symbolic links in addition to the
     timestamp on the file referenced by those links.  When this option
     is provided, the most recent timestamp among the file and the
     symbolic links is taken as the modification time for this target
     file.

`-n'
`--just-print'
`--dry-run'
`--recon'
     Print the commands that would be executed, but do not execute them.
     *Note Instead of Executing the Commands: Instead of Execution.

`-o FILE'
`--old-file=FILE'
`--assume-old=FILE'
     Do not remake the file FILE even if it is older than its
     prerequisites, and do not remake anything on account of changes in
     FILE.  Essentially the file is treated as very old and its rules
     are ignored.  *Note Avoiding Recompilation of Some Files: Avoiding
     Compilation.

`-p'
`--print-data-base'
     Print the data base (rules and variable values) that results from
     reading the makefiles; then execute as usual or as otherwise
     specified.  This also prints the version information given by the
     `-v' switch (see below).  To print the data base without trying to
     remake any files, use `make -qp'.  To print the data base of
     predefined rules and variables, use `make -p -f /dev/null'.  The
     data base output contains filename and linenumber information for
     command and variable definitions, so it can be a useful debugging
     tool in complex environments.

`-q'
`--question'
     "Question mode".  Do not run any commands, or print anything; just
     return an exit status that is zero if the specified targets are
     already up to date, one if any remaking is required, or two if an
     error is encountered.  *Note Instead of Executing the Commands:
     Instead of Execution.

`-r'
`--no-builtin-rules'
     Eliminate use of the built-in implicit rules (*note Using Implicit
     Rules: Implicit Rules.).  You can still define your own by writing
     pattern rules (*note Defining and Redefining Pattern Rules:
     Pattern Rules.).  The `-r' option also clears out the default list
     of suffixes for suffix rules (*note Old-Fashioned Suffix Rules:
     Suffix Rules.).  But you can still define your own suffixes with a
     rule for `.SUFFIXES', and then define your own suffix rules.  Note
     that only _rules_ are affected by the `-r' option; default
     variables remain in effect (*note Variables Used by Implicit
     Rules: Implicit Variables.); see the `-R' option below.

`-R'
`--no-builtin-variables'
     Eliminate use of the built-in rule-specific variables (*note
     Variables Used by Implicit Rules: Implicit Variables.).  You can
     still define your own, of course.  The `-R' option also
     automatically enables the `-r' option (see above), since it
     doesn't make sense to have implicit rules without any definitions
     for the variables that they use.

`-s'
`--silent'
`--quiet'
     Silent operation; do not print the commands as they are executed.
     *Note Command Echoing: Echoing.

`-S'
`--no-keep-going'
`--stop'
     Cancel the effect of the `-k' option.  This is never necessary
     except in a recursive `make' where `-k' might be inherited from
     the top-level `make' via `MAKEFLAGS' (*note Recursive Use of
     `make': Recursion.)  or if you set `-k' in `MAKEFLAGS' in your
     environment.

`-t'
`--touch'
     Touch files (mark them up to date without really changing them)
     instead of running their commands.  This is used to pretend that
     the commands were done, in order to fool future invocations of
     `make'.  *Note Instead of Executing the Commands: Instead of
     Execution.

`-v'
`--version'
     Print the version of the `make' program plus a copyright, a list
     of authors, and a notice that there is no warranty; then exit.

`-w'
`--print-directory'
     Print a message containing the working directory both before and
     after executing the makefile.  This may be useful for tracking
     down errors from complicated nests of recursive `make' commands.
     *Note Recursive Use of `make': Recursion.  (In practice, you
     rarely need to specify this option since `make' does it for you;
     see *Note The `--print-directory' Option: -w Option.)

`--no-print-directory'
     Disable printing of the working directory under `-w'.  This option
     is useful when `-w' is turned on automatically, but you do not
     want to see the extra messages.  *Note The `--print-directory'
     Option: -w Option.

`-W FILE'
`--what-if=FILE'
`--new-file=FILE'
`--assume-new=FILE'
     Pretend that the target FILE has just been modified.  When used
     with the `-n' flag, this shows you what would happen if you were
     to modify that file.  Without `-n', it is almost the same as
     running a `touch' command on the given file before running `make',
     except that the modification time is changed only in the
     imagination of `make'.  *Note Instead of Executing the Commands:
     Instead of Execution.

`--warn-undefined-variables'
     Issue a warning message whenever `make' sees a reference to an
     undefined variable.  This can be helpful when you are trying to
     debug makefiles which use variables in complex ways.


File: make.info,  Node: Implicit Rules,  Next: Archives,  Prev: Running,  Up: Top

10 Using Implicit Rules
***********************

Certain standard ways of remaking target files are used very often.  For
example, one customary way to make an object file is from a C source
file using the C compiler, `cc'.

   "Implicit rules" tell `make' how to use customary techniques so that
you do not have to specify them in detail when you want to use them.
For example, there is an implicit rule for C compilation.  File names
determine which implicit rules are run.  For example, C compilation
typically takes a `.c' file and makes a `.o' file.  So `make' applies
the implicit rule for C compilation when it sees this combination of
file name endings.

   A chain of implicit rules can apply in sequence; for example, `make'
will remake a `.o' file from a `.y' file by way of a `.c' file.

   The built-in implicit rules use several variables in their commands
so that, by changing the values of the variables, you can change the
way the implicit rule works.  For example, the variable `CFLAGS'
controls the flags given to the C compiler by the implicit rule for C
compilation.

   You can define your own implicit rules by writing "pattern rules".

   "Suffix rules" are a more limited way to define implicit rules.
Pattern rules are more general and clearer, but suffix rules are
retained for compatibility.

* Menu:

* Using Implicit::              How to use an existing implicit rule
                                  to get the commands for updating a file.
* Catalogue of Rules::          A list of built-in implicit rules.
* Implicit Variables::          How to change what predefined rules do.
* Chained Rules::               How to use a chain of implicit rules.
* Pattern Rules::               How to define new implicit rules.
* Last Resort::                 How to define commands for rules which
                                  cannot find any.
* Suffix Rules::                The old-fashioned style of implicit rule.
* Implicit Rule Search::        The precise algorithm for applying
                                  implicit rules.


File: make.info,  Node: Using Implicit,  Next: Catalogue of Rules,  Prev: Implicit Rules,  Up: Implicit Rules

10.1 Using Implicit Rules
=========================

To allow `make' to find a customary method for updating a target file,
all you have to do is refrain from specifying commands yourself.  Either
write a rule with no command lines, or don't write a rule at all.  Then
`make' will figure out which implicit rule to use based on which kind
of source file exists or can be made.

   For example, suppose the makefile looks like this:

     foo : foo.o bar.o
             cc -o foo foo.o bar.o $(CFLAGS) $(LDFLAGS)

Because you mention `foo.o' but do not give a rule for it, `make' will
automatically look for an implicit rule that tells how to update it.
This happens whether or not the file `foo.o' currently exists.

   If an implicit rule is found, it can supply both commands and one or
more prerequisites (the source files).  You would want to write a rule
for `foo.o' with no command lines if you need to specify additional
prerequisites, such as header files, that the implicit rule cannot
supply.

   Each implicit rule has a target pattern and prerequisite patterns.
There may be many implicit rules with the same target pattern.  For
example, numerous rules make `.o' files: one, from a `.c' file with the
C compiler; another, from a `.p' file with the Pascal compiler; and so
on.  The rule that actually applies is the one whose prerequisites
exist or can be made.  So, if you have a file `foo.c', `make' will run
the C compiler; otherwise, if you have a file `foo.p', `make' will run
the Pascal compiler; and so on.

   Of course, when you write the makefile, you know which implicit rule
you want `make' to use, and you know it will choose that one because you
know which possible prerequisite files are supposed to exist.  *Note
Catalogue of Implicit Rules: Catalogue of Rules, for a catalogue of all
the predefined implicit rules.

   Above, we said an implicit rule applies if the required
prerequisites "exist or can be made".  A file "can be made" if it is
mentioned explicitly in the makefile as a target or a prerequisite, or
if an implicit rule can be recursively found for how to make it.  When
an implicit prerequisite is the result of another implicit rule, we say
that "chaining" is occurring.  *Note Chains of Implicit Rules: Chained
Rules.

   In general, `make' searches for an implicit rule for each target, and
for each double-colon rule, that has no commands.  A file that is
mentioned only as a prerequisite is considered a target whose rule
specifies nothing, so implicit rule search happens for it.  *Note
Implicit Rule Search Algorithm: Implicit Rule Search, for the details
of how the search is done.

   Note that explicit prerequisites do not influence implicit rule
search.  For example, consider this explicit rule:

     foo.o: foo.p

The prerequisite on `foo.p' does not necessarily mean that `make' will
remake `foo.o' according to the implicit rule to make an object file, a
`.o' file, from a Pascal source file, a `.p' file.  For example, if
`foo.c' also exists, the implicit rule to make an object file from a C
source file is used instead, because it appears before the Pascal rule
in the list of predefined implicit rules (*note Catalogue of Implicit
Rules: Catalogue of Rules.).

   If you do not want an implicit rule to be used for a target that has
no commands, you can give that target empty commands by writing a
semicolon (*note Defining Empty Commands: Empty Commands.).


File: make.info,  Node: Catalogue of Rules,  Next: Implicit Variables,  Prev: Using Implicit,  Up: Implicit Rules

10.2 Catalogue of Implicit Rules
================================

Here is a catalogue of predefined implicit rules which are always
available unless the makefile explicitly overrides or cancels them.
*Note Canceling Implicit Rules: Canceling Rules, for information on
canceling or overriding an implicit rule.  The `-r' or
`--no-builtin-rules' option cancels all predefined rules.

   This manual only documents the default rules available on POSIX-based
operating systems.  Other operating systems, such as VMS, Windows,
OS/2, etc. may have different sets of default rules.  To see the full
list of default rules and variables available in your version of GNU
`make', run `make -p' in a directory with no makefile.

   Not all of these rules will always be defined, even when the `-r'
option is not given.  Many of the predefined implicit rules are
implemented in `make' as suffix rules, so which ones will be defined
depends on the "suffix list" (the list of prerequisites of the special
target `.SUFFIXES').  The default suffix list is: `.out', `.a', `.ln',
`.o', `.c', `.cc', `.C', `.cpp', `.p', `.f', `.F', `.r', `.y', `.l',
`.s', `.S', `.mod', `.sym', `.def', `.h', `.info', `.dvi', `.tex',
`.texinfo', `.texi', `.txinfo', `.w', `.ch' `.web', `.sh', `.elc',
`.el'.  All of the implicit rules described below whose prerequisites
have one of these suffixes are actually suffix rules.  If you modify
the suffix list, the only predefined suffix rules in effect will be
those named by one or two of the suffixes that are on the list you
specify; rules whose suffixes fail to be on the list are disabled.
*Note Old-Fashioned Suffix Rules: Suffix Rules, for full details on
suffix rules.

Compiling C programs
     `N.o' is made automatically from `N.c' with a command of the form
     `$(CC) -c $(CPPFLAGS) $(CFLAGS)'.

Compiling C++ programs
     `N.o' is made automatically from `N.cc', `N.cpp', or `N.C' with a
     command of the form `$(CXX) -c $(CPPFLAGS) $(CXXFLAGS)'.  We
     encourage you to use the suffix `.cc' for C++ source files instead
     of `.C'.

Compiling Pascal programs
     `N.o' is made automatically from `N.p' with the command `$(PC) -c
     $(PFLAGS)'.

Compiling Fortran and Ratfor programs
     `N.o' is made automatically from `N.r', `N.F' or `N.f' by running
     the Fortran compiler.  The precise command used is as follows:

    `.f'
          `$(FC) -c $(FFLAGS)'.

    `.F'
          `$(FC) -c $(FFLAGS) $(CPPFLAGS)'.

    `.r'
          `$(FC) -c $(FFLAGS) $(RFLAGS)'.

Preprocessing Fortran and Ratfor programs
     `N.f' is made automatically from `N.r' or `N.F'.  This rule runs
     just the preprocessor to convert a Ratfor or preprocessable
     Fortran program into a strict Fortran program.  The precise
     command used is as follows:

    `.F'
          `$(FC) -F $(CPPFLAGS) $(FFLAGS)'.

    `.r'
          `$(FC) -F $(FFLAGS) $(RFLAGS)'.

Compiling Modula-2 programs
     `N.sym' is made from `N.def' with a command of the form `$(M2C)
     $(M2FLAGS) $(DEFFLAGS)'.  `N.o' is made from `N.mod'; the form is:
     `$(M2C) $(M2FLAGS) $(MODFLAGS)'.

Assembling and preprocessing assembler programs
     `N.o' is made automatically from `N.s' by running the assembler,
     `as'.  The precise command is `$(AS) $(ASFLAGS)'.

     `N.s' is made automatically from `N.S' by running the C
     preprocessor, `cpp'.  The precise command is `$(CPP) $(CPPFLAGS)'.

Linking a single object file
     `N' is made automatically from `N.o' by running the linker
     (usually called `ld') via the C compiler.  The precise command
     used is `$(CC) $(LDFLAGS) N.o $(LOADLIBES) $(LDLIBS)'.

     This rule does the right thing for a simple program with only one
     source file.  It will also do the right thing if there are multiple
     object files (presumably coming from various other source files),
     one of which has a name matching that of the executable file.
     Thus,

          x: y.o z.o

     when `x.c', `y.c' and `z.c' all exist will execute:

          cc -c x.c -o x.o
          cc -c y.c -o y.o
          cc -c z.c -o z.o
          cc x.o y.o z.o -o x
          rm -f x.o
          rm -f y.o
          rm -f z.o

     In more complicated cases, such as when there is no object file
     whose name derives from the executable file name, you must write
     an explicit command for linking.

     Each kind of file automatically made into `.o' object files will
     be automatically linked by using the compiler (`$(CC)', `$(FC)' or
     `$(PC)'; the C compiler `$(CC)' is used to assemble `.s' files)
     without the `-c' option.  This could be done by using the `.o'
     object files as intermediates, but it is faster to do the
     compiling and linking in one step, so that's how it's done.

Yacc for C programs
     `N.c' is made automatically from `N.y' by running Yacc with the
     command `$(YACC) $(YFLAGS)'.

Lex for C programs
     `N.c' is made automatically from `N.l' by running Lex.  The actual
     command is `$(LEX) $(LFLAGS)'.

Lex for Ratfor programs
     `N.r' is made automatically from `N.l' by running Lex.  The actual
     command is `$(LEX) $(LFLAGS)'.

     The convention of using the same suffix `.l' for all Lex files
     regardless of whether they produce C code or Ratfor code makes it
     impossible for `make' to determine automatically which of the two
     languages you are using in any particular case.  If `make' is
     called upon to remake an object file from a `.l' file, it must
     guess which compiler to use.  It will guess the C compiler, because
     that is more common.  If you are using Ratfor, make sure `make'
     knows this by mentioning `N.r' in the makefile.  Or, if you are
     using Ratfor exclusively, with no C files, remove `.c' from the
     list of implicit rule suffixes with:

          .SUFFIXES:
          .SUFFIXES: .o .r .f .l ...

Making Lint Libraries from C, Yacc, or Lex programs
     `N.ln' is made from `N.c' by running `lint'.  The precise command
     is `$(LINT) $(LINTFLAGS) $(CPPFLAGS) -i'.  The same command is
     used on the C code produced from `N.y' or `N.l'.

TeX and Web
     `N.dvi' is made from `N.tex' with the command `$(TEX)'.  `N.tex'
     is made from `N.web' with `$(WEAVE)', or from `N.w' (and from
     `N.ch' if it exists or can be made) with `$(CWEAVE)'.  `N.p' is
     made from `N.web' with `$(TANGLE)' and `N.c' is made from `N.w'
     (and from `N.ch' if it exists or can be made) with `$(CTANGLE)'.

Texinfo and Info
     `N.dvi' is made from `N.texinfo', `N.texi', or `N.txinfo', with
     the command `$(TEXI2DVI) $(TEXI2DVI_FLAGS)'.  `N.info' is made from
     `N.texinfo', `N.texi', or `N.txinfo', with the command
     `$(MAKEINFO) $(MAKEINFO_FLAGS)'.

RCS
     Any file `N' is extracted if necessary from an RCS file named
     either `N,v' or `RCS/N,v'.  The precise command used is
     `$(CO) $(COFLAGS)'.  `N' will not be extracted from RCS if it
     already exists, even if the RCS file is newer.  The rules for RCS
     are terminal (*note Match-Anything Pattern Rules: Match-Anything
     Rules.), so RCS files cannot be generated from another source;
     they must actually exist.

SCCS
     Any file `N' is extracted if necessary from an SCCS file named
     either `s.N' or `SCCS/s.N'.  The precise command used is
     `$(GET) $(GFLAGS)'.  The rules for SCCS are terminal (*note
     Match-Anything Pattern Rules: Match-Anything Rules.), so SCCS
     files cannot be generated from another source; they must actually
     exist.

     For the benefit of SCCS, a file `N' is copied from `N.sh' and made
     executable (by everyone).  This is for shell scripts that are
     checked into SCCS.  Since RCS preserves the execution permission
     of a file, you do not need to use this feature with RCS.

     We recommend that you avoid using of SCCS.  RCS is widely held to
     be superior, and is also free.  By choosing free software in place
     of comparable (or inferior) proprietary software, you support the
     free software movement.

   Usually, you want to change only the variables listed in the table
above, which are documented in the following section.

   However, the commands in built-in implicit rules actually use
variables such as `COMPILE.c', `LINK.p', and `PREPROCESS.S', whose
values contain the commands listed above.

   `make' follows the convention that the rule to compile a `.X' source
file uses the variable `COMPILE.X'.  Similarly, the rule to produce an
executable from a `.X' file uses `LINK.X'; and the rule to preprocess a
`.X' file uses `PREPROCESS.X'.

   Every rule that produces an object file uses the variable
`OUTPUT_OPTION'.  `make' defines this variable either to contain `-o
$@', or to be empty, depending on a compile-time option.  You need the
`-o' option to ensure that the output goes into the right file when the
source file is in a different directory, as when using `VPATH' (*note
Directory Search::).  However, compilers on some systems do not accept
a `-o' switch for object files.  If you use such a system, and use
`VPATH', some compilations will put their output in the wrong place.  A
possible workaround for this problem is to give `OUTPUT_OPTION' the
value `; mv $*.o $@'.


File: make.info,  Node: Implicit Variables,  Next: Chained Rules,  Prev: Catalogue of Rules,  Up: Implicit Rules

10.3 Variables Used by Implicit Rules
=====================================

The commands in built-in implicit rules make liberal use of certain
predefined variables.  You can alter the values of these variables in
the makefile, with arguments to `make', or in the environment to alter
how the implicit rules work without redefining the rules themselves.
You can cancel all variables used by implicit rules with the `-R' or
`--no-builtin-variables' option.

   For example, the command used to compile a C source file actually
says `$(CC) -c $(CFLAGS) $(CPPFLAGS)'.  The default values of the
variables used are `cc' and nothing, resulting in the command `cc -c'.
By redefining `CC' to `ncc', you could cause `ncc' to be used for all C
compilations performed by the implicit rule.  By redefining `CFLAGS' to
be `-g', you could pass the `-g' option to each compilation.  _All_
implicit rules that do C compilation use `$(CC)' to get the program
name for the compiler and _all_ include `$(CFLAGS)' among the arguments
given to the compiler.

   The variables used in implicit rules fall into two classes: those
that are names of programs (like `CC') and those that contain arguments
for the programs (like `CFLAGS').  (The "name of a program" may also
contain some command arguments, but it must start with an actual
executable program name.)  If a variable value contains more than one
argument, separate them with spaces.

   The following tables describe of some of the more commonly-used
predefined variables.  This list is not exhaustive, and the default
values shown here may not be what are selected by `make' for your
environment.  To see the complete list of predefined variables for your
instance of GNU `make' you can run `make -p' in a directory with no
makefiles.

   Here is a table of some of the more common variables used as names of
programs in built-in rules: makefiles.

`AR'
     Archive-maintaining program; default `ar'.  

`AS'
     Program for compiling assembly files; default `as'.  

`CC'
     Program for compiling C programs; default `cc'.  

`CO'
     Program for checking out files from RCS; default `co'.  

`CXX'
     Program for compiling C++ programs; default `g++'.  

`CO'
     Program for extracting a file from RCS; default `co'.  

`CPP'
     Program for running the C preprocessor, with results to standard
     output; default `$(CC) -E'.

`FC'
     Program for compiling or preprocessing Fortran and Ratfor programs;
     default `f77'.  

`GET'
     Program for extracting a file from SCCS; default `get'.  

`LEX'
     Program to use to turn Lex grammars into source code; default
     `lex'.  

`YACC'
     Program to use to turn Yacc grammars into source code; default
     `yacc'.  

`LINT'
     Program to use to run lint on source code; default `lint'.  

`M2C'
     Program to use to compile Modula-2 source code; default `m2c'.  

`PC'
     Program for compiling Pascal programs; default `pc'.  

`MAKEINFO'
     Program to convert a Texinfo source file into an Info file; default
     `makeinfo'.  

`TEX'
     Program to make TeX DVI files from TeX source; default `tex'.  

`TEXI2DVI'
     Program to make TeX DVI files from Texinfo source; default
     `texi2dvi'.  

`WEAVE'
     Program to translate Web into TeX; default `weave'.  

`CWEAVE'
     Program to translate C Web into TeX; default `cweave'.  

`TANGLE'
     Program to translate Web into Pascal; default `tangle'.  

`CTANGLE'
     Program to translate C Web into C; default `ctangle'.  

`RM'
     Command to remove a file; default `rm -f'.  

   Here is a table of variables whose values are additional arguments
for the programs above.  The default values for all of these is the
empty string, unless otherwise noted.

`ARFLAGS'
     Flags to give the archive-maintaining program; default `rv'.

`ASFLAGS'
     Extra flags to give to the assembler (when explicitly invoked on a
     `.s' or `.S' file).

`CFLAGS'
     Extra flags to give to the C compiler.

`CXXFLAGS'
     Extra flags to give to the C++ compiler.

`COFLAGS'
     Extra flags to give to the RCS `co' program.

`CPPFLAGS'
     Extra flags to give to the C preprocessor and programs that use it
     (the C and Fortran compilers).

`FFLAGS'
     Extra flags to give to the Fortran compiler.

`GFLAGS'
     Extra flags to give to the SCCS `get' program.

`LDFLAGS'
     Extra flags to give to compilers when they are supposed to invoke
     the linker, `ld'.

`LFLAGS'
     Extra flags to give to Lex.

`YFLAGS'
     Extra flags to give to Yacc.

`PFLAGS'
     Extra flags to give to the Pascal compiler.

`RFLAGS'
     Extra flags to give to the Fortran compiler for Ratfor programs.

`LINTFLAGS'
     Extra flags to give to lint.


File: make.info,  Node: Chained Rules,  Next: Pattern Rules,  Prev: Implicit Variables,  Up: Implicit Rules

10.4 Chains of Implicit Rules
=============================

Sometimes a file can be made by a sequence of implicit rules.  For
example, a file `N.o' could be made from `N.y' by running first Yacc
and then `cc'.  Such a sequence is called a "chain".

   If the file `N.c' exists, or is mentioned in the makefile, no
special searching is required: `make' finds that the object file can be
made by C compilation from `N.c'; later on, when considering how to
make `N.c', the rule for running Yacc is used.  Ultimately both `N.c'
and `N.o' are updated.

   However, even if `N.c' does not exist and is not mentioned, `make'
knows how to envision it as the missing link between `N.o' and `N.y'!
In this case, `N.c' is called an "intermediate file".  Once `make' has
decided to use the intermediate file, it is entered in the data base as
if it had been mentioned in the makefile, along with the implicit rule
that says how to create it.

   Intermediate files are remade using their rules just like all other
files.  But intermediate files are treated differently in two ways.

   The first difference is what happens if the intermediate file does
not exist.  If an ordinary file B does not exist, and `make' considers
a target that depends on B, it invariably creates B and then updates
the target from B.  But if B is an intermediate file, then `make' can
leave well enough alone.  It won't bother updating B, or the ultimate
target, unless some prerequisite of B is newer than that target or
there is some other reason to update that target.

   The second difference is that if `make' _does_ create B in order to
update something else, it deletes B later on after it is no longer
needed.  Therefore, an intermediate file which did not exist before
`make' also does not exist after `make'.  `make' reports the deletion
to you by printing a `rm -f' command showing which file it is deleting.

   Ordinarily, a file cannot be intermediate if it is mentioned in the
makefile as a target or prerequisite.  However, you can explicitly mark
a file as intermediate by listing it as a prerequisite of the special
target `.INTERMEDIATE'.  This takes effect even if the file is mentioned
explicitly in some other way.

   You can prevent automatic deletion of an intermediate file by
marking it as a "secondary" file.  To do this, list it as a
prerequisite of the special target `.SECONDARY'.  When a file is
secondary, `make' will not create the file merely because it does not
already exist, but `make' does not automatically delete the file.
Marking a file as secondary also marks it as intermediate.

   You can list the target pattern of an implicit rule (such as `%.o')
as a prerequisite of the special target `.PRECIOUS' to preserve
intermediate files made by implicit rules whose target patterns match
that file's name; see *Note Interrupts::.  

   A chain can involve more than two implicit rules.  For example, it is
possible to make a file `foo' from `RCS/foo.y,v' by running RCS, Yacc
and `cc'.  Then both `foo.y' and `foo.c' are intermediate files that
are deleted at the end.

   No single implicit rule can appear more than once in a chain.  This
means that `make' will not even consider such a ridiculous thing as
making `foo' from `foo.o.o' by running the linker twice.  This
constraint has the added benefit of preventing any infinite loop in the
search for an implicit rule chain.

   There are some special implicit rules to optimize certain cases that
would otherwise be handled by rule chains.  For example, making `foo'
from `foo.c' could be handled by compiling and linking with separate
chained rules, using `foo.o' as an intermediate file.  But what
actually happens is that a special rule for this case does the
compilation and linking with a single `cc' command.  The optimized rule
is used in preference to the step-by-step chain because it comes
earlier in the ordering of rules.

