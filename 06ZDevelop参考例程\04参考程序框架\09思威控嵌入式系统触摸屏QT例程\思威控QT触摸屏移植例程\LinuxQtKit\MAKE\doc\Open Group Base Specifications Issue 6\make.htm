<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<head>
<meta name="generator" content="HTML Tidy, see www.w3.org">
<meta http-equiv="Content-Type" content="text/html; charset=iso-8859-1">
<link type="text/css" rel="stylesheet" href="style.css">
<!-- Generated by The Open Group's rhtm tool v1.2.1 -->
<!-- Copyright (c) 2001-2004 IEEE and The Open Group, All Rights Reserved -->
<title>make</title>
</head>
<body bgcolor="white">
<script type="text/javascript" language="JavaScript" src="../jscript/codes.js">
</script>

<basefont size="3"> <a name="make"></a> <a name="tag_04_84"></a><!-- make -->
 <!--header start-->
<center><font size="2">The Open Group Base Specifications Issue 6<br>
IEEE Std 1003.1, 2004 Edition<br>
Copyright &copy; 2001-2004 The IEEE and The Open Group, All Rights reserved.</font></center>

<!--header end-->
<hr size="2" noshade>
<h4><a name="tag_04_84_01"></a>NAME</h4>

<blockquote>make - maintain, update, and regenerate groups of programs (<b>DEVELOPMENT</b>)</blockquote>

<h4><a name="tag_04_84_02"></a>SYNOPSIS</h4>

<blockquote class="synopsis">
<div class="box"><code><tt><sup>[<a href="javascript:open_code('SD')">SD</a>]</sup> <img src="../images/opt-start.gif" alt=
"[Option Start]" border="0"> make</tt> <b>[</b><tt>-einpqrst</tt><b>][</b><tt>-f</tt> <i>makefile</i><b>]</b><tt>...</tt><b>[</b>
<tt>-k| -S</tt><b>][</b><i>macro</i><tt>=</tt><i>value</i><b>]</b><tt>...<br>
 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</tt> <b>[</b><i>target_name</i><tt>...</tt><b>]</b><tt><img src="../images/opt-end.gif" alt=
"[Option End]" border="0"></tt></code></div>
</blockquote>

<h4><a name="tag_04_84_03"></a>DESCRIPTION</h4>

<blockquote>
<p>The <i>make</i> utility shall update files that are derived from other files. A typical case is one where object files are
derived from the corresponding source files. The <i>make</i> utility examines time relationships and shall update those derived
files (called targets) that have modified times earlier than the modified times of the files (called prerequisites) from which they
are derived. A description file (makefile) contains a description of the relationships between files, and the commands that need to
be executed to update the targets to reflect changes in their prerequisites. Each specification, or rule, shall consist of a
target, optional prerequisites, and optional commands to be executed when a prerequisite is newer than the target. There are two
types of rule:</p>

<ol>
<li>
<p><i>Inference rules</i>, which have one target name with at least one period ( <tt>'.'</tt> ) and no slash ( <tt>'/'</tt> )</p>
</li>

<li>
<p><i>Target rules</i>, which can have more than one target name</p>
</li>
</ol>

<p>In addition, <i>make</i> shall have a collection of built-in macros and inference rules that infer prerequisite relationships to
simplify maintenance of programs.</p>

<p>To receive exactly the behavior described in this section, the user shall ensure that a portable makefile shall:</p>

<ul>
<li>
<p>Include the special target <b>.POSIX</b></p>
</li>

<li>
<p>Omit any special target reserved for implementations (a leading period followed by uppercase letters) that has not been
specified by this section</p>
</li>
</ul>

<p>The behavior of <i>make</i> is unspecified if either or both of these conditions are not met.</p>
</blockquote>

<h4><a name="tag_04_84_04"></a>OPTIONS</h4>

<blockquote>
<p>The <i>make</i> utility shall conform to the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001, <a href=
"../basedefs/xbd_chap12.html#tag_12_02">Section 12.2, Utility Syntax Guidelines</a>.</p>

<p>The following options shall be supported:</p>

<dl compact>
<dt><b>-e</b></dt>

<dd>Cause environment variables, including those with null values, to override macro assignments within makefiles.</dd>

<dt><b>-f&nbsp;</b> <i>makefile</i></dt>

<dd>Specify a different makefile. The argument <i>makefile</i> is a pathname of a description file, which is also referred to as
the <i>makefile</i>. A pathname of <tt>'-'</tt> shall denote the standard input. There can be multiple instances of this option,
and they shall be processed in the order specified. The effect of specifying the same option-argument more than once is
unspecified.</dd>

<dt><b>-i</b></dt>

<dd>Ignore error codes returned by invoked commands. This mode is the same as if the special target <b>.IGNORE</b> were specified
without prerequisites.</dd>

<dt><b>-k</b></dt>

<dd>Continue to update other targets that do not depend on the current target if a non-ignored error occurs while executing the
commands to bring a target up-to-date.</dd>

<dt><b>-n</b></dt>

<dd>Write commands that would be executed on standard output, but do not execute them. However, lines with a plus sign (
<tt>'+'</tt> ) prefix shall be executed. In this mode, lines with an at sign ( <tt>'@'</tt> ) character prefix shall be written to
standard output.</dd>

<dt><b>-p</b></dt>

<dd>Write to standard output the complete set of macro definitions and target descriptions. The output format is unspecified.</dd>

<dt><b>-q</b></dt>

<dd>Return a zero exit value if the target file is up-to-date; otherwise, return an exit value of 1. Targets shall not be updated
if this option is specified. However, a makefile command line (associated with the targets) with a plus sign ( <tt>'+'</tt> )
prefix shall be executed.</dd>

<dt><b>-r</b></dt>

<dd>Clear the suffix list and do not use the built-in rules.</dd>

<dt><b>-S</b></dt>

<dd>Terminate <i>make</i> if an error occurs while executing the commands to bring a target up-to-date. This shall be the default
and the opposite of <b>-k</b>.</dd>

<dt><b>-s</b></dt>

<dd>Do not write makefile command lines or touch messages (see <b>-t</b>) to standard output before executing. This mode shall be
the same as if the special target <b>.SILENT</b> were specified without prerequisites.</dd>

<dt><b>-t</b></dt>

<dd>Update the modification time of each target as though a <a href="../utilities/touch.html"><i>touch</i></a> <i>target</i> had
been executed. Targets that have prerequisites but no commands (see <a href="#tag_04_84_13_03">Target Rules</a>), or that are
already up-to-date, shall not be touched in this manner. Write messages to standard output for each target file indicating the name
of the file and that it was touched. Normally, the <i>makefile</i> command lines associated with each target are not executed.
However, a command line with a plus sign ( <tt>'+'</tt> ) prefix shall be executed.</dd>
</dl>

<p>Any options specified in the <i>MAKEFLAGS</i> environment variable shall be evaluated before any options specified on the
<i>make</i> utility command line. If the <b>-k</b> and <b>-S</b> options are both specified on the <i>make</i> utility command line
or by the <i>MAKEFLAGS</i> environment variable, the last option specified shall take precedence. If the <b>-f</b> or <b>-p</b>
options appear in the <i>MAKEFLAGS</i> environment variable, the result is undefined.</p>
</blockquote>

<h4><a name="tag_04_84_05"></a>OPERANDS</h4>

<blockquote>
<p>The following operands shall be supported:</p>

<dl compact>
<dt><i>target_name</i></dt>

<dd>Target names, as defined in the EXTENDED DESCRIPTION section. If no target is specified, while <i>make</i> is processing the
makefiles, the first target that <i>make</i> encounters that is not a special target or an inference rule shall be used.</dd>

<dt><i>macro</i>=<i>value</i></dt>

<dd>Macro definitions, as defined in <a href="#tag_04_84_13_04">Macros</a>.</dd>
</dl>

<p>If the <i>target_name</i> and <i>macro</i>= <i>value</i> operands are intermixed on the <i>make</i> utility command line, the
results are unspecified.</p>
</blockquote>

<h4><a name="tag_04_84_06"></a>STDIN</h4>

<blockquote>
<p>The standard input shall be used only if the <i>makefile</i> option-argument is <tt>'-'</tt>. See the INPUT FILES section.</p>
</blockquote>

<h4><a name="tag_04_84_07"></a>INPUT FILES</h4>

<blockquote>
<p>The input file, otherwise known as the makefile, is a text file containing rules, macro definitions, and comments. See the
EXTENDED DESCRIPTION section.</p>
</blockquote>

<h4><a name="tag_04_84_08"></a>ENVIRONMENT VARIABLES</h4>

<blockquote>
<p>The following environment variables shall affect the execution of <i>make</i>:</p>

<dl compact>
<dt><i>LANG</i></dt>

<dd>Provide a default value for the internationalization variables that are unset or null. (See the Base Definitions volume of
IEEE&nbsp;Std&nbsp;1003.1-2001, <a href="../basedefs/xbd_chap08.html#tag_08_02">Section 8.2, Internationalization Variables</a> for
the precedence of internationalization variables used to determine the values of locale categories.)</dd>

<dt><i>LC_ALL</i></dt>

<dd>If set to a non-empty string value, override the values of all the other internationalization variables.</dd>

<dt><i>LC_CTYPE</i></dt>

<dd>Determine the locale for the interpretation of sequences of bytes of text data as characters (for example, single-byte as
opposed to multi-byte characters in arguments and input files).</dd>

<dt><i>LC_MESSAGES</i></dt>

<dd>Determine the locale that should be used to affect the format and contents of diagnostic messages written to standard
error.</dd>

<dt><i>MAKEFLAGS</i></dt>

<dd>
This variable shall be interpreted as a character string representing a series of option characters to be used as the default
options. The implementation shall accept both of the following formats (but need not accept them when intermixed): 

<ul>
<li>
<p>The characters are option letters without the leading hyphens or &lt;blank&gt; separation used on a <i>make</i> utility command
line.</p>
</li>

<li>
<p>The characters are formatted in a manner similar to a portion of the <i>make</i> utility command line: options are preceded by
hyphens and &lt;blank&gt;-separated as described in the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001, <a href=
"../basedefs/xbd_chap12.html#tag_12_02">Section 12.2, Utility Syntax Guidelines</a>. The <i>macro</i>= <i>value</i> macro
definition operands can also be included. The difference between the contents of <i>MAKEFLAGS</i> and the <i>make</i> utility
command line is that the contents of the variable shall not be subjected to the word expansions (see <a href=
"xcu_chap02.html#tag_02_06"><i>Word Expansions</i></a>) associated with parsing the command line values.</p>
</li>
</ul>
</dd>

<dt><i>NLSPATH</i></dt>

<dd><sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup> <img src="../images/opt-start.gif" alt="[Option Start]" border="0">
Determine the location of message catalogs for the processing of <i>LC_MESSAGES .</i> <img src="../images/opt-end.gif" alt=
"[Option End]" border="0"></dd>

<dt><i>PROJECTDIR</i></dt>

<dd><sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup> <img src="../images/opt-start.gif" alt="[Option Start]" border=
"0"><br>
Provide a directory to be used to search for SCCS files not found in the current directory. In all of the following cases, the
search for SCCS files is made in the directory <b>SCCS</b> in the identified directory. If the value of <i>PROJECTDIR</i> begins
with a slash, it shall be considered an absolute pathname; otherwise, the value of <i>PROJECTDIR</i> is treated as a user name and
that user's initial working directory shall be examined for a subdirectory <b>src</b> or <b>source</b>. If such a directory is
found, it shall be used. Otherwise, the value is used as a relative pathname. 

<p>If <i>PROJECTDIR</i> is not set or has a null value, the search for SCCS files shall be made in the directory <b>SCCS</b> in the
current directory.</p>

<p>The setting of <i>PROJECTDIR</i> affects all files listed in the remainder of this utility description for files with a
component named <b>SCCS</b>. <img src="../images/opt-end.gif" alt="[Option End]" border="0"></p>
</dd>
</dl>

<p>The value of the <i>SHELL</i> environment variable shall not be used as a macro and shall not be modified by defining the
<b>SHELL</b> macro in a makefile or on the command line. All other environment variables, including those with null values, shall
be used as macros, as defined in <a href="#tag_04_84_13_04">Macros</a>.</p>
</blockquote>

<h4><a name="tag_04_84_09"></a>ASYNCHRONOUS EVENTS</h4>

<blockquote>
<p>If not already ignored, <i>make</i> shall trap SIGHUP, SIGTERM, SIGINT, and SIGQUIT and remove the current target unless the
target is a directory or the target is a prerequisite of the special target <b>.PRECIOUS</b> or unless one of the <b>-n</b>,
<b>-p</b>, or <b>-q</b> options was specified. Any targets removed in this manner shall be reported in diagnostic messages of
unspecified format, written to standard error. After this cleanup process, if any, <i>make</i> shall take the standard action for
all other signals.</p>
</blockquote>

<h4><a name="tag_04_84_10"></a>STDOUT</h4>

<blockquote>
<p>The <i>make</i> utility shall write all commands to be executed to standard output unless the <b>-s</b> option was specified,
the command is prefixed with an at sign, or the special target <b>.SILENT</b> has either the current target as a prerequisite or
has no prerequisites. If <i>make</i> is invoked without any work needing to be done, it shall write a message to standard output
indicating that no action was taken. If the <b>-t</b> option is present and a file is touched, <i>make</i> shall write to standard
output a message of unspecified format indicating that the file was touched, including the filename of the file.</p>
</blockquote>

<h4><a name="tag_04_84_11"></a>STDERR</h4>

<blockquote>
<p>The standard error shall be used only for diagnostic messages.</p>
</blockquote>

<h4><a name="tag_04_84_12"></a>OUTPUT FILES</h4>

<blockquote>
<p>Files can be created when the <b>-t</b> option is present. Additional files can also be created by the utilities invoked by
<i>make</i>.</p>
</blockquote>

<h4><a name="tag_04_84_13"></a>EXTENDED DESCRIPTION</h4>

<blockquote>
<p>The <i>make</i> utility attempts to perform the actions required to ensure that the specified targets are up-to-date. A target
is considered out-of-date if it is older than any of its prerequisites or if it does not exist. The <i>make</i> utility shall treat
all prerequisites as targets themselves and recursively ensure that they are up-to-date, processing them in the order in which they
appear in the rule. The <i>make</i> utility shall use the modification times of files to determine whether the corresponding
targets are out-of-date.</p>

<p>After <i>make</i> has ensured that all of the prerequisites of a target are up-to-date and if the target is out-of-date, the
commands associated with the target entry shall be executed. If there are no commands listed for the target, the target shall be
treated as up-to-date.</p>

<h5><a name="tag_04_84_13_01"></a>Makefile Syntax</h5>

<p>A makefile can contain rules, macro definitions (see <a href="#tag_04_84_13_04">Macros</a>), and comments. There are two kinds
of rules: <i>inference rules</i> and <i>target rules</i>. The <i>make</i> utility shall contain a set of built-in inference rules.
If the <b>-r</b> option is present, the built-in rules shall not be used and the suffix list shall be cleared. Additional rules of
both types can be specified in a makefile. If a rule is defined more than once, the value of the rule shall be that of the last one
specified. Macros can also be defined more than once, and the value of the macro is specified in <a href=
"#tag_04_84_13_04">Macros</a>. Comments start with a number sign ( <tt>'#'</tt> ) and continue until an unescaped &lt;newline&gt;
is reached.</p>

<p>By default, the following files shall be tried in sequence: <b>./makefile</b> and <b>./Makefile</b>. If neither
<b>./makefile</b> or <b>./Makefile</b> are found, other implementation-defined files may also be tried. <sup>[<a href=
"javascript:open_code('XSI')">XSI</a>]</sup> <img src="../images/opt-start.gif" alt="[Option Start]" border="0"> &nbsp;On
XSI-conformant systems, the additional files <b>./s.makefile</b>, <b>SCCS/s.makefile</b>, <b>./s.Makefile</b>, and
<b>SCCS/s.Makefile</b> shall also be tried. <img src="../images/opt-end.gif" alt="[Option End]" border="0"></p>

<p>The <b>-f</b> option shall direct <i>make</i> to ignore any of these default files and use the specified argument as a makefile
instead. If the <tt>'-'</tt> argument is specified, standard input shall be used.</p>

<p>The term <i>makefile</i> is used to refer to any rules provided by the user, whether in <b>./makefile</b> or its variants, or
specified by the <b>-f</b> option.</p>

<p>The rules in makefiles shall consist of the following types of lines: target rules, including special targets (see <a href=
"#tag_04_84_13_03">Target Rules</a>), inference rules (see <a href="#tag_04_84_13_05">Inference Rules</a>), macro definitions
(see <a href="#tag_04_84_13_04">Macros</a>), empty lines, and comments.</p>

<p>When an escaped &lt;newline&gt; (one preceded by a backslash) is found anywhere in the makefile except in a command line, it
shall be replaced, along with any leading white space on the following line, with a single &lt;space&gt;. When an escaped
&lt;newline&gt; is found in a command line in a makefile, the command line shall contain the backslash, the &lt;newline&gt;, and
the next line, except that the first character of the next line shall not be included if it is a &lt;tab&gt;.</p>

<h5><a name="tag_04_84_13_02"></a>Makefile Execution</h5>

<p>Makefile command lines shall be processed one at a time by writing the makefile command line to the standard output (unless one
of the conditions listed under <tt>'@'</tt> suppresses the writing) and executing the command(s) in the line. A &lt;tab&gt; may
precede the command to standard output. Command execution shall be as if the makefile command line were the argument to the <a
href="../functions/system.html"><i>system</i>()</a> function. The environment for the command being executed shall contain all of
the variables in the environment of <i>make</i>.</p>

<p>By default, when <i>make</i> receives a non-zero status from the execution of a command, it shall terminate with an error
message to standard error.</p>

<p>Makefile command lines can have one or more of the following prefixes: a hyphen ( <tt>'-'</tt> ), an at sign ( <tt>'@'</tt> ),
or a plus sign ( <tt>'+'</tt> ). These shall modify the way in which <i>make</i> processes the command. When a command is written
to standard output, the prefix shall not be included in the output.</p>

<dl compact>
<dt><tt>-</tt></dt>

<dd>If the command prefix contains a hyphen, or the <b>-i</b> option is present, or the special target <b>.IGNORE</b> has either
the current target as a prerequisite or has no prerequisites, any error found while executing the command shall be ignored.</dd>

<dt><tt>@</tt></dt>

<dd>If the command prefix contains an at sign and the <i>make</i> utility command line <b>-n</b> option is not specified, or the
<b>-s</b> option is present, or the special target <b>.SILENT</b> has either the current target as a prerequisite or has no
prerequisites, the command shall not be written to standard output before it is executed.</dd>

<dt><tt>+</tt></dt>

<dd>If the command prefix contains a plus sign, this indicates a makefile command line that shall be executed even if <b>-n</b>,
<b>-q</b>, or <b>-t</b> is specified.</dd>
</dl>

<h5><a name="tag_04_84_13_03"></a>Target Rules</h5>

<p>Target rules are formatted as follows:</p>

<pre>
<i>target</i> <b>[</b><i>target</i><tt>...</tt><b>]</b><tt>:</tt> <b>[</b><i>prerequisite</i><tt>...</tt><b>][;</b><i>command</i><b>]
[</b><tt>&lt;tab&gt;</tt><i>command</i>
<tt>&lt;tab&gt;</tt><i>command</i>
<tt>...</tt><b>]</b><tt><br>
</tt><i>line that does not begin with</i> <tt>&lt;tab&gt;
</tt>
</pre>

<p>Target entries are specified by a &lt;blank&gt;-separated, non-null list of targets, then a colon, then a
&lt;blank&gt;-separated, possibly empty list of prerequisites. Text following a semicolon, if any, and all following lines that
begin with a &lt;tab&gt;, are makefile command lines to be executed to update the target. The first non-empty line that does not
begin with a &lt;tab&gt; or <tt>'#'</tt> shall begin a new entry. An empty or blank line, or a line beginning with <tt>'#'</tt>,
may begin a new entry.</p>

<p>Applications shall select target names from the set of characters consisting solely of periods, underscores, digits, and
alphabetics from the portable character set (see the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001, <a href=
"../basedefs/xbd_chap06.html#tag_06_01">Section 6.1, Portable Character Set</a>). Implementations may allow other characters in
target names as extensions. The interpretation of targets containing the characters <tt>'%'</tt> and <tt>'"'</tt> is
implementation-defined.</p>

<p>A target that has prerequisites, but does not have any commands, can be used to add to the prerequisite list for that target.
Only one target rule for any given target can contain commands.</p>

<p>Lines that begin with one of the following are called <i>special targets</i> and control the operation of <i>make</i>:</p>

<dl compact>
<dt><b>.DEFAULT</b></dt>

<dd>If the makefile uses this special target, the application shall ensure that it is specified with commands, but without
prerequisites. The commands shall be used by <i>make</i> if there are no other rules available to build a target.</dd>

<dt><b>.IGNORE</b></dt>

<dd>Prerequisites of this special target are targets themselves; this shall cause errors from commands associated with them to be
ignored in the same manner as specified by the <b>-i</b> option. Subsequent occurrences of <b>.IGNORE</b> shall add to the list of
targets ignoring command errors. If no prerequisites are specified, <i>make</i> shall behave as if the <b>-i</b> option had been
specified and errors from all commands associated with all targets shall be ignored.</dd>

<dt><b>.POSIX</b></dt>

<dd>The application shall ensure that this special target is specified without prerequisites or commands. If it appears as the
first non-comment line in the makefile, <i>make</i> shall process the makefile as specified by this section; otherwise, the
behavior of <i>make</i> is unspecified.</dd>

<dt><b>.PRECIOUS</b></dt>

<dd>Prerequisites of this special target shall not be removed if <i>make</i> receives one of the asynchronous events explicitly
described in the ASYNCHRONOUS EVENTS section. Subsequent occurrences of <b>.PRECIOUS</b> shall add to the list of precious files.
If no prerequisites are specified, all targets in the makefile shall be treated as if specified with <b>.PRECIOUS</b>.</dd>

<dt><b>.SCCS_GET</b></dt>

<dd><sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup> <img src="../images/opt-start.gif" alt="[Option Start]" border="0">
The application shall ensure that this special target is specified without prerequisites. If this special target is included in a
makefile, the commands specified with this target shall replace the default commands associated with this special target (see <a
href="#tag_04_84_13_08">Default Rules</a>). The commands specified with this target are used to get all SCCS files that are not
found in the current directory. 

<p>When source files are named in a dependency list, <i>make</i> shall treat them just like any other target. Because the source
file is presumed to be present in the directory, there is no need to add an entry for it to the makefile. When a target has no
dependencies, but is present in the directory, <i>make</i> shall assume that that file is up-to-date. If, however, an SCCS file
named <b>SCCS/s.</b> <i>source_file</i> is found for a target <i>source_file</i>, <i>make</i> compares the timestamp of the target
file with that of the <b>SCCS/s.source_file</b> to ensure the target is up-to-date. If the target is missing, or if the SCCS file
is newer, <i>make</i> shall automatically issue the commands specified for the <b>.SCCS_GET</b> special target to retrieve the most
recent version. However, if the target is writable by anyone, <i>make</i> shall not retrieve a new version. <img src=
"../images/opt-end.gif" alt="[Option End]" border="0"></p>
</dd>

<dt><b>.SILENT</b></dt>

<dd>Prerequisites of this special target are targets themselves; this shall cause commands associated with them not to be written
to the standard output before they are executed. Subsequent occurrences of <b>.SILENT</b> shall add to the list of targets with
silent commands. If no prerequisites are specified, <i>make</i> shall behave as if the <b>-s</b> option had been specified and no
commands or touch messages associated with any target shall be written to standard output.</dd>

<dt><b>.SUFFIXES</b></dt>

<dd>Prerequisites of <b>.SUFFIXES</b> shall be appended to the list of known suffixes and are used in conjunction with the
inference rules (see <a href="#tag_04_84_13_05">Inference Rules</a>). If <b>.SUFFIXES</b> does not have any prerequisites, the
list of known suffixes shall be cleared.</dd>
</dl>

<p>The special targets <b>.IGNORE</b>, <b>.POSIX</b>, <b>.PRECIOUS</b>, <b>.SILENT</b>, and <b>.SUFFIXES</b> shall be specified
without commands.</p>

<p>Targets with names consisting of a leading period followed by the uppercase letters <tt>"POSIX"</tt> and then any other
characters are reserved for future standardization. Targets with names consisting of a leading period followed by one or more
uppercase letters are reserved for implementation extensions.</p>

<h5><a name="tag_04_84_13_04"></a>Macros</h5>

<p>Macro definitions are in the form:</p>

<pre>
<i>string1</i> <tt>=</tt> <b>[</b><i>string2</i><b>]</b>
</pre>

<p>The macro named <i>string1</i> is defined as having the value of <i>string2</i>, where <i>string2</i> is defined as all
characters, if any, after the equal sign, up to a comment character ( <tt>'#'</tt> ) or an unescaped &lt;newline&gt;. Any
&lt;blank&gt;s immediately before or after the equal sign shall be ignored.</p>

<p>Applications shall select macro names from the set of characters consisting solely of periods, underscores, digits, and
alphabetics from the portable character set (see the Base Definitions volume of IEEE&nbsp;Std&nbsp;1003.1-2001, <a href=
"../basedefs/xbd_chap06.html#tag_06_01">Section 6.1, Portable Character Set</a>). A macro name shall not contain an equals sign.
Implementations may allow other characters in macro names as extensions.</p>

<p>Macros can appear anywhere in the makefile. Macro expansions using the forms $( <i>string1</i>) or ${ <i>string1</i>} shall be
replaced by <i>string2</i>, as follows:</p>

<ul>
<li>
<p>Macros in target lines shall be evaluated when the target line is read.</p>
</li>

<li>
<p>Macros in makefile command lines shall be evaluated when the command is executed.</p>
</li>

<li>
<p>Macros in the string before the equals sign in a macro definition shall be evaluated when the macro assignment is made.</p>
</li>

<li>
<p>Macros after the equals sign in a macro definition shall not be evaluated until the defined macro is used in a rule or command,
or before the equals sign in a macro definition.</p>
</li>
</ul>

<p>The parentheses or braces are optional if <i>string1</i> is a single character. The macro $$ shall be replaced by the single
character <tt>'$'</tt>. If <i>string1</i> in a macro expansion contains a macro expansion, the results are unspecified.</p>

<p>Macro expansions using the forms $( <i>string1</i> <b>[:</b> <i>subst1</i> <b>=[</b> <i>subst2</i> <b>]]</b>) or ${
<i>string1</i> <b>[:</b> <i>subst1</i> <b>=[</b> <i>subst2</i> <b>]]</b>} can be used to replace all occurrences of <i>subst1</i>
with <i>subst2</i> when the macro substitution is performed. The <i>subst1</i> to be replaced shall be recognized when it is a
suffix at the end of a word in <i>string1</i> (where a <i>word</i>, in this context, is defined to be a string delimited by the
beginning of the line, a &lt;blank&gt;, or a &lt;newline&gt;). If <i>string1</i> in a macro expansion contains a macro expansion,
the results are unspecified.</p>

<p>Macro expansions in <i>string1</i> of macro definition lines shall be evaluated when read. Macro expansions in <i>string2</i> of
macro definition lines shall be performed when the macro identified by <i>string1</i> is expanded in a rule or command.</p>

<p>Macro definitions shall be taken from the following sources, in the following logical order, before the makefile(s) are
read.</p>

<ol>
<li>
<p>Macros specified on the <i>make</i> utility command line, in the order specified on the command line. It is unspecified whether
the internal macros defined in <a href="#tag_04_84_13_07">Internal Macros</a> are accepted from this source.</p>
</li>

<li>
<p>Macros defined by the <i>MAKEFLAGS</i> environment variable, in the order specified in the environment variable. It is
unspecified whether the internal macros defined in <a href="#tag_04_84_13_07">Internal Macros</a> are accepted from this
source.</p>
</li>

<li>
<p>The contents of the environment, excluding the <i>MAKEFLAGS</i> and <i>SHELL</i> variables and including the variables with null
values.</p>
</li>

<li>
<p>Macros defined in the inference rules built into <i>make</i>.</p>
</li>
</ol>

<p>Macro definitions from these sources shall not override macro definitions from a lower-numbered source. Macro definitions from a
single source (for example, the <i>make</i> utility command line, the <i>MAKEFLAGS</i> environment variable, or the other
environment variables) shall override previous macro definitions from the same source.</p>

<p>Macros defined in the makefile(s) shall override macro definitions that occur before them in the makefile(s) and macro
definitions from source 4. If the <b>-e</b> option is not specified, macros defined in the makefile(s) shall override macro
definitions from source 3. Macros defined in the makefile(s) shall not override macro definitions from source 1 or source 2.</p>

<p>Before the makefile(s) are read, all of the <i>make</i> utility command line options (except <b>-f</b> and <b>-p</b>) and
<i>make</i> utility command line macro definitions (except any for the <i>MAKEFLAGS</i> macro), not already included in the
<i>MAKEFLAGS</i> macro, shall be added to the <i>MAKEFLAGS</i> macro, quoted in an implementation-defined manner such that when
<i>MAKEFLAGS</i> is read by another instance of the <i>make</i> command, the original macro's value is recovered. Other
implementation-defined options and macros may also be added to the <i>MAKEFLAGS</i> macro. If this modifies the value of the
<i>MAKEFLAGS</i> macro, or, if the <i>MAKEFLAGS</i> macro is modified at any subsequent time, the <i>MAKEFLAGS</i> environment
variable shall be modified to match the new value of the <i>MAKEFLAGS</i> macro. The result of setting <i>MAKEFLAGS</i> in the
Makefile is unspecified.</p>

<p>Before the makefile(s) are read, all of the <i>make</i> utility command line macro definitions (except the <i>MAKEFLAGS</i>
macro or the <i>SHELL</i> macro) shall be added to the environment of <i>make</i>. Other implementation-defined variables may also
be added to the environment of <i>make</i>.</p>

<p>The <b>SHELL</b> macro shall be treated specially. It shall be provided by <i>make</i> and set to the pathname of the shell
command language interpreter (see <a href="sh.html"><i>sh</i></a>). The <i>SHELL</i> environment variable shall not affect the
value of the <b>SHELL</b> macro. If <b>SHELL</b> is defined in the makefile or is specified on the command line, it shall replace
the original value of the <b>SHELL</b> macro, but shall not affect the <i>SHELL</i> environment variable. Other effects of defining
<b>SHELL</b> in the makefile or on the command line are implementation-defined.</p>

<h5><a name="tag_04_84_13_05"></a>Inference Rules</h5>

<p>Inference rules are formatted as follows:</p>

<pre>
<i>target</i><tt>:
&lt;tab&gt;</tt><i>command
</i><b>[</b><tt>&lt;tab&gt;</tt><i>command</i><b>]</b><tt>...
<br>
</tt><i>line that does not begin with</i> <tt>&lt;tab&gt;</tt> <i>or</i> <tt>#
</tt>
</pre>

<p>The application shall ensure that the <i>target</i> portion is a valid target name (see <a href="#tag_04_84_13_03">Target
Rules</a>) of the form <b>.s2</b> or <b>.s1.s2</b> (where <b>.s1</b> and <b>.s2</b> are suffixes that have been given as
prerequisites of the <b>.SUFFIXES</b> special target and <i>s1</i> and <i>s2</i> do not contain any slashes or periods.) If there
is only one period in the target, it is a single-suffix inference rule. Targets with two periods are double-suffix inference rules.
Inference rules can have only one target before the colon.</p>

<p>The application shall ensure that the makefile does not specify prerequisites for inference rules; no characters other than
white space shall follow the colon in the first line, except when creating the <i>empty rule,</i> described below. Prerequisites
are inferred, as described below.</p>

<p>Inference rules can be redefined. A target that matches an existing inference rule shall overwrite the old inference rule. An
empty rule can be created with a command consisting of simply a semicolon (that is, the rule still exists and is found during
inference rule search, but since it is empty, execution has no effect). The empty rule can also be formatted as follows:</p>

<pre>
<i>rule</i><tt>: ;
</tt>
</pre>

<p>where zero or more &lt;blank&gt;s separate the colon and semicolon.</p>

<p>The <i>make</i> utility uses the suffixes of targets and their prerequisites to infer how a target can be made up-to-date. A
list of inference rules defines the commands to be executed. By default, <i>make</i> contains a built-in set of inference rules.
Additional rules can be specified in the makefile.</p>

<p>The special target <b>.SUFFIXES</b> contains as its prerequisites a list of suffixes that shall be used by the inference rules.
The order in which the suffixes are specified defines the order in which the inference rules for the suffixes are used. New
suffixes shall be appended to the current list by specifying a <b>.SUFFIXES</b> special target in the makefile. A <b>.SUFFIXES</b>
target with no prerequisites shall clear the list of suffixes. An empty <b>.SUFFIXES</b> target followed by a new <b>.SUFFIXES</b>
list is required to change the order of the suffixes.</p>

<p>Normally, the user would provide an inference rule for each suffix. The inference rule to update a target with a suffix
<b>.s1</b> from a prerequisite with a suffix <b>.s2</b> is specified as a target <b>.s2.s1</b>. The internal macros provide the
means to specify general inference rules (see <a href="#tag_04_84_13_07">Internal Macros</a>).</p>

<p>When no target rule is found to update a target, the inference rules shall be checked. The suffix of the target ( <b>.s1</b>) to
be built is compared to the list of suffixes specified by the <b>.SUFFIXES</b> special targets. If the <b>.s1</b> suffix is found
in <b>.SUFFIXES</b>, the inference rules shall be searched in the order defined for the first <b>.s2.s1</b> rule whose prerequisite
file ( <b>$*.s2</b>) exists. If the target is out-of-date with respect to this prerequisite, the commands for that inference rule
shall be executed.</p>

<p>If the target to be built does not contain a suffix and there is no rule for the target, the single suffix inference rules shall
be checked. The single-suffix inference rules define how to build a target if a file is found with a name that matches the target
name with one of the single suffixes appended. A rule with one suffix <b>.s2</b> is the definition of how to build <i>target</i>
from <b>target.s2</b>. The other suffix ( <b>.s1</b>) is treated as null.</p>

<p><sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup> <img src="../images/opt-start.gif" alt="[Option Start]" border="0"> A
tilde ( <tt>'&#152;'</tt> ) in the above rules refers to an SCCS file in the current directory. Thus, the rule <b>.c&#152;.o</b>
would transform an SCCS C-language source file into an object file ( <b>.o</b>). Because the <b>s.</b> of the SCCS files is a
prefix, it is incompatible with <i>make</i>'s suffix point of view. Hence, the <tt>'&#152;'</tt> is a way of changing any file
reference into an SCCS file reference. <img src="../images/opt-end.gif" alt="[Option End]" border="0"></p>

<h5><a name="tag_04_84_13_06"></a>Libraries</h5>

<p>If a target or prerequisite contains parentheses, it shall be treated as a member of an archive library. For the <i>lib</i>(
<i>member</i> <b>.o</b>) expression <i>lib</i> refers to the name of the archive library and <i>member</i> <b>.o</b> to the member
name. The application shall ensure that the member is an object file with the <b>.o</b> suffix. The modification time of the
expression is the modification time for the member as kept in the archive library; see <a href="ar.html"><i>ar</i></a>. The
<b>.a</b> suffix shall refer to an archive library. The <b>.s2.a</b> rule shall be used to update a member in the library from a
file with a suffix <b>.s2</b>.</p>

<h5><a name="tag_04_84_13_07"></a>Internal Macros</h5>

<p>The <i>make</i> utility shall maintain five internal macros that can be used in target and inference rules. In order to clearly
define the meaning of these macros, some clarification of the terms <i>target rule</i>, <i>inference rule</i>, <i>target</i>, and
<i>prerequisite</i> is necessary.</p>

<p>Target rules are specified by the user in a makefile for a particular target. Inference rules are user-specified or
<i>make</i>-specified rules for a particular class of target name. Explicit prerequisites are those prerequisites specified in a
makefile on target lines. Implicit prerequisites are those prerequisites that are generated when inference rules are used.
Inference rules are applied to implicit prerequisites or to explicit prerequisites that do not have target rules defined for them
in the makefile. Target rules are applied to targets specified in the makefile.</p>

<p>Before any target in the makefile is updated, each of its prerequisites (both explicit and implicit) shall be updated. This
shall be accomplished by recursively processing each prerequisite. Upon recursion, each prerequisite shall become a target itself.
Its prerequisites in turn shall be processed recursively until a target is found that has no prerequisites, at which point the
recursion stops. The recursion shall then back up, updating each target as it goes.</p>

<p>In the definitions that follow, the word <i>target</i> refers to one of:</p>

<ul>
<li>
<p>A target specified in the makefile</p>
</li>

<li>
<p>An explicit prerequisite specified in the makefile that becomes the target when <i>make</i> processes it during recursion</p>
</li>

<li>
<p>An implicit prerequisite that becomes a target when <i>make</i> processes it during recursion</p>
</li>
</ul>

<p>In the definitions that follow, the word <i>prerequisite</i> refers to one of the following:</p>

<ul>
<li>
<p>An explicit prerequisite specified in the makefile for a particular target</p>
</li>

<li>
<p>An implicit prerequisite generated as a result of locating an appropriate inference rule and corresponding file that matches the
suffix of the target</p>
</li>
</ul>

<p>The five internal macros are:</p>

<dl compact>
<dt>$@</dt>

<dd>The $@ shall evaluate to the full target name of the current target, or the archive filename part of a library archive target.
It shall be evaluated for both target and inference rules. 

<p>For example, in the <b>.c.a</b> inference rule, $@ represents the out-of-date <b>.a</b> file to be built. Similarly, in a
makefile target rule to build <b>lib.a</b> from <b>file.c</b>, $@ represents the out-of-date <b>lib.a</b>.</p>
</dd>

<dt>$%</dt>

<dd>The $% macro shall be evaluated only when the current target is an archive library member of the form <i>libname</i>(
<i>member</i> <b>.o</b>). In these cases, $@ shall evaluate to <i>libname</i> and $% shall evaluate to <i>member</i> <b>.o</b>. The
$% macro shall be evaluated for both target and inference rules. 

<p>For example, in a makefile target rule to build <b>lib.a</b>( <b>file.o</b>), $% represents <b>file.o</b>, as opposed to $@,
which represents <b>lib.a</b>.</p>
</dd>

<dt>$?</dt>

<dd>The $? macro shall evaluate to the list of prerequisites that are newer than the current target. It shall be evaluated for both
target and inference rules. 

<p>For example, in a makefile target rule to build <i>prog</i> from <b>file1.o</b>, <b>file2.o</b>, and <b>file3.o</b>, and where
<i>prog</i> is not out-of-date with respect to <b>file1.o</b>, but is out-of-date with respect to <b>file2.o</b> and
<b>file3.o</b>, $? represents <b>file2.o</b> and <b>file3.o</b>.</p>
</dd>

<dt>$&lt;</dt>

<dd>In an inference rule, the $&lt; macro shall evaluate to the filename whose existence allowed the inference rule to be chosen
for the target. In the <b>.DEFAULT</b> rule, the $&lt; macro shall evaluate to the current target name. The meaning of the $&lt;
macro shall be otherwise unspecified. 

<p>For example, in the <b>.c.a</b> inference rule, $&lt; represents the prerequisite <b>.c</b> file.</p>
</dd>

<dt>$*</dt>

<dd>The $* macro shall evaluate to the current target name with its suffix deleted. It shall be evaluated at least for inference
rules. 

<p>For example, in the <b>.c.a</b> inference rule, $*.o represents the out-of-date <b>.o</b> file that corresponds to the
prerequisite <b>.c</b> file.</p>
</dd>
</dl>

<p>Each of the internal macros has an alternative form. When an uppercase <tt>'D'</tt> or <tt>'F'</tt> is appended to any of the
macros, the meaning shall be changed to the <i>directory part</i> for <tt>'D'</tt> and <i>filename part</i> for <tt>'F'</tt>. The
directory part is the path prefix of the file without a trailing slash; for the current directory, the directory part is
<tt>'.'</tt>. When the $? macro contains more than one prerequisite filename, the $(?D) and $(?F) (or ${?D} and ${?F}) macros
expand to a list of directory name parts and filename parts respectively.</p>

<p>For the target <i>lib</i>( <i>member</i> <b>.o</b>) and the <b>s2.a</b> rule, the internal macros shall be defined as:</p>

<dl compact>
<dt>$&lt;</dt>

<dd><i>member</i> <b>.s2</b></dd>

<dt>$*</dt>

<dd><i>member</i></dd>

<dt>$@</dt>

<dd><i>lib</i></dd>

<dt>$?</dt>

<dd><i>member</i> <b>.s2</b></dd>

<dt>$%</dt>

<dd><i>member</i> <b>.o</b></dd>
</dl>

<h5><a name="tag_04_84_13_08"></a>Default Rules</h5>

<p>The default rules for <i>make</i> shall achieve results that are the same as if the following were used. Implementations that do
not support the C-Language Development Utilities option may omit <b>CC</b>, <b>CFLAGS</b>, <b>YACC</b>, <b>YFLAGS</b>, <b>LEX</b>,
<b>LFLAGS</b>, <b>LDFLAGS</b>, and the <b>.c</b>, <b>.y</b>, and <b>.l</b> inference rules. Implementations that do not support
FORTRAN may omit <b>FC</b>, <b>FFLAGS</b>, and the <b>.f</b> inference rules. Implementations may provide additional macros and
rules.</p>

<pre>
<i>SPECIAL TARGETS</i><tt><br>
<sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup><img src="../images/opt-start.gif" alt="[Option Start]" border="0">
.SCCS_GET: sccs $(SCCSFLAGS) get $(SCCSGETFLAGS) $@
<img src="../images/opt-end.gif" alt="[Option End]" border="0">
<br>
<sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup>
.SUFFIXES: .o .c .y .l .a .sh .f <img src="../images/opt-start.gif" border="0">.c&#152; .y&#152; .l&#152; .sh&#152; .f&#152;<img
src="../images/opt-end.gif" border="0">
<br>
</tt><i>MACROS</i><tt><br>
MAKE=make
AR=ar
ARFLAGS=-rv
YACC=yacc
YFLAGS=
LEX=lex
LFLAGS=
LDFLAGS=
CC=c99
CFLAGS=-O
FC=fort77
FFLAGS=-O 1
<sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup><img src="../images/opt-start.gif" alt="[Option Start]" border="0">
GET=get
GFLAGS=
SCCSFLAGS=
SCCSGETFLAGS=-s
<img src="../images/opt-end.gif" alt="[Option End]" border="0">
<br>
</tt><i>SINGLE SUFFIX RULES</i><tt><br>
.c:
    $(CC) $(CFLAGS) $(LDFLAGS) -o $@ $&lt;
<br>
.f:
    $(FC) $(FFLAGS) $(LDFLAGS) -o $@ $&lt;
<br>
.sh:
    cp $&lt; $@
    chmod a+x $@
<br>
<sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup><img src="../images/opt-start.gif" alt="[Option Start]" border="0">
.c&#152;:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.c
    $(CC) $(CFLAGS) $(LDFLAGS) -o $@ $*.c
<br>
.f&#152;:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.f
    $(FC) $(FFLAGS) $(LDFLAGS) -o $@ $*.f
<br>
.sh&#152;:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.sh
    cp $*.sh $@
    chmod a+x $@
<img src="../images/opt-end.gif" alt="[Option End]" border="0">
<br>
</tt><i>DOUBLE SUFFIX RULES</i><tt><br>
.c.o:
    $(CC) $(CFLAGS) -c $&lt;
<br>
.f.o:
    $(FC) $(FFLAGS) -c $&lt;
<br>
.y.o:
    $(YACC) $(YFLAGS) $&lt;
    $(CC) $(CFLAGS) -c y.tab.c
    rm -f y.tab.c
    mv y.tab.o $@
<br>
.l.o:
    $(LEX) $(LFLAGS) $&lt;
    $(CC) $(CFLAGS) -c lex.yy.c
    rm -f lex.yy.c
    mv lex.yy.o $@
<br>
.y.c:
    $(YACC) $(YFLAGS) $&lt;
    mv y.tab.c $@
<br>
.l.c:
    $(LEX) $(LFLAGS) $&lt;
    mv lex.yy.c $@
<br>
<sup>[<a href="javascript:open_code('XSI')">XSI</a>]</sup><img src="../images/opt-start.gif" alt="[Option Start]" border="0">
.c&#152;.o:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.c
    $(CC) $(CFLAGS) -c $*.c
<br>
.f&#152;.o:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.f
    $(FC) $(FFLAGS) -c $*.f
<br>
.y&#152;.o:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.y
    $(YACC) $(YFLAGS) $*.y
    $(CC) $(CFLAGS) -c y.tab.c
    rm -f y.tab.c
    mv y.tab.o $@
<br>
.l&#152;.o:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.l
    $(LEX) $(LFLAGS) $*.l
    $(CC) $(CFLAGS) -c lex.yy.c
    rm -f lex.yy.c
    mv lex.yy.o $@
<br>
.y&#152;.c:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.y
    $(YACC) $(YFLAGS) $*.y
    mv y.tab.c $@
<br>
.l&#152;.c:
    $(GET) $(GFLAGS) -p $&lt; &gt; $*.l
    $(LEX) $(LFLAGS) $*.l
    mv lex.yy.c $@
<img src="../images/opt-end.gif" alt="[Option End]" border="0">
<br>
.c.a:
    $(CC) -c $(CFLAGS) $&lt;
    $(AR) $(ARFLAGS) $@ $*.o
    rm -f $*.o
<br>
.f.a:
    $(FC) -c $(FFLAGS) $&lt;
    $(AR) $(ARFLAGS) $@ $*.o
    rm -f $*.o
</tt>
</pre>
</blockquote>

<h4><a name="tag_04_84_14"></a>EXIT STATUS</h4>

<blockquote>
<p>When the <b>-q</b> option is specified, the <i>make</i> utility shall exit with one of the following values:</p>

<dl compact>
<dt>&nbsp;0</dt>

<dd>Successful completion.</dd>

<dt>&nbsp;1</dt>

<dd>The target was not up-to-date.</dd>

<dt>&gt;1</dt>

<dd>An error occurred.</dd>
</dl>

<p>When the <b>-q</b> option is not specified, the <i>make</i> utility shall exit with one of the following values:</p>

<dl compact>
<dt>&nbsp;0</dt>

<dd>Successful completion.</dd>

<dt>&gt;0</dt>

<dd>An error occurred.</dd>
</dl>
</blockquote>

<h4><a name="tag_04_84_15"></a>CONSEQUENCES OF ERRORS</h4>

<blockquote>
<p>Default.</p>
</blockquote>

<hr>
<div class="box"><em>The following sections are informative.</em></div>

<h4><a name="tag_04_84_16"></a>APPLICATION USAGE</h4>

<blockquote>
<p>If there is a source file (such as <b>./source.c</b>) and there are two SCCS files corresponding to it ( <b>./s.source.c</b> and
<b>./SCCS/s.source.c</b>), on XSI-conformant systems <i>make</i> uses the SCCS file in the current directory. However, users are
advised to use the underlying SCCS utilities ( <a href="../utilities/admin.html"><i>admin</i></a>, <a href=
"../utilities/delta.html"><i>delta</i></a>, <a href="../utilities/get.html"><i>get</i></a>, and so on) or the <a href=
"../utilities/sccs.html"><i>sccs</i></a> utility for all source files in a given directory. If both forms are used for a given
source file, future developers are very likely to be confused.</p>

<p>It is incumbent upon portable makefiles to specify the <b>.POSIX</b> special target in order to guarantee that they are not
affected by local extensions.</p>

<p>The <b>-k</b> and <b>-S</b> options are both present so that the relationship between the command line, the <i>MAKEFLAGS</i>
variable, and the makefile can be controlled precisely. If the <b>k</b> flag is passed in <i>MAKEFLAGS</i> and a command is of the
form:</p>

<pre>
<tt>$(MAKE) -S foo
</tt>
</pre>

<p>then the default behavior is restored for the child <i>make</i>.</p>

<p>When the <b>-n</b> option is specified, it is always added to <i>MAKEFLAGS .</i> This allows a recursive <i>make</i> <b>-n</b>
<i>target</i> to be used to see all of the action that would be taken to update <i>target</i>.</p>

<p>Because of widespread historical practice, interpreting a <tt>'#'</tt> number sign inside a variable as the start of a comment
has the unfortunate side effect of making it impossible to place a number sign in a variable, thus forbidding something like:</p>

<pre>
<tt>CFLAGS = "-D COMMENT_CHAR='#'"
</tt>
</pre>

<p>Many historical <i>make</i> utilities stop chaining together inference rules when an intermediate target is nonexistent. For
example, it might be possible for a <i>make</i> to determine that both <b>.y.c</b> and <b>.c.o</b> could be used to convert a
<b>.y</b> to a <b>.o</b>. Instead, in this case, <i>make</i> requires the use of a <b>.y.o</b> rule.</p>

<p>The best way to provide portable makefiles is to include all of the rules needed in the makefile itself. The rules provided use
only features provided by other parts of this volume of IEEE&nbsp;Std&nbsp;1003.1-2001. The default rules include rules for
optional commands in this volume of IEEE&nbsp;Std&nbsp;1003.1-2001. Only rules pertaining to commands that are provided are needed
in an implementation's default set.</p>

<p>Macros used within other macros are evaluated when the new macro is used rather than when the new macro is defined.
Therefore:</p>

<pre>
<tt>MACRO =</tt> <i>value1</i><tt>NEW   = $(MACRO)
MACRO =</tt> <i>value2</i><tt><br>
target:
    echo $(NEW)
</tt>
</pre>

<p>would produce <i>value2</i> and not <i>value1</i> since <b>NEW</b> was not expanded until it was needed in the <a href=
"../utilities/echo.html"><i>echo</i></a> command line.</p>

<p>Some historical applications have been known to intermix <i>target_name</i> and <i>macro=name</i> operands on the command line,
expecting that all of the macros are processed before any of the targets are dealt with. Conforming applications do not do this,
although some backwards-compatibility support may be included in some implementations.</p>

<p>The following characters in filenames may give trouble: <tt>'='</tt>, <tt>':'</tt>, <tt>'`'</tt>, <tt>'&quot;</tt>, and
<tt>'@'</tt>. For inference rules, the description of $&lt; and $? seem similar. However, an example shows the minor difference.
In a makefile containing:</p>

<pre>
<tt>foo.o: foo.h
</tt>
</pre>

<p>if <b>foo.h</b> is newer than <b>foo.o</b>, yet <b>foo.c</b> is older than <b>foo.o</b>, the built-in rule to make <b>foo.o</b>
from <b>foo.c</b> is used, with $&lt; equal to <b>foo.c</b> and $? equal to <b>foo.h</b>. If <b>foo.c</b> is also newer than
<b>foo.o</b>, $&lt; is equal to <b>foo.c</b> and $? is equal to <b>foo.h foo.c</b>.</p>
</blockquote>

<h4><a name="tag_04_84_17"></a>EXAMPLES</h4>

<blockquote>
<ol>
<li>
<p>The following command:</p>

<pre>
<tt>make
</tt>
</pre>

<p>makes the first target found in the makefile.</p>
</li>

<li>
<p>The following command:</p>

<pre>
<tt>make junk
</tt>
</pre>

<p>makes the target <b>junk</b>.</p>
</li>

<li>
<p>The following makefile says that <b>pgm</b> depends on two files, <b>a.o</b> and <b>b.o</b>, and that they in turn depend on
their corresponding source files ( <b>a.c</b> and <b>b.c</b>), and a common file <b>incl.h</b>:</p>

<pre>
<tt>pgm: a.o b.o
    c99 a.o b.o -o pgm
a.o: incl.h a.c
    c99 -c a.c
b.o: incl.h b.c
    c99 -c b.c
</tt>
</pre>
</li>

<li>
<p>An example for making optimized <b>.o</b> files from <b>.c</b> files is:</p>

<pre>
<tt>.c.o:
    c99 -c -O $*.c
</tt>
</pre>

<p>or:</p>

<pre>
<tt>.c.o:
    c99 -c -O $&lt;
</tt>
</pre>
</li>

<li>
<p>The most common use of the archive interface follows. Here, it is assumed that the source files are all C-language source:</p>

<pre>
<tt>lib: lib(file1.o) lib(file2.o) lib(file3.o)
    @echo lib is now up-to-date
</tt>
</pre>

<p>The <b>.c.a</b> rule is used to make <b>file1.o</b>, <b>file2.o</b>, and <b>file3.o</b> and insert them into <b>lib</b>.</p>

<p>The treatment of escaped &lt;newline&gt;s throughout the makefile is historical practice. For example, the inference rule:</p>

<pre>
<tt>.c.o\
:
</tt>
</pre>

<p>works, and the macro:</p>

<pre>
<tt>f=  bar baz\
    biz
a:
    echo ==$f==
</tt>
</pre>

<p>echoes <tt>"==bar&nbsp;baz&nbsp;biz=="</tt>.</p>

<p>If $? were:</p>

<pre>
<tt>/usr/include/stdio.h /usr/include/unistd.h foo.h
</tt>
</pre>

<p>then $(?D) would be:</p>

<pre>
<tt>/usr/include /usr/include .
</tt>
</pre>

<p>and $(?F) would be:</p>

<pre>
<tt>stdio.h unistd.h foo.h
</tt>
</pre>
</li>

<li>
<p>The contents of the built-in rules can be viewed by running:</p>

<pre>
<tt>make -p -f /dev/null 2&gt;/dev/null
</tt>
</pre>
</li>
</ol>
</blockquote>

<h4><a name="tag_04_84_18"></a>RATIONALE</h4>

<blockquote>
<p>The <i>make</i> utility described in this volume of IEEE&nbsp;Std&nbsp;1003.1-2001 is intended to provide the means for changing
portable source code into executables that can be run on an IEEE&nbsp;Std&nbsp;1003.1-2001-conforming system. It reflects the most
common features present in System V and BSD <i>make</i>s.</p>

<p>Historically, the <i>make</i> utility has been an especially fertile ground for vendor and research organization-specific syntax
modifications and extensions. Examples include:</p>

<ul>
<li>
<p>Syntax supporting parallel execution (such as from various multi-processor vendors, GNU, and others)</p>
</li>

<li>
<p>Additional &quot;operators&quot; separating targets and their prerequisites (System V, BSD, and others)</p>
</li>

<li>
<p>Specifying that command lines containing the strings <tt>"${MAKE}"</tt> and <tt>"$(MAKE)"</tt> are executed when the <b>-n</b>
option is specified (GNU and System V)</p>
</li>

<li>
<p>Modifications of the meaning of internal macros when referencing libraries (BSD and others)</p>
</li>

<li>
<p>Using a single instance of the shell for all of the command lines of the target (BSD and others)</p>
</li>

<li>
<p>Allowing spaces as well as tabs to delimit command lines (BSD)</p>
</li>

<li>
<p>Adding C preprocessor-style &quot;include&quot; and &quot;ifdef&quot; constructs (System V, GNU, BSD, and others)</p>
</li>

<li>
<p>Remote execution of command lines (Sprite and others)</p>
</li>

<li>
<p>Specifying additional special targets (BSD, System V, and most others)</p>
</li>
</ul>

<p>Additionally, many vendors and research organizations have rethought the basic concepts of <i>make</i>, creating vastly
extended, as well as completely new, syntaxes. Each of these versions of <i>make</i> fulfills the needs of a different community of
users; it is unreasonable for this volume of IEEE&nbsp;Std&nbsp;1003.1-2001 to require behavior that would be incompatible (and
probably inferior) to historical practice for such a community.</p>

<p>In similar circumstances, when the industry has enough sufficiently incompatible formats as to make them irreconcilable, this
volume of IEEE&nbsp;Std&nbsp;1003.1-2001 has followed one or both of two courses of action. Commands have been renamed ( <a href=
"../utilities/cksum.html"><i>cksum</i></a>, <a href="../utilities/echo.html"><i>echo</i></a>, and <a href=
"../utilities/pax.html"><i>pax</i></a>) and/or command line options have been provided to select the desired behavior ( <a href=
"../utilities/grep.html"><i>grep</i></a>, <a href="../utilities/od.html"><i>od</i></a>, and <a href=
"../utilities/pax.html"><i>pax</i></a>).</p>

<p>Because the syntax specified for the <i>make</i> utility is, by and large, a subset of the syntaxes accepted by almost all
versions of <i>make</i>, it was decided that it would be counter-productive to change the name. And since the makefile itself is a
basic unit of portability, it would not be completely effective to reserve a new option letter, such as <i>make</i> <b>-P</b>, to
achieve the portable behavior. Therefore, the special target <b>.POSIX</b> was added to the makefile, allowing users to specify
&quot;standard&quot; behavior. This special target does not preclude extensions in the <i>make</i> utility, nor does it preclude such
extensions being used by the makefile specifying the target; it does, however, preclude any extensions from being applied that
could alter the behavior of previously valid syntax; such extensions must be controlled via command line options or new special
targets. It is incumbent upon portable makefiles to specify the <b>.POSIX</b> special target in order to guarantee that they are
not affected by local extensions.</p>

<p>The portable version of <i>make</i> described in this reference page is not intended to be the state-of-the-art software
generation tool and, as such, some newer and more leading-edge features have not been included. An attempt has been made to
describe the portable makefile in a manner that does not preclude such extensions as long as they do not disturb the portable
behavior described here.</p>

<p>When the <b>-n</b> option is specified, it is always added to <i>MAKEFLAGS .</i> This allows a recursive <i>make</i> <b>-n</b>
<i>target</i> to be used to see all of the action that would be taken to update <i>target</i>.</p>

<p>The definition of <i>MAKEFLAGS</i> allows both the System V letter string and the BSD command line formats. The two formats are
sufficiently different to allow implementations to support both without ambiguity.</p>

<p>Early proposals stated that an &quot;unquoted&quot; number sign was treated as the start of a comment. The <i>make</i> utility does not
pay any attention to quotes. A number sign starts a comment regardless of its surroundings.</p>

<p>The text about &quot;other implementation-defined pathnames may also be tried&quot; in addition to <b>./makefile</b> and
<b>./Makefile</b> is to allow such extensions as <b>SCCS/s.Makefile</b> and other variations. It was made an implementation-defined
requirement (as opposed to unspecified behavior) to highlight surprising implementations that might select something unexpected
like <b>/etc/Makefile</b>. XSI-conformant systems also try <b>./s.makefile</b>, <b>SCCS/s.makefile</b>, <b>./s.Makefile</b>, and
<b>SCCS/s.Makefile</b>.</p>

<p>Early proposals contained the macro <b>NPROC</b> as a means of specifying that <i>make</i> should use <i>n</i> processes to do
the work required. While this feature is a valuable extension for many systems, it is not common usage and could require other
non-trivial extensions to makefile syntax. This extension is not required by this volume of IEEE&nbsp;Std&nbsp;1003.1-2001, but
could be provided as a compatible extension. The macro <b>PARALLEL</b> is used by some historical systems with essentially the same
meaning (but without using a name that is a common system limit value). It is suggested that implementors recognize the existing
use of <b>NPROC</b> and/or <b>PARALLEL</b> as extensions to <i>make</i>.</p>

<p>The default rules are based on System V. The default <b>CC=</b> value is <a href="../utilities/c99.html"><i>c99</i></a> instead
of <i>cc</i> because this volume of IEEE&nbsp;Std&nbsp;1003.1-2001 does not standardize the utility named <i>cc</i>. Thus, every
conforming application would be required to define <b>CC=</b> <a href="../utilities/c99.html"><i>c99</i></a> to expect to run.
There is no advantage conferred by the hope that the makefile might hit the &quot;preferred&quot; compiler because this cannot be
guaranteed to work. Also, since the portable makescript can only use the <a href="../utilities/c99.html"><i>c99</i></a> options, no
advantage is conferred in terms of what the script can do. It is a quality-of-implementation issue as to whether <a href=
"../utilities/c99.html"><i>c99</i></a> is as valuable as <i>cc</i>.</p>

<p>The <b>-d</b> option to <i>make</i> is frequently used to produce debugging information, but is too implementation-defined to
add to this volume of IEEE&nbsp;Std&nbsp;1003.1-2001.</p>

<p>The <b>-p</b> option is not passed in <i>MAKEFLAGS</i> on most historical implementations and to change this would cause many
implementations to break without sufficiently increased portability.</p>

<p>Commands that begin with a plus sign ( <tt>'+'</tt> ) are executed even if the <b>-n</b> option is present. Based on the GNU
version of <i>make</i>, the behavior of <b>-n</b> when the plus-sign prefix is encountered has been extended to apply to <b>-q</b>
and <b>-t</b> as well. However, the System V convention of forcing command execution with <b>-n</b> when the command line of a
target contains either of the strings <tt>"$(MAKE)"</tt> or <tt>"${MAKE}"</tt> has not been adopted. This functionality appeared in
early proposals, but the danger of this approach was pointed out with the following example of a portion of a makefile:</p>

<pre>
<tt>subdir:
    cd subdir; rm all_the_files; $(MAKE)
</tt>
</pre>

<p>The loss of the System V behavior in this case is well-balanced by the safety afforded to other makefiles that were not aware of
this situation. In any event, the command line plus-sign prefix can provide the desired functionality.</p>

<p>The double colon in the target rule format is supported in BSD systems to allow more than one target line containing the same
target name to have commands associated with it. Since this is not functionality described in the SVID or XPG3 it has been allowed
as an extension, but not mandated.</p>

<p>The default rules are provided with text specifying that the built-in rules shall be the same as if the listed set were used.
The intent is that implementations should be able to use the rules without change, but will be allowed to alter them in ways that
do not affect the primary behavior.</p>

<p>The best way to provide portable makefiles is to include all of the rules needed in the makefile itself. The rules provided use
only features provided by other portions of this volume of IEEE&nbsp;Std&nbsp;1003.1-2001. The default rules include rules for
optional commands in this volume of IEEE&nbsp;Std&nbsp;1003.1-2001. Only rules pertaining to commands that are provided are needed
in the default set of an implementation.</p>

<p>One point of discussion was whether to drop the default rules list from this volume of IEEE&nbsp;Std&nbsp;1003.1-2001. They
provide convenience, but do not enhance portability of applications. The prime benefit is in portability of users who wish to type
<i>make</i> <i>command</i> and have the command build from a <b>command.c</b> file.</p>

<p>The historical <i>MAKESHELL</i> feature was omitted. In some implementations it is used to let a user override the shell to be
used to run <i>make</i> commands. This was confusing; for a portable <i>make</i>, the shell should be chosen by the makefile writer
or specified on the <i>make</i> command line and not by a user running <i>make</i>.</p>

<p>The <i>make</i> utilities in most historical implementations process the prerequisites of a target in left-to-right order, and
the makefile format requires this. It supports the standard idiom used in many makefiles that produce <a href=
"../utilities/yacc.html"><i>yacc</i></a> programs; for example:</p>

<pre>
<tt>foo: y.tab.o lex.o main.o
    $(CC) $(CFLAGS) -o $</tt>@ <tt>t.tab.o lex.o main.o
</tt>
</pre>

<p>In this example, if <i>make</i> chose any arbitrary order, the <b>lex.o</b> might not be made with the correct <b>y.tab.h</b>.
Although there may be better ways to express this relationship, it is widely used historically. Implementations that desire to
update prerequisites in parallel should require an explicit extension to <i>make</i> or the makefile format to accomplish it, as
described previously.</p>

<p>The algorithm for determining a new entry for target rules is partially unspecified. Some historical <i>make</i>s allow blank,
empty, or comment lines within the collection of commands marked by leading &lt;tab&gt;s. A conforming makefile must ensure that
each command starts with a &lt;tab&gt;, but implementations are free to ignore blank, empty, and comment lines without triggering
the start of a new entry.</p>

<p>The ASYNCHRONOUS EVENTS section includes having SIGTERM and SIGHUP, along with the more traditional SIGINT and SIGQUIT, remove
the current target unless directed not to do so. SIGTERM and SIGHUP were added to parallel other utilities that have historically
cleaned up their work as a result of these signals. When <i>make</i> receives any signal other than SIGQUIT, it is required to
resend itself the signal it received so that it exits with a status that reflects the signal. The results from SIGQUIT are
partially unspecified because, on systems that create <b>core</b> files upon receipt of SIGQUIT, the <b>core</b> from <i>make</i>
would conflict with a <b>core</b> file from the command that was running when the SIGQUIT arrived. The main concern was to prevent
damaged files from appearing up-to-date when <i>make</i> is rerun.</p>

<p>The <b>.PRECIOUS</b> special target was extended to affect all targets globally (by specifying no prerequisites). The
<b>.IGNORE</b> and <b>.SILENT</b> special targets were extended to allow prerequisites; it was judged to be more useful in some
cases to be able to turn off errors or echoing for a list of targets than for the entire makefile. These extensions to <i>make</i>
in System V were made to match historical practice from the BSD <i>make</i>.</p>

<p>Macros are not exported to the environment of commands to be run. This was never the case in any historical <i>make</i> and
would have serious consequences. The environment is the same as the environment to <i>make</i> except that <i>MAKEFLAGS</i> and
macros defined on the <i>make</i> command line are added.</p>

<p>Some implementations do not use <a href="../functions/system.html"><i>system</i>()</a> for all command lines, as required by the
portable makefile format; as a performance enhancement, they select lines without shell metacharacters for direct execution by <a
href="../functions/execve.html"><i>execve</i>()</a>. There is no requirement that <a href=
"../functions/system.html"><i>system</i>()</a> be used specifically, but merely that the same results be achieved. The
metacharacters typically used to bypass the direct <a href="../functions/execve.html"><i>execve</i>()</a> execution have been any
of:</p>

<pre>
<tt>=  |  ^  (  )  ;  &amp;  &lt;  &gt;  *  ?  [  ]  :  $  `  '  "  \  \n
</tt>
</pre>

<p>The default in some advanced versions of <i>make</i> is to group all the command lines for a target and execute them using a
single shell invocation; the System V method is to pass each line individually to a separate shell. The single-shell method has the
advantages in performance and the lack of a requirement for many continued lines. However, converting to this newer method has
caused portability problems with many historical makefiles, so the behavior with the POSIX makefile is specified to be the same as
that of System V. It is suggested that the special target <b>.ONESHELL</b> be used as an implementation extension to achieve the
single-shell grouping for a target or group of targets.</p>

<p>Novice users of <i>make</i> have had difficulty with the historical need to start commands with a &lt;tab&gt;. Since it is often
difficult to discern differences between &lt;tab&gt;s and &lt;space&gt;s on terminals or printed listings, confusing bugs can
arise. In early proposals, an attempt was made to correct this problem by allowing leading &lt;blank&gt;s instead of &lt;tab&gt;s.
However, implementors reported many makefiles that failed in subtle ways following this change, and it is difficult to implement a
<i>make</i> that unambiguously can differentiate between macro and command lines. There is extensive historical practice of
allowing leading spaces before macro definitions. Forcing macro lines into column 1 would be a significant backwards-compatibility
problem for some makefiles. Therefore, historical practice was restored.</p>

<p>The System V INCLUDE feature was considered, but not included. This would treat a line that began in the first column and
contained INCLUDE &lt;<i>filename</i>&gt; as an indication to read &lt;<i>filename</i>&gt; at that point in the makefile. This is
difficult to use in a portable way, and it raises concerns about nesting levels and diagnostics. System V, BSD, GNU, and others
have used different methods for including files.</p>

<p>The System V dynamic dependency feature was not included. It would support:</p>

<pre>
<tt>cat: $$@.c
</tt>
</pre>

<p>that would expand to;</p>

<pre>
<tt>cat: cat.c
</tt>
</pre>

<p>This feature exists only in the new version of System V <i>make</i> and, while useful, is not in wide usage. This means that
macros are expanded twice for prerequisites: once at makefile parse time and once at target update time.</p>

<p>Consideration was given to adding metarules to the POSIX <i>make</i>. This would make <b>%.o:&nbsp;%.c</b> the same as
<b>.c.o:</b>. This is quite useful and available from some vendors, but it would cause too many changes to this <i>make</i> to
support. It would have introduced rule chaining and new substitution rules. However, the rules for target names have been set to
reserve the <tt>'%'</tt> and <tt>''</tt> characters. These are traditionally used to implement metarules and quoting of target
names, respectively. Implementors are strongly encouraged to use these characters only for these purposes.</p>

<p>A request was made to extend the suffix delimiter character from a period to any character. The metarules feature in newer
<i>make</i>s solves this problem in a more general way. This volume of IEEE&nbsp;Std&nbsp;1003.1-2001 is staying with the more
conservative historical definition.</p>

<p>The standard output format for the <b>-p</b> option is not described because it is primarily a debugging option and because the
format is not generally useful to programs. In historical implementations the output is not suitable for use in generating
makefiles. The <b>-p</b> format has been variable across historical implementations. Therefore, the definition of <b>-p</b> was
only to provide a consistently named option for obtaining <i>make</i> script debugging information.</p>

<p>Some historical implementations have not cleared the suffix list with <b>-r</b>.</p>

<p>Implementations should be aware that some historical applications have intermixed <i>target_name</i> and <i>macro</i>=
<i>value</i> operands on the command line, expecting that all of the macros are processed before any of the targets are dealt with.
Conforming applications do not do this, but some backwards-compatibility support may be warranted.</p>

<p>Empty inference rules are specified with a semicolon command rather than omitting all commands, as described in an early
proposal. The latter case has no traditional meaning and is reserved for implementation extensions, such as in GNU <i>make</i>.</p>
</blockquote>

<h4><a name="tag_04_84_19"></a>FUTURE DIRECTIONS</h4>

<blockquote>
<p>None.</p>
</blockquote>

<h4><a name="tag_04_84_20"></a>SEE ALSO</h4>

<blockquote>
<p><a href="xcu_chap02.html#tag_02"><i>Shell Command Language</i></a>, <a href="ar.html"><i>ar</i></a>, <a href=
"c99.html"><i>c99</i></a>, <a href="get.html"><i>get</i></a>, <a href="lex.html"><i>lex</i></a>, <a href=
"sccs.html"><i>sccs</i></a>, <a href="sh.html"><i>sh</i></a>, <a href="yacc.html"><i>yacc</i></a>, the System Interfaces volume
of IEEE&nbsp;Std&nbsp;1003.1-2001, <i>exec</i>, <a href="../functions/system.html"><i>system</i>()</a></p>
</blockquote>

<h4><a name="tag_04_84_21"></a>CHANGE HISTORY</h4>

<blockquote>
<p>First released in Issue 2.</p>
</blockquote>

<h4><a name="tag_04_84_22"></a>Issue 5</h4>

<blockquote>
<p>The FUTURE DIRECTIONS section is added.</p>
</blockquote>

<h4><a name="tag_04_84_23"></a>Issue 6</h4>

<blockquote>
<p>This utility is marked as part of the Software Development Utilities option.</p>

<p>The Open Group Corrigendum U029/1 is applied, correcting a typographical error in the SPECIAL TARGETS section.</p>

<p>In the ENVIRONMENT VARIABLES section, the <i>PROJECTDIR</i> description is updated from &quot;otherwise, the home directory of a
user of that name is examined&quot; to &quot;otherwise, the value of <i>PROJECTDIR</i> is treated as a user name and that user's initial
working directory is examined&quot;.</p>

<p>It is specified whether the command line is related to the makefile or to the <i>make</i> command, and the macro processing
rules are updated to align with the IEEE&nbsp;P1003.2b draft standard.</p>

<p>The normative text is reworded to avoid use of the term &quot;must&quot; for application requirements.</p>

<p>PASC Interpretation 1003.2 #193 is applied.</p>
</blockquote>

<div class="box"><em>End of informative text.</em></div>

<hr size="2" noshade>
<center><font size="2"><!--footer start-->
UNIX &reg; is a registered Trademark of The Open Group.<br>
POSIX &reg; is a registered Trademark of The IEEE.<br>
[ <a href="../mindex.html">Main Index</a> | <a href="../basedefs/contents.html">XBD</a> | <a href=
"../utilities/contents.html">XCU</a> | <a href="../functions/contents.html">XSH</a> | <a href="../xrat/contents.html">XRAT</a>
]</font></center>

<!--footer end-->
<hr size="2" noshade>
</body>
</html>

