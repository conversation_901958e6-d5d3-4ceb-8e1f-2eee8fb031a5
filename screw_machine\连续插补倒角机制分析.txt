===============================================================================
                        连续插补倒角机制分析 - 为什么三段衔接没有倒角
===============================================================================

【问题现象】

=== 用户发现的问题 ===
```
三段插补轨迹：
第一段：MOVEABS(start_safe_z) AXIS(3)     ' 单轴运动
第二段：MOVEABS(end_x, end_safe_z)        ' 多轴运动  
第三段：MOVEABS(end_z) AXIS(3)            ' 单轴运动

现象：衔接处没有倒角，很突然

但是下面这样就可以：
MERGE = ON
CORNER_MODE = 32
ZSMOOTH = 5
MOVE(0, 20)                               ' 多轴运动
MOVECIRC2ABS(50, 40, 100, 20)             ' 多轴运动
MOVE(0, -20)                              ' 多轴运动

现象：衔接处有倒角，很平滑
```

【根本原因分析】

=== 连续插补的基本机制 ===
```
正运动控制器的连续插补（MERGE=ON）机制：
1. 只有相同类型的运动才能进行连续插补
2. 单轴运动和多轴运动属于不同类型
3. 不同类型的运动之间无法进行连续插补和倒角处理

技术原理：
- 多轴运动：使用插补算法，可以进行轨迹融合
- 单轴运动：直接控制单个轴，无法与其他轴协调
- 倒角功能：需要多轴协调运动才能实现
```

=== 运动类型分类 ===
```
多轴运动（可连续插补）：
✅ MOVE(x, z)                    ' 直线插补
✅ MOVEABS(x, z)                 ' 绝对位置直线插补
✅ MOVECIRC2ABS(x1, z1, x2, z2)  ' 圆弧插补
✅ MOVEARC(...)                  ' 各种圆弧插补

单轴运动（无法连续插补）：
❌ MOVE(distance) AXIS(n)        ' 单轴相对运动
❌ MOVEABS(position) AXIS(n)     ' 单轴绝对运动
❌ 单独的轴控制指令

混合情况的问题：
❌ 多轴运动 → 单轴运动 → 多轴运动
   无法在衔接处进行连续插补和倒角
```

【解决方案对比】

=== 方案1：全部使用多轴运动（推荐）===
```basic
'确保当前位置正确，然后使用多轴运动
IF ABS(DPOS(0) - start_x) > 0.1 THEN
    MOVEABS(start_x, DPOS(3))           ' 预定位到start_x
ENDIF

'第一段：垂直抬Z（多轴运动，但X不变）
MOVEABS(start_x, start_safe_z)          ' X=start_x, Z=start_safe_z

'第二段：水平移动（多轴运动）
MOVEABS(end_x, end_safe_z)              ' X=end_x, Z=end_safe_z

'第三段：垂直下降（多轴运动，但X不变）
MOVEABS(end_x, end_z)                   ' X=end_x, Z=end_z

优势：
✅ 全部是多轴运动，可以连续插补
✅ 支持倒角功能（CORNER_MODE=32）
✅ 轨迹平滑，无突变
✅ 符合连续插补机制
```

=== 方案2：使用相对运动（备选）===
```basic
'第一段：垂直抬Z
MOVE(0, start_safe_z - start_z)         ' 相对运动，只移动Z

'第二段：水平移动  
MOVE(end_x - start_x, end_safe_z - start_safe_z)

'第三段：垂直下降
MOVE(0, end_z - end_safe_z)             ' 相对运动，只移动Z

优势：
✅ 全部是多轴运动
✅ 可以连续插补和倒角
✅ 逻辑相对简单

缺点：
❌ 需要精确计算相对距离
❌ 累积误差可能影响精度
```

=== 方案3：分段设置连续插补（不推荐）===
```basic
'第一段：单独执行
MERGE = OFF
MOVEABS(start_safe_z) AXIS(3)
WAIT IDLE

'第二段和第三段：连续插补
MERGE = ON
CORNER_MODE = 32
MOVEABS(end_x, end_safe_z)
MOVEABS(end_x, end_z)
WAIT IDLE
MERGE = OFF

缺点：
❌ 第一段和第二段之间仍有突变
❌ 增加了等待时间，降低效率
❌ 逻辑复杂，不利于维护
```

【技术细节分析】

=== 连续插补的条件 ===
```
正运动控制器连续插补的必要条件：
1. MERGE = ON                           ' 开启连续插补
2. 相同的轴组（BASE设置）
3. 相同类型的运动指令
4. 无中间等待指令（WAIT IDLE等）
5. 运动缓冲区有足够空间

倒角功能的条件：
1. 满足连续插补的所有条件
2. CORNER_MODE = 32                     ' 开启自动倒角
3. ZSMOOTH > 0                          ' 设置倒角半径
4. 轨迹拐角角度满足倒角条件
```

=== 轴组和插补的关系 ===
```
当前设置：BASE(0, 3)                   ' X轴和Z轴插补

多轴运动的插补：
MOVEABS(x, z) → 控制器计算X轴和Z轴的协调运动
- 两轴同时启动
- 按比例分配速度
- 同时到达终点
- 可以进行轨迹融合

单轴运动的特点：
MOVEABS(z) AXIS(3) → 只控制Z轴运动
- 只有Z轴运动
- X轴保持静止
- 无法与其他轴协调
- 无法进行轨迹融合
```

【实际应用建议】

=== 螺丝机三段轨迹优化 ===
```
推荐的实现方式：

1. 预定位阶段：
   确保当前X位置在start_x
   IF ABS(DPOS(0) - start_x) > 0.1 THEN
       MOVEABS(start_x, DPOS(3))       ' 水平移动到起始X位置
   ENDIF

2. 三段连续插补：
   MERGE = ON                          ' 开启连续插补
   CORNER_MODE = 32                    ' 开启自动倒角
   ZSMOOTH = 5                         ' 设置倒角半径5mm
   
   MOVEABS(start_x, start_safe_z)      ' 第一段：垂直抬Z
   MOVEABS(end_x, end_safe_z)          ' 第二段：水平移动
   MOVEABS(end_x, end_z)               ' 第三段：垂直下降
   
   WAIT IDLE                           ' 等待完成
   MERGE = OFF                         ' 关闭连续插补

3. 效果验证：
   - 观察轨迹是否平滑
   - 检查衔接处是否有倒角
   - 监控速度曲线连续性
```

=== 参数调优建议 ===
```
倒角参数调整：
ZSMOOTH = 2~10                          ' 根据精度要求调整
- 值越大，倒角越明显，轨迹越平滑
- 值越小，倒角越小，精度越高

速度参数调整：
FORCE_SPEED = 50~100                    ' 根据机械性能调整
ACCEL = 500~2000                        ' 加速度
DECEL = 500~2000                        ' 减速度

平滑参数调整：
SRAMP = 50~200                          ' S曲线时间
VP_MODE = 7                             ' SS曲线，最平滑
```

【验证方法】

=== 示波器监控验证 ===
```
监控信号：
VP_SPEED(0) - X轴速度
VP_SPEED(3) - Z轴速度

连续插补成功的特征：
✅ 速度曲线连续，无断点
✅ 衔接处速度平滑过渡
✅ 无突然的速度跳变

倒角成功的特征：
✅ 拐角处速度不降到0
✅ 轨迹呈圆弧过渡
✅ 整体运动更加平滑
```

=== 实际轨迹验证 ===
```
观察要点：
1. 第一段：应该是垂直直线（X不变，Z上升）
2. 第二段：应该是水平移动（Z不变或变化，X移动）
3. 第三段：应该是垂直直线（X不变，Z下降）
4. 衔接处：应该有圆弧倒角，不是尖角

测试方法：
- 使用低速运动，便于观察
- 记录关键点的坐标
- 对比理论轨迹和实际轨迹
```

【总结】

连续插补倒角的关键要点：
✅ **运动类型一致**：全部使用多轴运动，避免单轴运动
✅ **正确的参数设置**：MERGE=ON, CORNER_MODE=32, ZSMOOTH>0
✅ **合理的轨迹设计**：确保起始位置正确，避免不必要的预动作
✅ **无中间等待**：不要在连续插补过程中插入WAIT IDLE
✅ **参数优化**：根据实际需求调整倒角半径和速度参数

通过使用全多轴运动的方案，三段插补轨迹现在可以实现：
- 真正的垂直直线运动（通过位置控制实现）
- 平滑的轨迹衔接（通过连续插补实现）
- 圆弧倒角过渡（通过CORNER_MODE=32实现）
- 高效的运动性能（通过参数优化实现）

这样既保证了轨迹的正确性，又实现了平滑的连续插补效果。

===============================================================================
