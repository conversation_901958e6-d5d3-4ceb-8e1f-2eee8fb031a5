===============================================================================
                        4x2螺丝布局说明 - Y轴跨度大的新布局
===============================================================================

【布局概述】

=== 新布局特点 ===
```
布局方式：4行2列（4x2阵列）
Y轴跨度：大跨度分布，充分利用Y轴行程
螺丝总数：8个螺丝/侧
X轴分布：2个位置（100mm, 200mm）
Y轴分布：4个位置，间距50mm
```

=== 布局优势 ===
```
✅ Y轴跨度大：充分利用Y轴的移动范围
✅ 间距均匀：每行间距50mm，分布均匀
✅ 适配性好：适合长条形工件
✅ 效率高：减少X轴移动距离
✅ 布局清晰：4行2列，结构简单明了
```

【具体布局数据】

=== 左侧工件布局 ===
```
左侧螺丝布局（4行2列）：
第一行（Y=50mm）：  螺丝1(100,50)   螺丝2(200,50)
第二行（Y=100mm）： 螺丝3(100,100)  螺丝4(200,100)
第三行（Y=150mm）： 螺丝5(100,150)  螺丝6(200,150)
第四行（Y=200mm）： 螺丝7(100,200)  螺丝8(200,200)

Y轴跨度：50mm - 200mm = 150mm
行间距：50mm（均匀分布）
用户位置：Y=5mm
```

=== 右侧工件布局 ===
```
右侧螺丝布局（4行2列）：
第一行（Y=250mm）： 螺丝1(100,250)  螺丝2(200,250)
第二行（Y=300mm）： 螺丝3(100,300)  螺丝4(200,300)
第三行（Y=350mm）： 螺丝5(100,350)  螺丝6(200,350)
第四行（Y=400mm）： 螺丝7(100,400)  螺丝8(200,400)

Y轴跨度：250mm - 400mm = 150mm
行间距：50mm（均匀分布）
用户位置：Y=5mm
```

=== 整体Y轴分布 ===
```
Y轴坐标系统：
用户位置：     5mm（左右共用）
左侧工件：    50mm - 200mm（跨度150mm）
右侧工件：   250mm - 400mm（跨度150mm）
总Y轴行程：   5mm - 400mm（395mm）

优势：
✅ 充分利用Y轴行程
✅ 左右工件分离明确
✅ 用户操作位置统一
✅ Y轴跨度大，适合长工件
```

【数据结构】

=== TABLE数组存储 ===
```basic
左侧螺丝数据（起始地址0）：
TABLE(0)=100, TABLE(1)=50,  TABLE(2)=30    ' 螺丝1(100,50,30)
TABLE(3)=200, TABLE(4)=50,  TABLE(5)=30    ' 螺丝2(200,50,30)
TABLE(6)=100, TABLE(7)=100, TABLE(8)=30    ' 螺丝3(100,100,30)
TABLE(9)=200, TABLE(10)=100,TABLE(11)=30   ' 螺丝4(200,100,30)
TABLE(12)=100,TABLE(13)=150,TABLE(14)=30   ' 螺丝5(100,150,30)
TABLE(15)=200,TABLE(16)=150,TABLE(17)=30   ' 螺丝6(200,150,30)
TABLE(18)=100,TABLE(19)=200,TABLE(20)=30   ' 螺丝7(100,200,30)
TABLE(21)=200,TABLE(22)=200,TABLE(23)=30   ' 螺丝8(200,200,30)

右侧螺丝数据（起始地址200）：
TABLE(200)=100,TABLE(201)=250,TABLE(202)=30  ' 螺丝1(100,250,30)
TABLE(203)=200,TABLE(204)=250,TABLE(205)=30  ' 螺丝2(200,250,30)
TABLE(206)=100,TABLE(207)=300,TABLE(208)=30  ' 螺丝3(100,300,30)
TABLE(209)=200,TABLE(210)=300,TABLE(211)=30  ' 螺丝4(200,300,30)
TABLE(212)=100,TABLE(213)=350,TABLE(214)=30  ' 螺丝5(100,350,30)
TABLE(215)=200,TABLE(216)=350,TABLE(217)=30  ' 螺丝6(200,350,30)
TABLE(218)=100,TABLE(219)=400,TABLE(220)=30  ' 螺丝7(100,400,30)
TABLE(221)=200,TABLE(222)=400,TABLE(223)=30  ' 螺丝8(200,400,30)
```

【Y轴移动优化】

=== Y轴移动序列 ===
```
左侧任务的Y轴移动：
1. 任务开始：5mm → 50mm（第一个螺丝）
2. 螺丝1→2：50mm → 50mm（同行，无需移动）
3. 螺丝2→3：50mm → 100mm（移动50mm）
4. 螺丝3→4：100mm → 100mm（同行，无需移动）
5. 螺丝4→5：100mm → 150mm（移动50mm）
6. 螺丝5→6：150mm → 150mm（同行，无需移动）
7. 螺丝6→7：150mm → 200mm（移动50mm）
8. 螺丝7→8：200mm → 200mm（同行，无需移动）
9. 任务完成：200mm → 5mm（回用户位置）

总Y轴移动：45mm + 50mm + 50mm + 50mm + 195mm = 390mm
```

=== 移动效率分析 ===
```
4x2布局优势：
✅ 同行螺丝无需Y轴移动：50%的螺丝无需Y轴移动
✅ 行间移动距离固定：每次移动50mm，预测性好
✅ X轴移动距离短：只有100mm，效率高
✅ 总移动距离合理：充分利用Y轴行程

对比2x4布局：
- 2x4：Y轴移动次数少，但X轴移动距离长
- 4x2：Y轴移动次数多，但X轴移动距离短
- 4x2更适合Y轴行程大的机械结构
```

【工作流程】

=== 左侧4x2作业流程 ===
```
1. 任务开始：Y轴移动到第一行（50mm）
2. 第一行作业：
   - 螺丝1(100,50)：Y轴已在位置，直接作业
   - 螺丝2(200,50)：Y轴不动，X轴移动100mm
3. 第二行作业：
   - Y轴移动到第二行（100mm）
   - 螺丝3(100,100)：X轴回到100mm位置
   - 螺丝4(200,100)：X轴移动到200mm
4. 第三行作业：
   - Y轴移动到第三行（150mm）
   - 螺丝5(100,150)：X轴回到100mm位置
   - 螺丝6(200,150)：X轴移动到200mm
5. 第四行作业：
   - Y轴移动到第四行（200mm）
   - 螺丝7(100,200)：X轴回到100mm位置
   - 螺丝8(200,200)：X轴移动到200mm
6. 任务完成：Y轴回到用户位置（5mm）
```

【显示信息】

=== 数据设置显示 ===
```
"数据设置完成（4x2阵列，Y轴跨度大）"
"左侧螺丝数量：8（4行2列阵列）"
"右侧螺丝数量：8（4行2列阵列）"
"最大支持螺丝数量：64个/侧"
"左侧Y坐标分布：50mm,100mm,150mm,200mm（跨度150mm）"
"右侧Y坐标分布：250mm,300mm,350mm,400mm（跨度150mm）"
"左侧用户位置：5mm，右侧用户位置：5mm"
```

=== 任务执行显示 ===
```
左侧任务：
"开始左侧打螺丝任务，螺丝数量：8"
"左Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：50mm"

右侧任务：
"开始右侧打螺丝任务，螺丝数量：8"
"右Y轴从用户位置直接移动到第一个螺丝的实际Y坐标：250mm"
```

【配置调整】

=== 当前螺丝数量设置 ===
```basic
用户已设置：
left_screw_num = 3              ' 左侧3个螺丝（测试用）
right_screw_num = 3             ' 右侧3个螺丝（测试用）

完整设置：
CALL SetScrewCount(8, 8)        ' 设置左右各8个螺丝（4x2完整布局）
```

=== Y轴跨度调整 ===
```basic
如需调整Y轴跨度，修改对应的Y坐标：

左侧更大跨度（例如跨度200mm）：
TABLE(1) = 30    ' 第一行：30mm
TABLE(7) = 100   ' 第二行：100mm
TABLE(13) = 170  ' 第三行：170mm
TABLE(19) = 230  ' 第四行：230mm

右侧相应调整：
TABLE(201) = 270  ' 第一行：270mm
TABLE(207) = 340  ' 第二行：340mm
TABLE(213) = 410  ' 第三行：410mm
TABLE(219) = 480  ' 第四行：480mm
```

=== X轴位置调整 ===
```basic
如需调整X轴位置，修改对应的X坐标：

更大X轴间距（例如150mm间距）：
第一列：X = 75mm
第二列：X = 225mm

修改方法：
TABLE(0) = 75, TABLE(3) = 225   ' 第一行
TABLE(6) = 75, TABLE(9) = 225   ' 第二行
TABLE(12) = 75, TABLE(15) = 225 ' 第三行
TABLE(18) = 75, TABLE(21) = 225 ' 第四行
```

【适用场景】

=== 4x2布局适合的工件 ===
```
✅ 长条形工件：Y方向尺寸大的工件
✅ 大跨度需求：需要充分利用Y轴行程
✅ 少列多行：螺丝分布为少列多行的情况
✅ Y轴行程大：机械结构Y轴行程较大
```

=== 与2x4布局的选择 ===
```
选择4x2布局的情况：
✅ 工件Y方向尺寸大
✅ Y轴行程充足（>400mm）
✅ 希望减少X轴移动距离
✅ 螺丝分布适合4行2列

选择2x4布局的情况：
✅ 工件X方向尺寸大
✅ X轴行程充足
✅ 希望减少Y轴移动次数
✅ 螺丝分布适合2行4列
```

【测试验证】

=== 验证要点 ===
```
1. Y轴跨度验证：
   - 确认Y轴能够到达所有位置（5mm-400mm）
   - 验证Y轴移动精度和重复性

2. 布局验证：
   - 确认螺丝孔位置与TABLE数组一致
   - 验证4行2列的布局正确

3. 移动效率验证：
   - 测试同行螺丝的Y轴不移动
   - 验证行间移动的50mm距离

4. 完整流程验证：
   - 测试8个螺丝的完整作业流程
   - 验证Y轴移动序列正确
```

=== 测试方法 ===
```basic
1. 设置完整螺丝数量：
   CALL SetScrewCount(8, 8)

2. 观察Y轴移动：
   - 按下IN0，观察左Y轴移动到50mm
   - 验证同行螺丝Y轴不移动
   - 检查行间移动距离为50mm

3. 验证布局数据：
   - 手动移动到各个螺丝位置
   - 对比实际位置与TABLE数组数据
   - 确认4x2布局正确

4. 完整作业测试：
   - 运行完整的8螺丝作业流程
   - 验证Y轴跨度利用充分
   - 检查作业效率提升
```

【总结】

4x2螺丝布局特点：
✅ **Y轴跨度大**：充分利用Y轴行程，跨度150mm
✅ **布局合理**：4行2列，适合长条形工件
✅ **移动效率高**：同行螺丝无需Y轴移动
✅ **间距均匀**：50mm行间距，分布均匀
✅ **适配性好**：适合Y轴行程大的机械结构
✅ **扩展性强**：可根据需要调整跨度和间距

这个4x2布局为螺丝机提供了更大的Y轴跨度利用率，
特别适合长条形工件和Y轴行程充足的应用场景。

===============================================================================
