'验证修复结果的测试文件
'检查所有变量名和函数名是否在18字符限制内

'测试全局变量声明（无"="赋值）
GLOBAL test_var1
GLOBAL test_var2
GLOBAL test_var3

'测试函数名长度（17字符以内）
GLOBAL SUB TestShortName()
    PRINT "函数名测试通过"
END SUB

'测试变量初始化
GLOBAL SUB TestInit()
    test_var1 = 10
    test_var2 = 20  
    test_var3 = 30
    PRINT "变量初始化测试通过"
    PRINT "test_var1 =", test_var1
    PRINT "test_var2 =", test_var2
    PRINT "test_var3 =", test_var3
END SUB

'主程序
CALL TestInit()
CALL TestShortName()
PRINT "所有测试通过，修复成功！"
END
