'===============================================================================
'                        输入检测诊断程序 - 简单测试输入电平
'===============================================================================

'变量定义
DIM i
DIM input_state(16)
DIM last_state(16)
DIM count

'初始化
count = 0
PRINT "==============================================================================="
PRINT "                        输入检测诊断程序启动"
PRINT "==============================================================================="
PRINT "程序功能：实时显示输入状态，检测上升沿"
PRINT "监控引脚：IN0 - IN15"
PRINT "显示格式：[引脚号] 当前状态 | 上升沿检测"
PRINT "==============================================================================="

'初始化输入状态
FOR i = 0 TO 15
    last_state(i) = IN(i)
NEXT i

PRINT "输入状态初始化完成，开始监控..."
PRINT ""

'主循环
WHILE 1
    count = count + 1
    
    '每100次循环显示一次状态（约5秒）
    IF count MOD 100 = 0 THEN
        PRINT "=== 输入状态监控 (", count/100, ") ==="
        FOR i = 0 TO 15
            input_state(i) = IN(i)
            PRINT "IN", i, " = ", input_state(i)
        NEXT i
        PRINT ""
    ENDIF
    
    '检测上升沿
    FOR i = 0 TO 15
        input_state(i) = IN(i)
        
        '检测上升沿 (0 → 1)
        IF (input_state(i) = 1) AND (last_state(i) = 0) THEN
            PRINT "*** 检测到 IN", i, " 上升沿！当前时间：", count * 50, "ms ***"
            
            '简单的响应测试
            IF i = 0 THEN
                PRINT "IN0触发：执行简单测试"
                PRINT "设置轴参数..."
                BASE(0, 1)
                DPOS = 0, 0
                SPEED = 50, 50
                ACCEL = 500, 500
                DECEL = 500, 500
                PRINT "执行简单运动：MOVE(10, 10)"
                MOVE(10, 10)
                WAIT IDLE(0)
                WAIT IDLE(1)
                PRINT "运动完成，当前位置：(", DPOS(0), ", ", DPOS(1), ")"
                PRINT ""
            ELSEIF i = 1 THEN
                PRINT "IN1触发：显示系统信息"
                PRINT "控制器型号：", ?*model
                PRINT "固件版本：", ?*version
                PRINT "系统时间：", count * 50, "ms"
                PRINT ""
            ELSEIF i = 2 THEN
                PRINT "IN2触发：输出测试"
                PRINT "设置输出OUT0 = 1"
                OUT(0) = 1
                DELAY 1000
                PRINT "设置输出OUT0 = 0"
                OUT(0) = 0
                PRINT ""
            ELSE
                PRINT "IN", i, "触发：通用响应测试"
                PRINT "触发时间：", count * 50, "ms"
                PRINT "输入引脚：IN", i
                PRINT ""
            ENDIF
        ENDIF
        
        '检测下降沿 (1 → 0)
        IF (input_state(i) = 0) AND (last_state(i) = 1) THEN
            PRINT "--- 检测到 IN", i, " 下降沿 ---"
        ENDIF
        
        last_state(i) = input_state(i)
    NEXT i
    
    DELAY 50                        ' 延时50ms
WEND

END
