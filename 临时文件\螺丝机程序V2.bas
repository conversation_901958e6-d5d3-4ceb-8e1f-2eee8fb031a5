'=============================================================================
' 螺丝机程序V2（全新版本，避免函数名冲突）
' 四轴控制(X,Y1,Y2,Z) + 电批Modbus通讯 + 圆弧插补运动
' 使用全新的函数名，避免与之前程序冲突
'=============================================================================

'全局变量定义
GLOBAL g_SysStatus                  ' 系统状态：0-待机，1-运行中，2-回零中
GLOBAL g_AxisHome(4)                ' 各轴回零状态：0-未回零，1-回零中，2-已回零，3-失败
GLOBAL g_LeftScrewNum               ' 左侧螺丝点位数量
GLOBAL g_RightScrewNum              ' 右侧螺丝点位数量
GLOBAL g_CurScrew                   ' 当前打螺丝序号

'螺丝位置数据存储
GLOBAL g_LeftStart = 0              ' 左侧螺丝位置数据起始地址
GLOBAL g_RightStart = 100           ' 右侧螺丝位置数据起始地址

'吸螺丝位置
GLOBAL g_PickX = 50                 ' 吸螺丝位置X
GLOBAL g_PickY = 150                ' 吸螺丝位置Y  
GLOBAL g_PickZ = 5                  ' 吸螺丝位置Z

'电批参数
GLOBAL g_ScrewAddr = 10             ' 电批设备地址

'主程序
CALL ScrewInit()
CALL AxisSetup()
CALL ComSetup()
CALL DataSetup()

PRINT "=== 螺丝机控制系统V2启动 ==="
PRINT "功能说明："
PRINT "IN0 - 左侧开始打螺丝"
PRINT "IN1 - 右侧开始打螺丝"
PRINT "IN2 - 系统回零"
PRINT "IN3 - 急停"
PRINT "OP0 - 吸螺丝控制(低电平有效)"

'主循环
WHILE 1
    CALL InputScan()
    CALL StatusUpdate()
    DELAY(50)
WEND
END

'================ 系统初始化 ================
GLOBAL SUB ScrewInit()
    g_SysStatus = 0
    g_LeftScrewNum = 4              ' 默认左侧4个螺丝
    g_RightScrewNum = 4             ' 默认右侧4个螺丝
    g_CurScrew = 0
    
    '初始化回零状态
    FOR i = 0 TO 3
        g_AxisHome(i) = 0
    NEXT
    
    PRINT "系统初始化完成"
END SUB

'================ 轴参数设置 ================
GLOBAL SUB AxisSetup()
    '四轴配置：X轴(0), Y1轴(1), Y2轴(2), Z轴(3)
    BASE(0, 1, 2, 3)
    ATYPE = 1, 1, 1, 1              ' 步进电机开环脉冲控制
    UNITS = 1000, 1000, 1000, 1000  ' 脉冲当量 1000脉冲/mm
    SPEED = 100, 100, 100, 50       ' 运动速度 mm/s
    ACCEL = 1000, 1000, 1000, 500   ' 加速度 mm/s²
    DECEL = 1000, 1000, 1000, 500   ' 减速度 mm/s²
    CREEP = 10, 10, 10, 5           ' 回零爬行速度
    
    '限位和原点信号配置
    FWD_IN = 8, 9, 10, 11           ' 正限位 IN8-IN11
    REV_IN = 12, 13, 14, 15         ' 负限位 IN12-IN15
    DATUM_IN = 16, 17, 18, 19       ' 原点开关 IN16-IN19
    
    '信号反转(根据实际硬件调整)
    FOR i = 8 TO 19
        INVERT_IN(i, ON)            ' 常开信号反转
    NEXT
    
    '连续插补设置
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 2                 ' 拐角减速模式
    
    PRINT "轴参数设置完成"
END SUB

'================ 通讯设置 ================
GLOBAL SUB ComSetup()
    '配置RS485串口为Modbus主站
    SETCOM(115200, 8, 1, 0, 0, 14)  ' 115200波特率，Modbus主站模式
    ADDRESS = 1                      ' 控制器地址
    
    '设置Modbus连接
    MODBUSM_DES(g_ScrewAddr, 500, 1) ' 连接电批，超时500ms
    
    PRINT "通讯设置完成"
END SUB

'================ 数据设置 ================
GLOBAL SUB DataSetup()
    '左侧螺丝位置数据(2x2阵列示例)
    TABLE(0) = 100   : TABLE(1) = 80   : TABLE(2) = 0    ' 螺丝1
    TABLE(3) = 150   : TABLE(4) = 80   : TABLE(5) = 0    ' 螺丝2
    TABLE(6) = 100   : TABLE(7) = 120  : TABLE(8) = 0    ' 螺丝3
    TABLE(9) = 150   : TABLE(10) = 120 : TABLE(11) = 0   ' 螺丝4
    
    '右侧螺丝位置数据(2x2阵列示例)
    TABLE(100) = 100 : TABLE(101) = 220 : TABLE(102) = 0  ' 螺丝1
    TABLE(103) = 150 : TABLE(104) = 220 : TABLE(105) = 0  ' 螺丝2
    TABLE(106) = 100 : TABLE(107) = 260 : TABLE(108) = 0  ' 螺丝3
    TABLE(109) = 150 : TABLE(110) = 260 : TABLE(111) = 0  ' 螺丝4
    
    PRINT "数据设置完成"
    PRINT "左侧螺丝数量：", g_LeftScrewNum
    PRINT "右侧螺丝数量：", g_RightScrewNum
END SUB

'================ 输入扫描 ================
GLOBAL SUB InputScan()
    '左侧开始按键
    IF SCAN_EVENT(IN(0)) > 0 THEN
        IF g_SysStatus = 0 AND CheckAxisHome() = 1 THEN
            PRINT "开始左侧打螺丝作业"
            CALL LeftWork()
        ELSE
            PRINT "系统未就绪或未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '右侧开始按键  
    IF SCAN_EVENT(IN(1)) > 0 THEN
        IF g_SysStatus = 0 AND CheckAxisHome() = 1 THEN
            PRINT "开始右侧打螺丝作业"
            CALL RightWork()
        ELSE
            PRINT "系统未就绪或未回零，无法开始作业"
        ENDIF
    ENDIF
    
    '系统回零
    IF SCAN_EVENT(IN(2)) > 0 THEN
        IF g_SysStatus = 0 THEN
            PRINT "开始系统回零"
            CALL HomeWork()
        ELSE
            PRINT "系统运行中，无法回零"
        ENDIF
    ENDIF
    
    '急停
    IF SCAN_EVENT(IN(3)) > 0 THEN
        PRINT "急停触发！"
        RAPIDSTOP(2)
        g_SysStatus = 0
        OP(0, OFF)                  ' 关闭吸螺丝
    ENDIF
END SUB

'================ 检查轴回零状态 ================
GLOBAL SUB CheckAxisHome()
    FOR i = 0 TO 3
        IF g_AxisHome(i) <> 2 THEN
            RETURN 0                ' 有轴未回零
        ENDIF
    NEXT
    RETURN 1                        ' 所有轴已回零
END SUB

'================ 状态更新 ================
GLOBAL SUB StatusUpdate()
    '更新Modbus寄存器供HMI显示
    MODBUS_REG(0) = g_SysStatus
    MODBUS_REG(1) = g_LeftScrewNum
    MODBUS_REG(2) = g_RightScrewNum
    MODBUS_REG(3) = g_CurScrew
    
    '更新各轴回零状态
    FOR i = 0 TO 3
        MODBUS_REG(10 + i) = g_AxisHome(i)
    NEXT
    
    '更新各轴当前位置
    FOR i = 0 TO 3
        MODBUS_IEEE(20 + i) = DPOS(i)
    NEXT
END SUB

'================ 回零作业 ================
GLOBAL SUB HomeWork()
    g_SysStatus = 2                 ' 设置为回零状态
    PRINT "开始四轴回零..."
    
    '按Z-Y-X顺序回零，避免碰撞
    FOR i = 3 TO 0 STEP -1
        PRINT "开始轴", i, "回零"
        g_AxisHome(i) = 1           ' 设置为回零中
        
        BASE(i)
        DATUM(0) AXIS(i)            ' 清除错误状态
        DELAY(10)
        DATUM(3)                    ' 正向找原点回零
        
        WAIT UNTIL IDLE(i) = -1
        DELAY(10)
        
        IF AXISSTATUS(i) = 0 THEN
            g_AxisHome(i) = 2       ' 回零成功
            PRINT "轴", i, "回零成功"
        ELSE
            g_AxisHome(i) = 3       ' 回零失败
            PRINT "轴", i, "回零失败，状态：", HEX(AXISSTATUS(i))
            g_SysStatus = 0
            RETURN
        ENDIF
        
        DELAY(500)                  ' 轴间延时
    NEXT
    
    PRINT "所有轴回零完成"
    g_SysStatus = 0                 ' 回到待机状态
END SUB

'================ 左侧作业 ================
GLOBAL SUB LeftWork()
    g_SysStatus = 1                 ' 设置为运行状态
    PRINT "执行左侧打螺丝任务"
    
    FOR screw_idx = 0 TO g_LeftScrewNum - 1
        g_CurScrew = screw_idx + 1
        PRINT "开始打第", g_CurScrew, "个螺丝（左侧）"
        
        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(g_LeftStart + screw_idx * 3)
        screw_y = TABLE(g_LeftStart + screw_idx * 3 + 1)
        screw_z = TABLE(g_LeftStart + screw_idx * 3 + 2)
        
        '执行打螺丝流程
        CALL ScrewWork(screw_x, screw_y, screw_z, 1)  ' 1表示左侧Y1轴
        
        DELAY(500)                  ' 螺丝间延时
    NEXT
    
    PRINT "左侧打螺丝任务完成"
    g_SysStatus = 0                 ' 回到待机状态
    g_CurScrew = 0
END SUB

'================ 右侧作业 ================
GLOBAL SUB RightWork()
    g_SysStatus = 1                 ' 设置为运行状态
    PRINT "执行右侧打螺丝任务"
    
    FOR screw_idx = 0 TO g_RightScrewNum - 1
        g_CurScrew = screw_idx + 1
        PRINT "开始打第", g_CurScrew, "个螺丝（右侧）"
        
        '获取螺丝位置
        DIM screw_x, screw_y, screw_z
        screw_x = TABLE(g_RightStart + screw_idx * 3)
        screw_y = TABLE(g_RightStart + screw_idx * 3 + 1)
        screw_z = TABLE(g_RightStart + screw_idx * 3 + 2)
        
        '执行打螺丝流程
        CALL ScrewWork(screw_x, screw_y, screw_z, 2)  ' 2表示右侧Y2轴
        
        DELAY(500)                  ' 螺丝间延时
    NEXT
    
    PRINT "右侧打螺丝任务完成"
    g_SysStatus = 0                 ' 回到待机状态
    g_CurScrew = 0
END SUB

'================ 打螺丝作业流程 ================
GLOBAL SUB ScrewWork(target_x, target_y, target_z, y_axis)
    PRINT "目标位置：X=", target_x, " Y=", target_y, " Z=", target_z

    '1. 移动到吸螺丝位置
    PRINT "移动到吸螺丝位置"
    CALL MoveToPickPos(y_axis)

    '2. 吸取螺丝
    PRINT "吸取螺丝"
    OP(0, ON)                       ' 开启吸螺丝
    DELAY(1000)                     ' 等待吸取稳定

    '3. 圆弧插补运动到螺丝孔位
    PRINT "圆弧运动到螺丝孔位"
    CALL ArcMoveToTarget(target_x, target_y, target_z, y_axis)

    '4. 执行打螺丝
    PRINT "执行打螺丝"
    CALL DoScrew(target_z)

    '5. 圆弧插补运动回到吸螺丝位置
    PRINT "圆弧运动回到吸螺丝位置"
    CALL ArcMoveBack(target_x, target_y, target_z, y_axis)

    '6. 关闭吸螺丝
    OP(0, OFF)                      ' 关闭吸螺丝

    PRINT "螺丝完成"
END SUB

'================ 移动到吸螺丝位置 ================
GLOBAL SUB MoveToPickPos(y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴

    '先抬升Z轴到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '移动到吸螺丝位置
    MOVEABS(g_PickX, g_PickY) AXIS(0, y_axis)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(g_PickZ) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 圆弧插补运动到目标位置 ================
GLOBAL SUB ArcMoveToTarget(target_x, target_y, target_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴插补

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '计算圆弧中间点（简单的弧形路径）
    DIM mid_x, mid_y, cur_x, cur_y
    cur_x = DPOS(0)
    cur_y = DPOS(y_axis)

    mid_x = (cur_x + target_x) / 2
    mid_y = (cur_y + target_y) / 2 + 30  ' 抬高30mm形成弧形

    '三点圆弧插补：当前点 -> 中间点 -> 目标点
    MOVECIRC2(target_x, target_y, mid_x, mid_y, 0)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到螺丝孔位高度
    MOVEABS(target_z) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 圆弧插补运动回到吸螺丝位置 ================
GLOBAL SUB ArcMoveBack(current_x, current_y, current_z, y_axis)
    BASE(0, y_axis, 3)              ' X, Y, Z轴插补

    '先抬升到安全高度
    MOVEABS(20) AXIS(3)
    WAIT IDLE(3)

    '计算圆弧中间点
    DIM mid_x, mid_y
    mid_x = (current_x + g_PickX) / 2
    mid_y = (current_y + g_PickY) / 2 + 30  ' 抬高30mm形成弧形

    '三点圆弧插补：当前点 -> 中间点 -> 吸螺丝位置
    MOVECIRC2(g_PickX, g_PickY, mid_x, mid_y, 0)
    WAIT IDLE(0)
    WAIT IDLE(y_axis)

    '下降到吸螺丝高度
    MOVEABS(g_PickZ) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 执行打螺丝 ================
GLOBAL SUB DoScrew(target_z)
    '下降到接触位置
    MOVEABS(target_z - 5) AXIS(3)   ' 下降到螺丝孔上方5mm
    WAIT IDLE(3)

    '启动电批锁紧
    CALL ScrewControl(1)

    '等待电批完成
    DIM timeout_count, complete_status
    timeout_count = 0

    WHILE timeout_count < 100       ' 10秒超时
        complete_status = CheckScrewStatus()
        IF complete_status = 1 THEN
            PRINT "电批锁紧完成"
            GOTO screw_done
        ENDIF
        DELAY(100)
        timeout_count = timeout_count + 1
    WEND

    screw_done:

    IF timeout_count >= 100 THEN
        PRINT "电批锁紧超时"
    ENDIF

    '抬升Z轴
    MOVEABS(target_z + 10) AXIS(3)
    WAIT IDLE(3)
END SUB

'================ 电批控制 ================
GLOBAL SUB ScrewControl(action)
    DIM result

    IF action = 1 THEN
        '执行螺丝锁紧
        result = MODBUSM_REGSET(9222, 1, 1)    ' 写入组合命令寄存器，执行锁紧
        IF result = 0 THEN
            PRINT "电批锁紧命令发送成功"
        ELSE
            PRINT "电批通讯失败"
        ENDIF
    ELSEIF action = 2 THEN
        '执行螺丝松开
        result = MODBUSM_REGSET(9222, 1, 4)    ' 写入组合命令寄存器，执行松开
        IF result = 0 THEN
            PRINT "电批松开命令发送成功"
        ELSE
            PRINT "电批通讯失败"
        ENDIF
    ENDIF
END SUB

'================ 检查电批状态 ================
GLOBAL SUB CheckScrewStatus()
    DIM status_flag, complete_flag

    '读取电批状态
    MODBUSM_REGGET(9728, 1, 100)   ' 读取状态标志到MODBUS_REG(100)
    status_flag = MODBUS_REG(100)

    '读取完成标志
    MODBUSM_REGGET(9743, 1, 101)   ' 读取完成标志到MODBUS_REG(101)
    complete_flag = MODBUS_REG(101)

    RETURN complete_flag            ' 返回完成状态
END SUB

'================ 设置螺丝位置 ================
GLOBAL SUB SetScrewPos(side, screw_num, pos_x, pos_y, pos_z)
    DIM base_addr, index

    IF side = 1 THEN
        base_addr = g_LeftStart
    ELSEIF side = 2 THEN
        base_addr = g_RightStart
    ELSE
        PRINT "错误：无效的侧边参数"
        RETURN
    ENDIF

    index = base_addr + (screw_num - 1) * 3
    TABLE(index) = pos_x
    TABLE(index + 1) = pos_y
    TABLE(index + 2) = pos_z

    PRINT "设置螺丝位置：侧边=", side, " 编号=", screw_num
    PRINT "位置：X=", pos_x, " Y=", pos_y, " Z=", pos_z
END SUB

'================ 显示系统状态 ================
GLOBAL SUB ShowStatus()
    PRINT "=== 系统状态 ==="
    PRINT "系统状态：", g_SysStatus, " (0-待机,1-运行,2-回零)"
    PRINT "当前螺丝：", g_CurScrew
    PRINT "左侧螺丝数量：", g_LeftScrewNum
    PRINT "右侧螺丝数量：", g_RightScrewNum

    PRINT "=== 轴回零状态 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", g_AxisHome(i), " (0-未回零,1-回零中,2-已回零,3-失败)"
    NEXT

    PRINT "=== 轴当前位置 ==="
    FOR i = 0 TO 3
        PRINT "轴", i, "：", DPOS(i), " mm"
    NEXT

    PRINT "=== 吸螺丝位置 ==="
    PRINT "X=", g_PickX, " Y=", g_PickY, " Z=", g_PickZ
END SUB
