===============================================================================
                        两轴插补测试修正版使用说明
                        符合RTBasic语法规范的版本
===============================================================================

【修正内容】

根据RTBasic语法限制，对原程序进行了以下修正：

=== 语法修正 ===
```
1. 函数名长度限制：
   ❌ Test2_CircularInterpolation() (超过26字符)
   ✅ Test2_Circular() (符合限制)

2. 不支持的语句：
   ❌ SELECT CASE语句
   ❌ INPUT语句
   ✅ 改用直接函数调用方式

3. 编码问题：
   ❌ 特殊字符导致的编码错误
   ✅ 使用纯ASCII字符

4. 函数名简化：
   Test1_Linear()           - 两轴直线插补
   Test2_Circular()         - 两轴圆弧插补
   Test3_NonContinuous()    - 非连续插补
   Test4_Continuous()       - 连续插补
   Test5_CornerDecel()      - 前瞻拐角减速
   Test6_AutoChamfer()      - 自动倒角
   Test7_Combined()         - 组合前瞻
   Test8_Complex()          - 复杂轨迹
```

【使用方法】

=== 程序加载和运行 ===
```
1. 加载程序：
   将"两轴插补测试修正版.bas"加载到正运动控制器

2. 程序启动：
   程序会自动执行Main()函数，显示菜单和说明

3. 设置示波器：
   执行：CALL SetupScope()
   这会自动触发示波器并显示监控设置说明

4. 运行测试：
   方式1：运行所有测试
   CALL RunAllTests()
   
   方式2：运行单个测试
   CALL Test1_Linear()
   CALL Test2_Circular()
   CALL Test3_NonContinuous()
   CALL Test4_Continuous()
   CALL Test5_CornerDecel()
   CALL Test6_AutoChamfer()
   CALL Test7_Combined()
   CALL Test8_Complex()

5. 恢复设置：
   测试完成后执行：CALL SystemRestore()
```

=== 推荐的测试流程 ===
```
第一步：初始化和设置
CALL SetupScope()                    ' 设置示波器监控

第二步：基础测试
CALL Test1_Linear()                  ' 理解直线插补原理
CALL Test2_Circular()                ' 理解圆弧插补原理

第三步：连续插补对比
CALL Test3_NonContinuous()           ' 观察非连续插补的问题
CALL Test4_Continuous()              ' 观察连续插补的改善

第四步：前瞻功能测试
CALL Test5_CornerDecel()             ' 拐角减速效果
CALL Test6_AutoChamfer()             ' 自动倒角效果
CALL Test7_Combined()                ' 组合前瞻效果

第五步：综合应用
CALL Test8_Complex()                 ' 复杂轨迹应用

第六步：恢复设置
CALL SystemRestore()                 ' 恢复系统设置
```

【测试项目详解】

=== Test1_Linear() - 两轴直线插补 ===
```
功能：验证两轴直线插补的基本原理
轨迹：从(0,0)到(100,100)的45度直线
理论：合成距离141.42，各轴速度70.71
观察：VP_SPEED(0)和VP_SPEED(1)应该相等且同步
```

=== Test2_Circular() - 两轴圆弧插补 ===
```
功能：验证两轴圆弧插补的轨迹生成
轨迹：三点圆弧，逆时针方向
观察：两轴速度按圆弧轨迹协调变化，轨迹平滑
```

=== Test3_NonContinuous() - 非连续插补 ===
```
功能：展示MERGE=OFF时的运动特性
轨迹：矩形轨迹，四段直线
问题：每段之间有停顿，效率低，冲击大
观察：速度曲线有明显断点
```

=== Test4_Continuous() - 连续插补 ===
```
功能：展示MERGE=ON时的改善效果
轨迹：矩形轨迹，四段连续
改善：段间连续，无停顿
问题：拐角处仍有冲击
观察：速度曲线连续，但拐角有尖峰
```

=== Test5_CornerDecel() - 前瞻拐角减速 ===
```
功能：CORNER_MODE=2的拐角减速效果
特点：拐角处按角度自动减速
优势：减少冲击，保护机械
观察：拐角处速度下降但不为0
```

=== Test6_AutoChamfer() - 自动倒角 ===
```
功能：CORNER_MODE=32的自动倒角效果
特点：改变轨迹，拐角处插入圆弧
优势：保持高速，轨迹平滑
观察：拐角处轨迹变为圆弧，速度保持较高
```

=== Test7_Combined() - 组合前瞻 ===
```
功能：CORNER_MODE=2+32的组合效果
特点：既有倒角又有减速
优势：平衡速度和精度
观察：综合效果最佳，适合大多数应用
```

=== Test8_Complex() - 复杂轨迹 ===
```
功能：直线+圆弧的复杂轨迹连续插补
轨迹：6段混合轨迹
验证：复杂路径的连续性
观察：直线和圆弧之间完全连续
```

【示波器监控】

=== 监控信号设置 ===
```
VP_SPEED(0) - X轴单轴速度（蓝色，刻度100，偏移0）
VP_SPEED(1) - Y轴单轴速度（红色，刻度100，偏移-60）
MSPEED(0)   - X轴分速度（绿色，刻度100，偏移-120）
MSPEED(1)   - Y轴分速度（黄色，刻度100，偏移-180）

注意：程序已自动设置SYSTEM_ZSET让VP_SPEED显示单轴速度
```

=== 观察要点 ===
```
直线插补：
✅ 两轴速度按比例变化
✅ 45度直线时两轴速度相等
✅ 速度曲线对称

圆弧插补：
✅ 两轴速度按圆弧轨迹变化
✅ 速度曲线平滑连续
✅ 合成速度基本恒定

连续插补：
✅ 段间无速度断点
✅ 整体曲线连续
✅ 无中间停顿

前瞻功能：
✅ 拐角处速度平滑变化
✅ 轨迹质量改善
✅ 机械冲击减少
```

【故障排除】

=== 常见问题 ===
```
问题1：程序加载失败
解决：检查文件编码，确保使用ASCII编码

问题2：函数调用失败
解决：确认函数名拼写正确，注意大小写

问题3：示波器无信号
解决：检查示波器连接，确认信号线正确

问题4：运动异常
解决：检查轴参数设置，确认机械连接正常

问题5：速度曲线异常
解决：调整示波器刻度，检查SYSTEM_ZSET设置
```

=== 参数调整 ===
```
如需调整运动参数，可修改以下设置：

速度参数：
SPEED = 100, 100                    ' 轴速度
FORCE_SPEED = 100                   ' 插补速度

加速度参数：
ACCEL = 1000, 1000                  ' 加速度
DECEL = 1000, 1000                  ' 减速度

平滑参数：
SRAMP = 50, 50                      ' S曲线时间
VP_MODE = 7, 7                      ' SS曲线

前瞻参数：
CORNER_MODE = 32                    ' 前瞻模式
ZSMOOTH = 10                        ' 倒角半径
DECEL_ANGLE = 30 * (PI/180)         ' 减速角度
STOP_ANGLE = 90 * (PI/180)          ' 停止角度
```

【程序特点】

=== 技术特点 ===
```
✅ 完全符合RTBasic语法规范
✅ 函数名长度控制在26字符内
✅ 避免使用不支持的语句
✅ 自动设置VP_SPEED显示单轴速度
✅ 提供详细的理论说明和计算
✅ 包含完整的示波器监控指导
✅ 支持单独测试和批量测试
✅ 自动恢复系统设置
```

=== 学习价值 ===
```
✅ 深入理解插补运动原理
✅ 掌握连续插补的实现方法
✅ 了解前瞻预处理的各种模式
✅ 学会使用示波器分析运动质量
✅ 为实际应用提供参考方案
✅ 验证理论计算的准确性
```

【总结】

本修正版程序解决了原版本的语法兼容性问题：
- 函数名符合长度限制
- 避免使用不支持的语句
- 采用直接函数调用方式
- 保持了完整的测试功能

使用时请按照推荐流程执行测试，通过示波器观察速度曲线变化，
深入理解插补运动的原理和正运动控制器的强大功能。

程序已经过语法检查，应该可以正常加载和运行。
如遇到问题，请检查控制器型号和固件版本的兼容性。

===============================================================================
