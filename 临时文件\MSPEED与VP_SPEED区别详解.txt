===============================================================================
                        MSPEED与VP_SPEED区别详解 - 基于官方手册
===============================================================================

【根本区别】

=== MSPEED - 实际反馈速度 ===
```
官方定义：
MSPEED -- 实际反馈速度
类型：轴状态
描述：轴的测量反馈位置速度，单位是 units/s

特点：
✅ 反映轴的实际运动速度
✅ 基于编码器或位置反馈计算
✅ 显示轴的真实运动状态
✅ 不受SYSTEM_ZSET影响
✅ 始终显示实际测量值
```

=== VP_SPEED - 当前运动速度 ===
```
官方定义：
VP_SPEED -- 当前运动速度
类型：轴状态
描述：返回轴当前运动的速度，单位为 units/s

默认行为（SYSTEM_ZSET bit0=1）：
- 当多轴运动时，主轴返回的是插补运动的速度，不是主轴的分速度
- 非主轴返回的是相应的分速度，与 MSPEED 效果一致
- VP_SPEED 在默认情况下是为显示多轴合成速度设计的，是没有负值的

修改后行为（SYSTEM_ZSET bit0=0）：
- 可以用来显示单轴的命令速度，可正可负
```

【详细对比分析】

=== 数据来源不同 ===
```
MSPEED：
- 来源：编码器反馈或位置测量
- 性质：实际测量值
- 精度：取决于编码器分辨率
- 延迟：有测量和计算延迟

VP_SPEED：
- 来源：运动控制器内部计算
- 性质：理论命令值
- 精度：基于运动规划计算
- 延迟：实时计算，无延迟
```

=== 插补运动时的表现 ===
```
以BASE(0,1) MOVE(100,100)为例：

默认情况（SYSTEM_ZSET bit0=1）：
MSPEED(0) = X轴实际反馈速度（约70.71）
MSPEED(1) = Y轴实际反馈速度（约70.71）
VP_SPEED(0) = 插补合成速度（100）
VP_SPEED(1) = Y轴分速度（约70.71）

修改后（SYSTEM_ZSET bit0=0）：
MSPEED(0) = X轴实际反馈速度（约70.71）
MSPEED(1) = Y轴实际反馈速度（约70.71）
VP_SPEED(0) = X轴命令速度（约70.71，可正可负）
VP_SPEED(1) = Y轴命令速度（约70.71，可正可负）
```

=== 应用场景不同 ===
```
MSPEED适用于：
✅ 检测实际运动性能
✅ 诊断机械问题
✅ 验证跟随误差
✅ 监控系统稳定性
✅ 闭环控制分析

VP_SPEED适用于：
✅ 监控运动规划
✅ 验证插补算法
✅ 分析速度曲线
✅ 调试运动参数
✅ 理论与实际对比
```

【SYSTEM_ZSET设置无效的原因】

=== 常见问题分析 ===
```
问题现象：
设置了SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)
但VP_SPEED仍显示合成速度，不显示单轴速度

可能原因：
1. 设置时机错误
   ❌ 在BASE()之后设置
   ✅ 必须在BASE()之前设置

2. 设置方法错误
   ❌ 使用了错误的位操作
   ✅ 正确的位操作：AND (NOT 1)

3. 验证方法错误
   ❌ 没有验证设置是否生效
   ✅ 用PRINT检查SYSTEM_ZSET值

4. 理解错误
   ❌ 以为会改变MSPEED
   ✅ 只影响VP_SPEED的显示方式
```

=== 正确的设置方法 ===
```basic
'正确的设置顺序
SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)   ' 第一步：设置bit0=0
PRINT "SYSTEM_ZSET=", SYSTEM_ZSET       ' 第二步：验证设置（应该是偶数）
BASE(0, 1)                              ' 第三步：设置轴组

'验证效果
MOVE(100, 100)
'此时VP_SPEED(0)和VP_SPEED(1)应该都约为70.71
'而不是VP_SPEED(0)=100, VP_SPEED(1)=70.71
```

【实际测试验证】

=== 测试程序 ===
```basic
'测试MSPEED与VP_SPEED的区别
PRINT "=== MSPEED与VP_SPEED对比测试 ==="

'保存原始设置
DIM original_zset
original_zset = SYSTEM_ZSET

'测试1：默认设置（合成速度模式）
PRINT "测试1：默认设置（SYSTEM_ZSET bit0=1）"
SYSTEM_ZSET = original_zset OR 1        ' 确保bit0=1
BASE(0, 1)
DPOS = 0, 0
UNITS = 100, 100
SPEED = 100, 100
ACCEL = 1000, 1000
DECEL = 1000, 1000
TRIGGER
MOVE(100, 100)
DELAY 2000                              ' 等待运动过程中
PRINT "MSPEED(0)=", MSPEED(0), " MSPEED(1)=", MSPEED(1)
PRINT "VP_SPEED(0)=", VP_SPEED(0), " VP_SPEED(1)=", VP_SPEED(1)
WAIT IDLE

'测试2：单轴速度模式
PRINT "测试2：单轴速度模式（SYSTEM_ZSET bit0=0）"
SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)   ' 设置bit0=0
PRINT "SYSTEM_ZSET=", SYSTEM_ZSET       ' 验证设置
BASE(0, 1)                              ' 重新设置轴组
DPOS = 0, 0
TRIGGER
MOVE(100, 100)
DELAY 2000                              ' 等待运动过程中
PRINT "MSPEED(0)=", MSPEED(0), " MSPEED(1)=", MSPEED(1)
PRINT "VP_SPEED(0)=", VP_SPEED(0), " VP_SPEED(1)=", VP_SPEED(1)
WAIT IDLE

'恢复原始设置
SYSTEM_ZSET = original_zset
```

=== 预期结果 ===
```
测试1结果（默认设置）：
MSPEED(0) ≈ 70.71    （X轴实际反馈速度）
MSPEED(1) ≈ 70.71    （Y轴实际反馈速度）
VP_SPEED(0) ≈ 100    （插补合成速度）
VP_SPEED(1) ≈ 70.71  （Y轴分速度）

测试2结果（单轴模式）：
MSPEED(0) ≈ 70.71    （X轴实际反馈速度，不变）
MSPEED(1) ≈ 70.71    （Y轴实际反馈速度，不变）
VP_SPEED(0) ≈ 70.71  （X轴命令速度，可正可负）
VP_SPEED(1) ≈ 70.71  （Y轴命令速度，可正可负）
```

【示波器监控建议】

=== 推荐监控配置 ===
```
方案1：性能分析（推荐MSPEED）
MSPEED(0) - X轴实际速度（蓝色，刻度100，偏移0）
MSPEED(1) - Y轴实际速度（红色，刻度100，偏移-60）
VP_SPEED(0) - 插补合成速度（绿色，刻度100，偏移-120）
VP_SPEED(1) - Y轴分速度（黄色，刻度100，偏移-180）

观察要点：
✅ MSPEED反映实际性能
✅ VP_SPEED反映理论规划
✅ 两者差异反映系统误差

方案2：插补分析（推荐VP_SPEED单轴模式）
设置SYSTEM_ZSET bit0=0后：
VP_SPEED(0) - X轴命令速度（蓝色，刻度100，偏移0）
VP_SPEED(1) - Y轴命令速度（红色，刻度100，偏移-60）
MSPEED(0) - X轴实际速度（绿色，刻度100，偏移-120）
MSPEED(1) - Y轴实际速度（黄色，刻度100，偏移-180）

观察要点：
✅ VP_SPEED显示各轴理论速度
✅ MSPEED显示各轴实际速度
✅ 便于分析各轴协调性
```

=== 故障诊断应用 ===
```
机械问题诊断：
- MSPEED波形不平滑 → 机械振动或刚性不足
- MSPEED与VP_SPEED差异大 → 跟随误差大
- MSPEED有尖峰 → 机械冲击或共振

插补质量分析：
- VP_SPEED(单轴模式)不协调 → 插补算法问题
- VP_SPEED有突变 → 运动规划不连续
- VP_SPEED与理论计算不符 → 参数设置错误
```

【总结】

MSPEED与VP_SPEED的核心区别：
✅ **数据来源**：MSPEED来自实际反馈，VP_SPEED来自理论计算
✅ **应用目的**：MSPEED用于性能分析，VP_SPEED用于规划验证
✅ **设置影响**：SYSTEM_ZSET只影响VP_SPEED，不影响MSPEED
✅ **监控价值**：两者结合使用，全面分析运动质量

SYSTEM_ZSET设置的关键要点：
✅ **设置时机**：必须在BASE()之前设置
✅ **设置方法**：SYSTEM_ZSET = SYSTEM_ZSET AND (NOT 1)
✅ **验证方法**：检查SYSTEM_ZSET值是否为偶数
✅ **影响范围**：只改变VP_SPEED显示方式，不影响MSPEED

通过正确理解和使用MSPEED与VP_SPEED，
可以更好地分析和优化两轴插补运动的性能。

===============================================================================
