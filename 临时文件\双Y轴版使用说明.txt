===============================================================================
                    螺丝机双Y轴版 - 使用双Y轴实现滑轨功能说明
===============================================================================

【核心设计理念】
✅ 利用现有双Y轴实现滑轨功能，无需额外轴
✅ Y1轴（左侧）和Y2轴（右侧）独立控制用户侧↔工作侧移动
✅ 滑轨螺距10mm/圈，电机转一圈滑块移动10mm
✅ 队列控制系统，支持电批忙碌时排队等待

【硬件配置】
轴配置：
- 轴0 (X轴)：横向移动轴
- 轴1 (Y1轴)：左侧纵向移动轴 ⭐ 兼做左侧滑轨
- 轴2 (Y2轴)：右侧纵向移动轴 ⭐ 兼做右侧滑轨
- 轴3 (Z轴)：垂直升降轴

Y轴滑轨参数：
- 螺距：10mm/圈
- 脉冲当量：100脉冲/mm（适配10mm螺距）
- 运动速度：80mm/s
- 加速度：800mm/s²

位置定义：
- 用户侧：Y轴较小坐标值（靠近用户，方便放置工件）
- 工作侧：Y轴较大坐标值（电批侧，第一个螺丝位置）

默认位置设置：
- 左侧用户位置：50mm
- 左侧工作位置：80mm（第一个螺丝Y坐标）
- 右侧用户位置：200mm
- 右侧工作位置：220mm（第一个螺丝Y坐标）

【操作流程】

=== 标准作业流程 ===
1. 系统回零
   - 按下IN2进行四轴回零
   - 回零完成后双Y轴自动移动到用户位置

2. 放置工件
   - 左Y轴在用户位置（50mm），方便用户放置左侧工件
   - 右Y轴在用户位置（200mm），方便用户放置右侧工件

3. 开始作业
   - 按下IN0（左侧）：左Y轴自动移动到工作位置（80mm）
   - 按下IN1（右侧）：右Y轴自动移动到工作位置（220mm）
   - 系统开始相应侧的打螺丝作业

4. 自动完成
   - 打完所有螺丝后
   - 对应Y轴自动回到用户位置
   - 用户可以取走工件，放置新工件

=== 队列功能详解 ===
场景1：电批空闲，左侧请求
- 用户按下IN0 → 左Y轴移动到工作位置 → 立即开始左侧打螺丝

场景2：电批忙碌，右侧请求
- 用户按下IN1 → 右Y轴移动到工作位置 → 右侧任务加入队列等待
- 当前任务完成后 → 自动开始右侧任务

场景3：双侧同时请求
- 左右两侧都可以独立请求
- Y轴都会移动到工作位置
- 系统按先来先服务原则处理

【输入信号】
- IN0：左侧开始按键（左Y轴移动到工作位置）
- IN1：右侧开始按键（右Y轴移动到工作位置）
- IN2：系统回零按键
- IN3：急停按键
- IN4：手动左Y轴到用户位置 ⭐ 新增
- IN5：手动右Y轴到用户位置 ⭐ 新增

【状态说明】

=== Y轴滑轨状态 ===
- 0：用户侧（方便放置工件）
- 1：工作侧（电批作业位置）
- 2：移动中（正在移动）

=== 队列状态 ===
- 0：无任务
- 1：等待中（已请求，等待电批空闲）
- 2：执行中（正在打螺丝）

=== 电批状态 ===
- 0：空闲（可以接受新任务）
- 1：忙碌（正在打螺丝）

【螺丝位置配置】
系统自动根据Y轴工作位置配置螺丝坐标：

左侧螺丝位置：
- 第一排：Y = left_work_pos (80mm)
- 第二排：Y = left_work_pos + 40 (120mm)

右侧螺丝位置：
- 第一排：Y = right_work_pos (220mm)
- 第二排：Y = right_work_pos + 40 (260mm)

【测试功能】
查看系统状态：
CALL ShowStatus()

测试左Y轴滑轨：
CALL LeftSlideToWork()    ' 移动到工作位置
CALL LeftSlideToUser()    ' 移动到用户位置

测试右Y轴滑轨：
CALL RightSlideToWork()   ' 移动到工作位置
CALL RightSlideToUser()   ' 移动到用户位置

设置Y轴位置：
left_user_pos = 40        ' 设置左侧用户位置
left_work_pos = 75        ' 设置左侧工作位置
right_user_pos = 190      ' 设置右侧用户位置
right_work_pos = 215      ' 设置右侧工作位置

【优势特点】

=== 硬件简化 ===
✅ 无需额外滑轨轴，利用现有双Y轴
✅ 减少硬件成本和复杂度
✅ 充分利用现有轴的功能

=== 用户友好 ===
✅ Y轴空闲时在用户侧，方便放置工件
✅ 按键后Y轴自动移动，无需手动操作
✅ 作业完成后自动回到用户侧

=== 高效生产 ===
✅ 支持队列等待，电批利用率高
✅ 双侧独立控制，互不干扰
✅ 自动调度，减少等待时间

【典型使用场景】

场景1：左侧单件连续生产
1. 用户在左侧放置工件（左Y轴在用户位置50mm）
2. 按下IN0，左Y轴移动到工作位置80mm
3. 开始左侧打螺丝作业
4. 完成后左Y轴回到用户位置50mm
5. 用户取走工件，放置新工件

场景2：右侧单件连续生产
1. 用户在右侧放置工件（右Y轴在用户位置200mm）
2. 按下IN1，右Y轴移动到工作位置220mm
3. 开始右侧打螺丝作业
4. 完成后右Y轴回到用户位置200mm
5. 用户取走工件，放置新工件

场景3：双侧交替生产
1. 左侧开始作业，左Y轴在工作位置
2. 作业过程中，用户在右侧放置工件，按下IN1
3. 右Y轴移动到工作位置，右侧任务加入队列
4. 左侧完成后，自动开始右侧任务
5. 右侧完成后，右Y轴回到用户位置

【参数调整】

Y轴脉冲当量调整：
- 当前设置：UNITS = 1000, 100, 100, 1000
- Y1轴和Y2轴都是100脉冲/mm（适配10mm螺距）
- 如需调整：修改UNITS参数中的第2、3个值

Y轴位置调整：
- 修改全局变量：
  left_user_pos = 50     ' 左侧用户位置
  left_work_pos = 80     ' 左侧工作位置
  right_user_pos = 200   ' 右侧用户位置
  right_work_pos = 220   ' 右侧工作位置

Y轴速度调整：
- 当前设置：SPEED = 100, 80, 80, 50
- Y1轴和Y2轴都是80mm/s
- 如需调整：修改SPEED参数中的第2、3个值

【注意事项】
1. Y轴脉冲当量必须正确设置（100脉冲/mm对应10mm螺距）
2. 用户位置和工作位置要根据实际机械结构调整
3. 确保Y轴行程足够，能够到达设定的位置
4. 螺丝位置会根据工作位置自动计算
5. 急停后需要重新回零，Y轴会自动回到用户位置

【故障排除】
1. Y轴不移动
   - 检查脉冲当量设置（UNITS参数）
   - 检查Y轴限位和原点开关
   - 确认Y轴已正确回零

2. 位置不准确
   - 重新执行回零
   - 检查脉冲当量是否匹配实际螺距
   - 调整用户位置和工作位置参数

3. 队列不工作
   - 调用ShowStatus()查看状态
   - 检查电批忙碌状态
   - 确认Y轴已到达工作位置

【版本对比】
完美版 → 双Y轴版：
+ 利用现有双Y轴实现滑轨功能
+ 无需额外硬件轴
+ Y轴脉冲当量适配10mm螺距
+ 独立的左右Y轴控制
+ 自动螺丝位置计算
+ 更加经济实用的方案

===============================================================================
