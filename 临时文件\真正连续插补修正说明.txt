===============================================================================
                        真正连续插补修正说明 - 解决衔接处速度变成0的问题
===============================================================================

【问题根本原因】

=== 发现的关键问题 ===
```
现象：X轴和Z轴在衔接的地方速度变成了0
根本原因：STARTMOVE_SPEED的设置规则理解错误

官方手册的关键说明：
"第二段的起始速度15，因为高于第一段的结束速度10，因此实际的起始速度为10"

STARTMOVE_SPEED的实际规则：
- 如果STARTMOVE_SPEED > 前一段的ENDMOVE_SPEED
  实际起始速度 = 前一段的ENDMOVE_SPEED
- 如果STARTMOVE_SPEED ≤ 前一段的ENDMOVE_SPEED  
  实际起始速度 = STARTMOVE_SPEED
```

=== 之前错误的理解 ===
```
错误认为：
STARTMOVE_SPEED = ENDMOVE_SPEED 就能实现连续

实际情况：
如果前一段没有设置ENDMOVE_SPEED，或者设置为0
那么下一段的STARTMOVE_SPEED再高也没用
实际起始速度还是0，导致衔接处速度变成0
```

【官方例程分析】

=== 官方SP指令连续插补例程 ===
```basic
来源：RTBasic编程手册V1.1.2.txt 第21260行

关键代码：
BASE(0,1,2,3)
MERGE = ON                      '启动连续插补
CORNER_MODE = 0                 '不启动自动拐角减速，手动设置速度

'第一段
FORCE_SPEED = 100               '第一段速度100
ENDMOVE_SPEED = 10              '第一段结束速度10
MOVESP(100,0)

'第二段  
FORCE_SPEED = 150               '第二段速度150
ENDMOVE_SPEED = 15              '第二段结束速度15
STARTMOVE_SPEED = 15            '第二段起始速度15
MOVESP(0,100)                   '实际起始速度=10（前一段的结束速度）

'第三段
FORCE_SPEED = 200               '第三段速度200
ENDMOVE_SPEED = 20              '第三段结束速度20
STARTMOVE_SPEED = 20            '第三段起始速度20
MOVESP(-100,0)                  '实际起始速度=15（前一段的结束速度）

关键点：
✅ 每段都必须设置ENDMOVE_SPEED
✅ 下一段的STARTMOVE_SPEED要等于前一段的ENDMOVE_SPEED
✅ CORNER_MODE = 0，不使用自动拐角减速
✅ 手动控制每段的起始和结束速度
```

【修正方案】

=== 核心修正原则 ===
```basic
1. 严格按照官方例程的模式：
   - MERGE = ON
   - CORNER_MODE = 0
   - 每段都设置FORCE_SPEED和ENDMOVE_SPEED
   - 下一段的STARTMOVE_SPEED = 前一段的ENDMOVE_SPEED

2. 速度衔接设计：
   第一段：0 → 30mm/s（ENDMOVE_SPEED = 30）
   第二段：30 → 30mm/s（STARTMOVE_SPEED = 30, ENDMOVE_SPEED = 30）
   第三段：30 → 0mm/s（STARTMOVE_SPEED = 30, ENDMOVE_SPEED = 0）

3. 去除可能干扰连续性的参数：
   - 不使用VP_MODE=7（可能影响连续性）
   - 不使用ZSMOOTH_MODE（可能影响连续性）
   - 不使用复杂的CORNER_MODE（可能影响连续性）
   - 只保留基本的SRAMP平滑
```

=== 修正前后对比 ===
```basic
修正前（速度变成0）：
VP_MODE = 7, 7                  ' 复杂的SS曲线
ZSMOOTH_MODE = 1                ' 平滑模式
CORNER_MODE = 32 + 2            ' 复杂的拐角模式
STARTMOVE_SPEED = 20            ' 设置了起始速度
ENDMOVE_SPEED = 20              ' 设置了结束速度
但是：前一段的ENDMOVE_SPEED可能不是20，导致衔接失败

修正后（真正连续）：
MERGE = ON                      ' 开启连续插补
CORNER_MODE = 0                 ' 手动控制速度，不自动减速
SRAMP = 50, 50                  ' 适度平滑

第一段：
FORCE_SPEED = 80
ENDMOVE_SPEED = 30              ' 明确设置结束速度30
MOVEABSSP(...)

第二段：
FORCE_SPEED = 100
STARTMOVE_SPEED = 30            ' 起始速度30（等于前一段结束速度）
ENDMOVE_SPEED = 30              ' 结束速度30
MOVEABSSP(...)                  ' 实际起始速度=30（连续）

第三段：
FORCE_SPEED = 80
STARTMOVE_SPEED = 30            ' 起始速度30（等于前一段结束速度）
ENDMOVE_SPEED = 0               ' 结束速度0（停止）
MOVEABSSP(...)                  ' 实际起始速度=30（连续）

结果：速度衔接 0→30→30→0，完全连续
```

【技术细节】

=== STARTMOVE_SPEED和ENDMOVE_SPEED的正确使用 ===
```basic
规则1：每段运动都必须设置ENDMOVE_SPEED
- 如果不设置，默认为0
- 会导致下一段从0开始，破坏连续性

规则2：下一段的STARTMOVE_SPEED必须等于前一段的ENDMOVE_SPEED
- 如果STARTMOVE_SPEED > 前段ENDMOVE_SPEED：实际起始 = 前段ENDMOVE_SPEED
- 如果STARTMOVE_SPEED ≤ 前段ENDMOVE_SPEED：实际起始 = STARTMOVE_SPEED
- 最安全的做法：STARTMOVE_SPEED = 前段ENDMOVE_SPEED

规则3：第一段不需要设置STARTMOVE_SPEED
- 第一段从静止开始，起始速度自动为0
- 只需要设置ENDMOVE_SPEED为衔接速度

规则4：最后一段的ENDMOVE_SPEED设为0
- 确保运动最终停止
- 如果后面还有运动，则设为下一段的衔接速度
```

=== CORNER_MODE=0的重要性 ===
```basic
CORNER_MODE = 0的作用：
- 不启动自动拐角减速
- 完全由STARTMOVE_SPEED和ENDMOVE_SPEED控制速度
- 避免自动减速干扰连续插补
- 确保速度按设定值衔接

为什么不能用其他CORNER_MODE：
- CORNER_MODE = 2：自动拐角减速，会干扰速度衔接
- CORNER_MODE = 32：自动倒角，可能改变轨迹和速度
- CORNER_MODE = 34：组合模式，更复杂，容易出问题

官方建议：
连续插补时使用CORNER_MODE = 0，手动控制速度
```

=== MERGE=ON的作用机制 ===
```basic
MERGE = ON的功能：
- 将多段运动连接起来
- 避免段间减速到0
- 但需要正确的速度参数配合

MERGE=ON不能解决的问题：
- 如果速度参数设置错误，仍会减速到0
- 如果CORNER_MODE干扰，仍可能不连续
- 如果轴参数不匹配，仍可能有问题

正确的组合：
MERGE = ON + CORNER_MODE = 0 + 正确的SP速度参数
```

【速度曲线分析】

=== 修正后的预期速度曲线 ===
```
X轴和Z轴速度曲线：
速度
 ^
 |      /--------\
 |     /          \
 |    /            \
 |   /              \
 |  /                \
 | /                  \
 |/                    \
 +-----|-----|-----|-----|---> 时间
      30    30    30
      第1段  第2段  第3段
      结束   开始   开始
            结束

特点：
✅ 第1段：0→30mm/s（平滑加速）
✅ 第2段：30→30mm/s（完全连续，无减速）
✅ 第3段：30→0mm/s（平滑减速）
✅ 衔接处速度保持30mm/s，不变成0
✅ 整体曲线平滑连续
```

=== 与之前的对比 ===
```
之前的问题曲线：
速度
 ^
 |    /\     /\     /\
 |   /  \   /  \   /  \
 |  /    \ /    \ /    \
 | /      V      V      \
 |/       0      0       \
 +-----|-----|-----|-----|---> 时间
      衔接   衔接   衔接
      变0    变0    变0

问题：每个衔接处都减速到0，不连续

修正后的连续曲线：
速度
 ^
 |      /--------\
 |     /          \
 |    /            \
 |   /              \
 |  /                \
 | /                  \
 |/                    \
 +-----|-----|-----|-----|---> 时间
      30    30    30
      连续   连续   连续

优势：衔接处保持30mm/s，完全连续
```

【验证方法】

=== 速度连续性验证 ===
```
1. 示波器观察：
   - 观察MSPEED(0)和MSPEED(3)曲线
   - 确认衔接处速度不为0
   - 检查速度保持在30mm/s

2. 程序调试：
   - 在每段运动前后打印当前速度
   - 使用TRACE输出调试信息
   - 确认STARTMOVE_SPEED和ENDMOVE_SPEED生效

3. 机械观察：
   - 听取运动声音，应该连续无停顿
   - 观察运动轨迹，应该平滑连续
   - 检查定位精度和重复性
```

=== 故障排除 ===
```
如果衔接处仍然减速到0：
1. 检查MERGE是否设置为ON
2. 确认CORNER_MODE是否为0
3. 验证每段的ENDMOVE_SPEED是否正确设置
4. 检查STARTMOVE_SPEED是否等于前段ENDMOVE_SPEED
5. 确认没有其他参数干扰连续插补

如果速度衔接不平滑：
1. 适当调整SRAMP参数
2. 检查ACCEL和DECEL设置
3. 确认FORCE_SPEED不要过高
4. 验证衔接速度是否合理
```

【应用效果】

=== 螺丝机应用优势 ===
```
运动质量：
✅ 三段轨迹真正连续，无停顿
✅ X轴和Z轴速度衔接平滑
✅ 机械冲击显著减少
✅ 定位精度提升

作业效率：
✅ 运动时间缩短（无停顿时间）
✅ 螺丝对准更准确
✅ 减少重复作业
✅ 整体效率提升

设备保护：
✅ 机械磨损减少
✅ 电机负载平滑
✅ 振动和噪音降低
✅ 设备寿命延长
```

=== 参数调整建议 ===
```
如需更高速度：
FORCE_SPEED = 120, 100, 120     ' 提高各段速度
衔接速度 = 40mm/s               ' 提高衔接速度

如需更平滑：
SRAMP = 80, 80                  ' 增加S曲线时间
衔接速度 = 20mm/s               ' 降低衔接速度

如需更高精度：
FORCE_SPEED = 60, 80, 60        ' 降低速度
衔接速度 = 25mm/s               ' 适中衔接速度
```

【总结】

真正连续插补的关键：
✅ **严格按官方例程**：MERGE=ON + CORNER_MODE=0 + 正确SP参数
✅ **速度参数匹配**：下段STARTMOVE_SPEED = 前段ENDMOVE_SPEED
✅ **每段设置ENDMOVE_SPEED**：确保为下一段提供正确的起始速度
✅ **避免复杂参数**：不使用可能干扰连续性的高级功能
✅ **速度衔接设计**：0→30→30→0，衔接处保持30mm/s
✅ **验证连续性**：使用示波器确认速度不为0

这个修正彻底解决了X轴和Z轴在衔接处速度变成0的问题，
实现了真正的连续插补，为螺丝机提供了高效、平滑的三段轨迹运动。

===============================================================================
