REM ===============================================================================
REM                        两轴插补测试demo - 直线插补、圆弧插补和连续插补
REM ===============================================================================

REM 全局变量定义
GLOBAL test_mode                    ' 测试模式：1=直线插补，2=圆弧插补，3=连续插补
GLOBAL original_zset                ' 保存原始SYSTEM_ZSET设置

REM ===============================================================================
REM                                主程序
REM ===============================================================================
PRINT "==============================================================================="
PRINT "                        两轴插补测试demo"
PRINT "==============================================================================="
PRINT "基于官方手册5.2插补运动章节，测试直线插补、圆弧插补和连续插补"
PRINT ""

'初始化系统
CALL InitSystem()

'显示测试菜单
CALL ShowMenu()

'等待用户选择
WHILE 1
    IF IN(0) THEN                   ' 按键IN0：直线插补测试
        test_mode = 1
        CALL TestLinearInterpolation()
        CALL ShowMenu()
    ENDIF
    
    IF IN(1) THEN                   ' 按键IN1：圆弧插补测试
        test_mode = 2
        CALL TestCircularInterpolation()
        CALL ShowMenu()
    ENDIF
    
    IF IN(2) THEN                   ' 按键IN2：连续插补测试
        test_mode = 3
        CALL TestContinuousInterpolation()
        CALL ShowMenu()
    ENDIF
    
    IF IN(3) THEN                   ' 按键IN3：速度监控设置
        CALL SetupSpeedMonitoring()
    ENDIF
    
    DELAY(100)                      ' 延时100ms，避免按键抖动
WEND

REM ===============================================================================
REM                                系统初始化
REM ===============================================================================
GLOBAL SUB InitSystem()
    PRINT "系统初始化..."
    
    '保存原始SYSTEM_ZSET设置
    original_zset = SYSTEM_ZSET
    
    '设置轴参数（X轴=轴0，Y轴=轴1）
    UNITS = 1000, 1000              ' 脉冲当量1000pulse/mm
    SPEED = 100, 100                ' 最大速度100mm/s
    ACCEL = 1000, 1000              ' 加速度1000mm/s²
    DECEL = 1000, 1000              ' 减速度1000mm/s²
    SRAMP = 50, 50                  ' S曲线时间50ms
    
    '清零坐标
    DPOS = 0, 0
    
    '设置基础轴组（X轴为主轴）
    BASE(0, 1)
    
    PRINT "轴参数设置完成："
    PRINT "X轴(轴0)：主轴，脉冲当量1000，速度100mm/s，加速度1000mm/s²"
    PRINT "Y轴(轴1)：从轴，脉冲当量1000，速度100mm/s，加速度1000mm/s²"
    PRINT "坐标已清零，BASE(0,1)设置完成"
    PRINT ""
END SUB

REM ===============================================================================
REM                                显示测试菜单
REM ===============================================================================
GLOBAL SUB ShowMenu()
    PRINT "==============================================================================="
    PRINT "                                测试菜单"
    PRINT "==============================================================================="
    PRINT "IN0 - 直线插补测试（两轴直线插补原理验证）"
    PRINT "IN1 - 圆弧插补测试（两轴圆弧插补原理验证）"
    PRINT "IN2 - 连续插补测试（MERGE=ON连续插补验证）"
    PRINT "IN3 - 速度监控设置（TRIGGER示波器监控）"
    PRINT ""
    PRINT "请按相应按键选择测试项目..."
    PRINT "==============================================================================="
END SUB

REM ===============================================================================
REM                                直线插补测试
REM ===============================================================================
GLOBAL SUB TestLinearInterpolation()
    PRINT ""
    PRINT "==============================================================================="
    PRINT "                            直线插补测试"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.2.4二轴直线插补原理："
    PRINT "从A点(0,0)运动到B点(50,30)，XY轴同时启动并同时到达终点"
    PRINT ""
    
    '计算理论值
    DIM delta_x, delta_y, total_distance, speed_x, speed_y
    delta_x = 50                    ' X轴运动距离50mm
    delta_y = 30                    ' Y轴运动距离30mm
    total_distance = SQR(delta_x * delta_x + delta_y * delta_y)
    speed_x = 100 * delta_x / total_distance
    speed_y = 100 * delta_y / total_distance
    
    PRINT "理论计算："
    PRINT "ΔX =", delta_x, "mm, ΔY =", delta_y, "mm"
    PRINT "插补合成运动距离 = √(ΔX² + ΔY²) =", total_distance, "mm"
    PRINT "主轴速度S = 100mm/s"
    PRINT "X轴实际速度 = S×ΔX/总距离 =", speed_x, "mm/s"
    PRINT "Y轴实际速度 = S×ΔY/总距离 =", speed_y, "mm/s"
    PRINT ""
    
    '设置VP_SPEED显示单轴速度
    SYSTEM_ZSET = original_zset AND (NOT 1)
    
    '回到起点
    DPOS = 0, 0
    FORCE_SPEED = 100               ' 主轴速度100mm/s
    
    PRINT "开始直线插补运动..."
    TRIGGER                         ' 触发示波器
    
    '执行直线插补
    MOVE(50, 30)                    ' 从(0,0)到(50,30)的直线插补
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '恢复SYSTEM_ZSET设置
    SYSTEM_ZSET = original_zset OR 1
    
    PRINT "直线插补完成！"
    PRINT "当前位置：X =", DPOS(0), "mm, Y =", DPOS(1), "mm"
    PRINT "请观察示波器VP_SPEED(0)和VP_SPEED(1)曲线，验证速度分配是否正确"
    PRINT ""
END SUB

REM ===============================================================================
REM                                圆弧插补测试
REM ===============================================================================
GLOBAL SUB TestCircularInterpolation()
    PRINT ""
    PRINT "==============================================================================="
    PRINT "                            圆弧插补测试"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.2.2圆弧插补原理："
    PRINT "在XY平面第一象限走一段逆时针圆弧"
    PRINT ""
    
    '设置VP_SPEED显示单轴速度
    SYSTEM_ZSET = original_zset AND (NOT 1)
    
    '回到起点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    DPOS = 0, 0
    
    FORCE_SPEED = 80                ' 主轴速度80mm/s
    
    PRINT "圆弧参数："
    PRINT "起点：(0, 0)"
    PRINT "终点：(30, 30)"
    PRINT "中间点：(0, 30) - 确定圆弧路径"
    PRINT "方向：逆时针"
    PRINT "主轴速度：80mm/s"
    PRINT ""
    
    PRINT "开始圆弧插补运动..."
    TRIGGER                         ' 触发示波器
    
    '执行三点圆弧插补（逆时针）
    MOVECIRC2ABS(0, 30, 30, 30)     ' 从(0,0)经过(0,30)到(30,30)的圆弧
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '恢复SYSTEM_ZSET设置
    SYSTEM_ZSET = original_zset OR 1
    
    PRINT "圆弧插补完成！"
    PRINT "当前位置：X =", DPOS(0), "mm, Y =", DPOS(1), "mm"
    PRINT "请观察示波器VP_SPEED(0)和VP_SPEED(1)曲线，验证圆弧插补的速度变化"
    PRINT ""
END SUB

REM ===============================================================================
REM                                连续插补测试
REM ===============================================================================
GLOBAL SUB TestContinuousInterpolation()
    PRINT ""
    PRINT "==============================================================================="
    PRINT "                            连续插补测试"
    PRINT "==============================================================================="
    PRINT "根据官方手册5.2连续插补原理："
    PRINT "设置MERGE=ON后，相同主轴的插补运动会自动连续起来"
    PRINT "连续两段运动之间不减速，验证前瞻预处理效果"
    PRINT ""
    
    '设置VP_SPEED显示单轴速度
    SYSTEM_ZSET = original_zset AND (NOT 1)
    
    '回到起点
    MOVEABS(0, 0)
    WAIT IDLE(0)
    WAIT IDLE(1)
    DPOS = 0, 0
    
    '设置连续插补参数
    MERGE = ON                      ' 开启连续插补
    CORNER_MODE = 32                ' 自动倒角设置
    ZSMOOTH = 5                     ' 倒角半径5mm
    FORCE_SPEED = 60                ' 主轴速度60mm/s
    
    PRINT "连续插补参数："
    PRINT "MERGE = ON - 开启连续插补"
    PRINT "CORNER_MODE = 32 - 自动倒角设置"
    PRINT "ZSMOOTH = 5mm - 倒角半径"
    PRINT "FORCE_SPEED = 60mm/s - 主轴速度"
    PRINT ""
    
    PRINT "轨迹规划：正方形路径"
    PRINT "第1段：(0,0) → (40,0) 直线插补"
    PRINT "第2段：(40,0) → (40,40) 直线插补"
    PRINT "第3段：(40,40) → (0,40) 直线插补"
    PRINT "第4段：(0,40) → (0,0) 直线插补"
    PRINT ""
    
    PRINT "开始连续插补运动..."
    TRIGGER                         ' 触发示波器
    
    '执行连续插补（正方形轨迹）
    MOVEABS(40, 0)                  ' 第1段：向右
    MOVEABS(40, 40)                 ' 第2段：向上
    MOVEABS(0, 40)                  ' 第3段：向左
    MOVEABS(0, 0)                   ' 第4段：向下
    
    '等待所有运动完成
    WAIT IDLE(0)
    WAIT IDLE(1)
    
    '关闭连续插补
    MERGE = OFF
    CORNER_MODE = 0
    
    '恢复SYSTEM_ZSET设置
    SYSTEM_ZSET = original_zset OR 1
    
    PRINT "连续插补完成！"
    PRINT "当前位置：X =", DPOS(0), "mm, Y =", DPOS(1), "mm"
    PRINT "请观察示波器VP_SPEED(0)和VP_SPEED(1)曲线："
    PRINT "- 连续插补成功：拐角处速度不降到0，有倒角平滑过渡"
    PRINT "- 连续插补失败：拐角处速度降到0，有明显停顿"
    PRINT ""
END SUB

REM ===============================================================================
REM                                速度监控设置
REM ===============================================================================
GLOBAL SUB SetupSpeedMonitoring()
    PRINT ""
    PRINT "==============================================================================="
    PRINT "                            速度监控设置"
    PRINT "==============================================================================="
    PRINT "示波器监控信号配置："
    PRINT ""
    PRINT "VP_SPEED(0) - X轴单轴速度"
    PRINT "  颜色：蓝色，垂直刻度：100，偏移：0"
    PRINT ""
    PRINT "VP_SPEED(1) - Y轴单轴速度"
    PRINT "  颜色：红色，垂直刻度：100，偏移：-60"
    PRINT ""
    PRINT "MSPEED(0) - X轴分速度（验证用）"
    PRINT "  颜色：绿色，垂直刻度：100，偏移：-120"
    PRINT ""
    PRINT "MSPEED(1) - Y轴分速度（验证用）"
    PRINT "  颜色：黄色，垂直刻度：100，偏移：-180"
    PRINT ""
    PRINT "分析要点："
    PRINT "1. 直线插补：X轴和Y轴速度比例应符合理论计算"
    PRINT "2. 圆弧插补：X轴和Y轴速度应连续变化，呈正弦/余弦关系"
    PRINT "3. 连续插补：拐角处速度不应降到0，应有平滑过渡"
    PRINT ""
    PRINT "TRIGGER已设置，请在ZDevelop中打开示波器观察"
    PRINT "==============================================================================="
    
    TRIGGER                         ' 自动触发示波器
END SUB

PRINT "两轴插补测试demo加载完成！"
PRINT "请按IN0-IN3选择测试项目"
