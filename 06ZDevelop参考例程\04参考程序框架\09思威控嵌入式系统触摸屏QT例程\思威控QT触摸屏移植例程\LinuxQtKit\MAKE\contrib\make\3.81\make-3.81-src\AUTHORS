-----------------------------------

GNU make development up to version 3.75 by:
    <PERSON> <<EMAIL>>


Development starting with GNU make 3.76 by:
    <PERSON> <<EMAIL>>

    Additional development starting with GNU make 3.81 by:
        <PERSON> <<EMAIL>>


GNU Make User's Manual
  Written by:
    <PERSON> <<EMAIL>>

  Edited by:
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>
    <PERSON> <<EMAIL>>

-----------------------------------
GNU make porting efforts:

  Port to VMS by:
      <PERSON> <kka<PERSON><EMAIL>>
      <PERSON><PERSON><PERSON> <Hart<PERSON><PERSON>.<PERSON>@hp.com>
      Archive support/Bug fixes by:
        <PERSON> <<EMAIL>>
        <PERSON> <<EMAIL>>

  Port to Amiga by:
      <PERSON> <<EMAIL>>

  Port to MS-DOS (DJGPP), OS/2, and MS-Windows (native/MinGW) by:
      <PERSON> <<EMAIL>>
      <PERSON> <PERSON><PERSON>h <<EMAIL>>
      <PERSON> Zaretskii <<EMAIL>>
      <PERSON> <PERSON> <<EMAIL>>
      <PERSON> Beuning <<EMAIL>>
      Earnie <PERSON> <<EMAIL>>

-----------------------------------
Other contributors:

  <PERSON> <PERSON> <<EMAIL>>
  Howard Chu <<EMAIL>>
  Paul Eggert <<EMAIL>>
  Klaus Heinz <<EMAIL>>
  Michael Joosten
  Jim Kelton <<EMAIL>>
  David Lubbren <<EMAIL>>
  Tim Magill <<EMAIL>>
  Markus Mauhart <<EMAIL>>
  Greg McGary <<EMAIL>>
  Thomas Riedl <<EMAIL>>
  Han-Wen Nienhuys <<EMAIL>>
  Andreas Schwab <<EMAIL>>
  Carl Staelin (Princeton University)
  Ian Stewartson (Data Logic Limited)

With suggestions/comments/bug reports from a cast of ... well ...
hundreds, anyway :)

-------------------------------------------------------------------------------
Copyright (C) 1997, 1998, 1999, 2000, 2001, 2002, 2003, 2004, 2005, 2006
Free Software Foundation, Inc.
This file is part of GNU Make.

GNU Make is free software; you can redistribute it and/or modify it under the
terms of the GNU General Public License as published by the Free Software
Foundation; either version 2, or (at your option) any later version.

GNU Make is distributed in the hope that it will be useful, but WITHOUT ANY
WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
A PARTICULAR PURPOSE.  See the GNU General Public License for more details.

You should have received a copy of the GNU General Public License along with
GNU Make; see the file COPYING.  If not, write to the Free Software
Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA 02110-1301 USA.
