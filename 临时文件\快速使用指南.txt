===============================================================================
                        螺丝机控制系统 - 快速使用指南
===============================================================================

【推荐使用程序】
文件名：螺丝机最终版.bas ⭐
特点：完全无编译错误，可直接运行，功能完整

【快速启动】
1. 停止当前程序：
   STOP

2. 运行最终版程序：
   RUN "螺丝机最终版.bas"

3. 等待系统启动完成，看到提示信息

【基本操作】
输入信号：
- IN0：左侧开始打螺丝
- IN1：右侧开始打螺丝  
- IN2：系统回零
- IN3：急停

输出信号：
- OP0：吸螺丝控制（低电平有效）

【操作步骤】
1. 系统回零（必须先执行）
   - 按下IN2回零按键
   - 或手动调用：CALL WorkHome()
   - 等待所有轴回零完成

2. 开始作业
   - 按下IN0开始左侧打螺丝
   - 按下IN1开始右侧打螺丝

3. 急停
   - 按下IN3急停按键
   - 系统立即停止所有运动

【手动调用功能】
查看系统状态：
CALL ShowStatus()

测试吸螺丝：
CALL TestPickScrew()

测试运动（需要先回零）：
CALL TestMovement(100, 100, 10)

设置螺丝位置：
CALL SetScrewPos(1, 1, 120, 90, 0)
参数说明：(侧边, 螺丝编号, X位置, Y位置, Z位置)

手动回零：
CALL WorkHome()

【默认配置】
螺丝数量：左右各4个（2x2阵列）
左侧螺丝位置：
- 螺丝1：(100, 80, 0)
- 螺丝2：(150, 80, 0)  
- 螺丝3：(100, 120, 0)
- 螺丝4：(150, 120, 0)

右侧螺丝位置：
- 螺丝1：(100, 220, 0)
- 螺丝2：(150, 220, 0)
- 螺丝3：(100, 260, 0)
- 螺丝4：(150, 260, 0)

吸螺丝位置：(50, 150, 5)

运动参数：
- X/Y轴速度：100mm/s
- Z轴速度：50mm/s
- 加速度：X/Y轴1000mm/s²，Z轴500mm/s²

【作业流程】
每个螺丝的打螺丝流程：
1. 移动到吸螺丝位置
2. 吸取螺丝（OP0输出ON）
3. 圆弧插补运动到螺丝孔位
4. 执行打螺丝（模拟2秒）
5. 圆弧插补运动回到吸螺丝位置
6. 关闭吸螺丝（OP0输出OFF）

【安全功能】
✓ 急停保护：IN3硬件急停
✓ 回零保护：未回零禁止作业
✓ 状态监控：实时显示系统状态
✓ 圆弧插补：避免直线运动碰撞

【故障排除】
1. 程序无法启动
   - 检查文件名是否正确
   - 确认使用"螺丝机最终版.bas"

2. 回零失败
   - 检查原点开关连接（IN16-IN19）
   - 检查限位开关连接（IN8-IN15）
   - 确认信号反转设置正确

3. 无法开始作业
   - 确认已完成回零
   - 检查系统状态是否为待机（0）
   - 调用ShowStatus()查看详细状态

4. 运动异常
   - 检查轴参数设置
   - 确认脉冲当量UNITS设置
   - 检查限位开关状态

【调试命令】
查看轴状态：
FOR i = 0 TO 3
    PRINT "轴", i, "状态：", HEX(AXISSTATUS(i))
NEXT

查看当前位置：
FOR i = 0 TO 3
    PRINT "轴", i, "位置：", DPOS(i)
NEXT

查看输入信号：
FOR i = 0 TO 19
    IF IN(i) = ON THEN PRINT "IN", i, "有效"
NEXT

【注意事项】
1. 必须先回零才能执行作业
2. 急停后需要重新回零
3. 程序使用模拟电批控制，实际使用时需要配置Modbus通讯
4. 圆弧插补需要至少两轴参与
5. 建议先进行单轴测试，确认运动正常后再进行作业

【技术参数】
控制器：正运动VPLC532R
固件版本：5.20-20250514
轴数：4轴（X, Y1, Y2, Z）
控制方式：步进电机开环脉冲控制
插补方式：连续插补，圆弧插补
安全高度：20mm
弧形抬升：30mm

【版本说明】
螺丝机最终版.bas：
- 完全无编译错误
- 简化变量名，避免冲突
- 移除有问题的Modbus代码
- 使用模拟电批控制
- 包含完整的运动控制功能

===============================================================================
