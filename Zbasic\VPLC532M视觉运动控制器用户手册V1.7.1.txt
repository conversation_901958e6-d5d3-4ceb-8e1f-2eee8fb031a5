本手册介绍了产品的安装、接线、接口定义和操作说明等相关内容。

本手册版权归深圳市正运动技术有限公司所有，在未经本公司书面授权的情况下，任何人不得翻印、翻译和抄袭本手册中的任何内容。前述行为均将构成对本公司手册版权之侵犯，本司将依法追究其法律责任。
涉及控制器软件的详细资料以及每个指令的介绍和例程，请参阅 BASIC 软件手册。

本手册中的信息资料仅供参考。由于改进设计和功能等原因，正运动公司保留对本资+料的最终解释权！内容如有更改，恕不另行通知！
调试机器要注意安全！

请务必在机器中设计有效的安全保护装置，并在软件中加入出错处理程序，否则所造成的损失，正运动公司没有义务或责任对此负责。
为了保证产品安全、正常、有效的使用，请您务必在安装、使用产品前仔细阅读本产品手册。


产品型号：VPLC532M 视觉运动控制一体机
文件名	版本号	版本（更改）说明	更新日期	更改人





用户手册	




V1.5	1.增加型号信息，工作环境
2.增加各个端子和接口的规格接线和基本使用方法
3.增加网口说明
4.增加扩展模块资源映射
5.增加编程软件使用方法
6.增加版权声明、安全注意事项、运行与维护及售后服务说明内容	




2023/6/20	




xcx

用户手册	
V1.6	1.修改手册配图
2.增加使能和禁止显示器的支持内容	
2024/5/15	
xcx

用户手册	
V1.7.0	1.更新接口参数
2.产品外观变化，更新配图	
2024/11/11	
xcx
用户手册	V1.7.1	1.更新手册配图	2025/4/17	xcx




本章对正确使用本产品所需关注的安全注意事项进行说明。在使用本产品之前，请先阅读使用说明并正确理解安全注意事项的相关信息。
本产品应在符合设计规格要求的环境下使用，否则可能导致设备损坏，或者人员受伤，因未遵守相关规定引发的功能异常或部件损坏等不在产品质量保证范围之内。
因未遵守本手册的内容、违规操作产品引发的人身安全事故、财产损失等，我司将不承担任何法律责任。


按等级可分为“危险”、“注意”。如果没有按要求操作，可能会导致中度伤害、轻伤及设备损伤的情况。
请妥善保管本指南以备需要时阅读，并请务必将本手册交给最终用户。

安装


危险	控制器拆卸时，系统使用的外部供应电源全部断开后再进行操作，否则可能造成设备误操作或损坏设备；
禁止在以下场合使用：有灰尘、油烟、导电性尘埃、腐蚀性气体、可燃性气体的
场所；暴露于高温、结露、风雨的场合；有振动、冲击的场合；电击、火灾、误操作也会导致产品损坏和恶化。


注意	安装时避免金属屑和电线头掉入硬件电路板内；
安装后保证其硬件电路板上没有异物；
安装时，应使其与安装架紧密牢固；
如果控制器安装不当，可能导致误操作、故障及火灾。
配线


危险	设备外部配线的规格和安装方式应符合当地配电法规要求；
在配线作业时，应将系统使用的外部供应电源全部断开后再进行操作；
配线作业结束后进行通电、运行时，必须安装产品附带的端子；
线缆端子应做好绝缘，确保线缆安装到端子台后，线缆间的绝缘距离不会减少。



注意	安装时避免金属屑和电线头掉入硬件电路板内；
电缆连接应在对所连接的接口的类型进行确认的基础上正确地进行；
应确认压入端子的线缆接触良好；
请勿把控制线及通信电缆与主电路或动力电源线等捆扎在一起，走线应相距 100mm
以上，否则噪声可能导致误动作。
如果控制器安装不当，可能会导致触电或设备故障、误动作；


目录

第一章  产品信息	1
1.1产品简介	1
1.2功能特点	1
1.3系统框图	2
1.4硬件安装	3
第二章  产品规格	4
2.1基本规格	4
2.2铭牌及型号	4
2.3接口定义	5
2.4工作环境	6
第三章  接线通讯设定及组网	7
3.1电源输入、CAN 通讯接口	7
3.1.1电源规格	7
3.1.2CAN 通讯规格和接线	7
3.1.3基本使用方法	8
3.2IN 数字量输入、高速锁存端口、单端编码器	9
3.2.1数字输入规格及接线	9
3.2.2基本使用方法	11
3.3OUT 数字量输出、PWM 端口、硬件比较输出、单端脉冲	11
3.3.1数字输出规格及接线	12
3.3.2基本使用方法	13
3.4COM 串口	13
3.4.1RS232/RS485 通讯接口规格及接线	14
3.4.2基本使用方法	15
3.5LAN 网口	15
3.6EtherCAT 总线接口	16
3.7HDMI 接口	17
3.8USB 接口	18
3.9MPG 手轮接口	18
3.9.1MPG 手轮接口规格及接线	19
3.9.2基本使用方法	20
第四章  扩展模块	21
4.1CAN 总线扩展	21
4.1.1CAN 总线扩展接线	21
4.1.2CAN 总线扩展资源映射	22
4.2EtherCAT 总线扩展	25
4.2.1EtherCAT 总线扩展接线	25
4.2.2EtherCAT 总线扩展资源映射	26
第五章  编程应用	27
5.1RTSys 软件使用	27
5.2固件升级	31
5.2.1使用 RTSys 软件进行固件升级	31
5.2.2zfirmdown 工具软件进行固件升级	34
5.3上位机编程应用	36
第六章  运行与维护	39
6.1定期检查与维护	39
6.2常见问题	40
第七章  售后服务	42

第一章	产品信息	


VPLC532M 是一款总线型的视觉运动控制一体机。控制器本身支持 32 轴的运动控制，用以实现各种电子凸轮、直线、圆弧、连续轨迹加工、机械手等复杂的运动控制与定位、测量、检测及识别等机器视觉应用。
VPLC532M 运动控制器可用于 3C 电子、锂电、印刷包装、食品医药、机械手、协助机器人、半导体和激光等行业应用场合。


VPLC532M 本身最多支持 32 轴EtherCAT 运动控制。
脉冲输出模式：脉冲/方向。
编码器接口支持编码器位置测量，可以配置为手轮输入模式。
专用的手轮输入接口。
每轴最大输出脉冲频率 500kHz。
通过 CAN 总线和 EtherCAT 总线扩展 IO，最多可扩展到 4096 个隔离输入口和 4096 个隔离输出口。
16 路 NPN 型通用输入，其中 2 路高速输入，14 路低速输入，高速输入可配置为锁存信号使用。
16 路 NPN 通用输出，其中 12 路高速输出，4 路低速输出，输出口最大输出电流可达 300mA，可直接驱动部分电磁阀。
通用数字输出口最大输出电流可达 500mA，可直接驱动部分电磁阀。
4 个USB(其中 2 个USB3.0)接口、1 个 COM(RS485 接口/232 接口)接口、2 个千兆以太网接口、1个百兆 EtherCAT 接口。
支持点位运动、电子凸轮、直线插补、圆弧插补、连续插补运动、机械手指令。
支持电子凸轮、电子齿轮、位置锁存、同步跟随、虚拟轴等功能。
支持硬件比较输出(HW_PSWITCH2),硬件定时器,运动中精准输出。
支持脉冲闭环，螺距补偿等功能
支持 Basic 多文件多任务编程。
多种程序加密手段，保护客户的知识产权。
掉电检测，掉电存储。


注意：关闭桌面显示后减少对内存的使用，对实时性有一定提升，具体操作如下：


Linux 命令行输入：# 开机不启动桌面显示

>>> sudo systemctl mask lightdm.service # 恢复启动桌面显示
>>> sudo systemctl unmask lightdm.service


系统框图如下图所示：





通过两枚 M5 螺钉将控制器固定在安装面上。安装时请注意安装的位置，请将控制器正面(操作人员的实际安装面)面向操作人员，并使其垂直于墙壁。(单位：mm、安装孔直径 4.5mm)










安装注意：	
只有受过电气设备相关培训，具有电气知识的专业人员才能操作，严禁非专业人员操作！
安装前请务必仔细阅读产品使用说明书和安全注意事项！
安装前，请确保产品处于断电状态；
请勿拆解模块，否则可能损坏机器；
为了利于通风以及控制器的更换，控制器与周边环境之间应留出 2-3cm；
考虑到对控制器的方便操作及维护，请勿将控制器安装在以下场所：
a)周边环境温度超出-10℃~55℃范围的场所
b)周边环境湿度超出 10%-95%（非凝结）范围的场所
c)有腐蚀性气体、可燃性气体的场所
d)灰尘、铁粉等导电性的粉末、油雾、盐分、有机溶剂较多的场所
e)阳光直射的场所


第二章	产品规格	


项目	描述
型号	VPLC532M-16
基本轴数	16
最多扩展轴数	32
基本轴类型	脉冲轴/编码器轴/EtherCAT 总线轴
数字 IO 数	16 路输入 16 路输出
最多扩展 IO 数	4096 路输入 4096 路输出
PWM 数	2
AD/DA	0
最多扩展 AD/DA	AD 512 路、DA 512 路
脉冲位数	64
编码器位数	64
速度加速度位数	64
脉冲最高频率	500kHz
每轴运动缓冲数	4096
数组空间	1280000
程序空间	64MB
Flash 空间	512MB
电源输入	24V 直流输入
通讯接口	RS232、RS485、USB、以太网、CAN、EtherCAT
外形尺寸	162*47*102mm


铭牌信息：


型号	规格描述
VPLC532M-6-8	6 轴，点位运动，电子凸轮，直线插补，圆弧插补，连续插补运动，机械手指令


VPLC532M-8	8 轴，点位运动，电子凸轮，直线插补，圆弧插补，连续插补运动，机械手指令
VPLC532M-16	16 轴，点位运动，电子凸轮，直线插补，圆弧插补，连续插补运动，机械手指令
VPLC532M	32 轴，点位运动，电子凸轮，直线插补，圆弧插补，连续插补运动，机械手指令

注：上表中型号除轴资源不同，其他资源相同，均可参考本手册。



接口说明如下表：

标识	接口	个数	说明
POW	
状态指示灯	1 个	电源指示灯：电源接通时亮灯
RUN		1 个	运行指示灯：正常运行时亮灯
ALM		1 个	错误指示灯：运行错误时亮灯
RS232	RS232(port0)串口	1 个	采用 MODBUS_RTU 协议
RS485	RS485(port1)串口	1 个	采用 MODBUS_RTU 协议

ETHERNET	
网口	
2 个	采用 MODBUS_TCP 协议，通过交换机扩展网口个数， ?*port 查询网口通道数，默认 IP 地址 
************
EtherCAT	EtherCAT 总线接口	1 个	EtherCAT 总线接口，接 EtherCAT 总线驱动器和
EtherCAT 总线扩展模块
E+24V	主电源	1 个	24V 直流电源给控制器供电
E5V	5V 电源输出	1 个	用于提供 PWM 或者单端轴扩展时共阳极使用
USB	USB3.0/2.0	4 个	向下兼容 USB2.0、USB1.0 接口，可以连接相机、鼠
标、键盘、U 盘等 USB 外设
CAN	CAN 总线接口	1 个	连接 CAN 扩展模块或标准 CAN 设备
IN	数字 IO 输入口	16 个	NPN 型，部分输入口兼容锁存和单端编码器的功能


OUT	数字 IO 输出口	16 个	NPN 型，部分输出口兼容 PWM、HW 和单端脉冲轴功能
MPG	手轮接口	1 个	5-24V 手轮信号输入


项目	参数
工作温度	-10℃～55℃
工作相对湿度	10%-95%非凝结
储存温度	-40℃～80℃(不冻结)
储存湿度	90%RH 以下(不结露)


振动	频率	5-150Hz
	位移	3.5mm(直接安装) (<9Hz)
	加速度	1g(直接安装) (>9Hz)
	方向	3 轴向
冲击(碰撞)	15g，11ms，半正弦波，3 轴向
防护等级	IP20


第三章	接线通讯设定及组网	


电源输入采用 5Pin 间距为 3.81mm 的螺钉式可插拔接线端子，该端子为控制器电源和 CAN 通讯共用。


端子	名称	类型	功能

	EGND	输入	电源地/CAN 通讯公共端
	CANL	输入/输出	CAN 差分数据-
	EARTH	接地	安规地/屏蔽层
	CANH	输入/输出	CAN 差分数据+
	E+24V	输入	电源 24V 输入



项目	说明
输入电压	DC24V(-5%~5%)
启动电流	≤1A
工作电流	≤1.2A
防反接	有
过流保护	有


控制器的 CAN 接口采用标准 CAN 通讯协议，主要包含三个端子，CANL、CANH 和公共端。支持连接扩展模块和其他标准 CAN 设备。


项目	说明
最大通讯速率	1Mbps
终端电阻	120Ω
拓扑结构	菊花链连接结构
可扩展节点数	最大 16 个
通讯距离	通讯距离越长通讯速率越低，建议最大 30m



将标准 CAN 模块的 CANL 和 CANH 分别连接对方的 CANL 和 CANH，CAN 总线通讯双方的公共端连接在一起，在 CAN 总线的左右两端各接一个 120 欧的电阻。



如上为菊花链拓扑结构接线，不可采用星型拓扑结构，当使用环境较为理想并且节点较少时也可考虑分支结构；
请在 CAN 总线最两端接口各并接一个 120Ω 的终端电阻，匹配电路阻抗，保证通讯稳定性；
请务必连接 CAN 总线上各个节点的公共端，以防止 CAN 芯片烧坏；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地；
现场布线还要注意强电和弱电布线要拉开距离，建议 20cm 以上；
要注意整个线路上的设备接地（机壳）要良好，机壳的接地要接在标准的厂房地桩上。


双绞屏蔽线，屏蔽电缆接地：



1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232 任一种接口连接 RTSys；

3.请使用“CANIO_ADDRESS”指令根据需要设置主端“地址”和“速率”，“CANIO_ENABLE”指令设置使能或禁止内部 CAN 主端功能，也可以通过“RTSys/控制器/控制器状态/通讯配置”界面直观查看 CAN 状态，详细说明见“Basic 编程手册”；


4.根据从站手册说明正确设置从站扩展板的“地址”和“速率”；

5.全部设置完成后重启所有站点电源即可建立通讯；

6.注意 CAN 总线上每个节点的“速率”设置必须一致，“地址”设置不能够产生冲突，否则
“ALM”告警灯会亮起，通讯建立失败或者通讯错乱。


数字量输入采用间距为 3.81mm 的螺钉式可插拔接线端子，数字输入信号集成有高速锁存和单端编码器功能。



引脚号	名称	类型	功能 1	功能 2	功能 3
2	EGND	/	IO 公共端	/	/
4	IN0	
NPN 型，高速输入	开关输入 0	高速锁存 A	EA1
6	IN1		开关输入 1	高速锁存 B	EB1
8	IN2	




NPN 型，低速输入	开关输入 2	/	EZ1
10	IN3		开关输入 3	/	/
12	IN4		开关输入 4	/	/
14	IN5		开关输入 5	/	/
16	IN6		开关输入 6	/	/
18	IN7		开关输入 7	/	/
20	IN8		开关输入 8	/	/
22	IN9		开关输入 9	/	/
24	IN10	


NPN 型，低速输入	开关输入 10	/	/
26	IN11		开关输入 11	/	/
28	IN12		开关输入 12	/	/
30	IN13		开关输入 13	/	/
32	IN14		开关输入 14	/	/
34	IN15		开关输入 15	/	/
36	EGND	/	IO 公共端	/	/




项目	高速输入（IN0-1）	低速输入（IN2-15）
输入方式	NPN 型，低电平输入触发	NPN 型，低电平输入触发
输入频率	＜100kHz	＜5kHz
输入阻抗	4.7kΩ	4.7kΩ
输入电压等级	DC24V	DC24V
输入开启电压	<14.5V	<14.5V
输入关闭电压	>14.7V	>14.7V
最小输入电流	-1.8mA	-1.8mA
最大输入电流	-6mA	-6mA
隔离方式	电容隔离	电容隔离
注意：以上参数是当控制器电源电压（E+24V 端口）为 24V 时的典型值。




本例以采用 IN0-2 连接编码器来说明，接线完成通过 ATYPE(1)=3 配置后 IN 即可作为编码器输入信号使用，其中 IN0 为 EA1、IN1 为 EB1、IN2 为 EZ1，对应的编码器轴号为 1。


高速数字输入 IN（0-1）和低速数字输入 IN（2-15）接线原理如上图，外部信号源可以是光耦也可以是按键开关或传感器等，只要输出电平满足要求均可接入；
公共端请选择 IO 端子上的“EGND”端口与外部输入信号的“COM”端连接，如果外部设备该信号区域电源与控制器电源在同一个供电系统中，也可以省略该连接。




1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232 任一种接口连接 RTSys；

3.可通过“IN”指令直接读取相应输入口的状态值，也可以通过“RTSys/工具/输入口”界面直观查看输入口状态，详细说明见“Basic 编程手册”；
4.锁存功能可通过“REGIST”指令进行设定启用，软件里面使用 REG_INPUTS 配置。详细说明见
“Basic 编程手册”。


数字量输出采用间距为 3.81mm 的螺钉式可插拔接线端子，数字输出信号中集成有 PWM 功能和硬件比较输出功能。



引脚号	名称	类型	功能 1	功能 2	功能 3
1	EGND	/	E5V 电源地/
IO 公共端	/	/
3	OUT0	
NPN 型，高速输出	开关输出 0	PWM 输出 0	硬件比较输出 0
5	OUT1		开关输出 1	PWM 输出 1	硬件比较输出 1
7	OUT2	

NPN 型，低速输出	开关输出 2	/	/
9	OUT3		开关输出 3	/	/
11	OUT4		开关输出 4	/	/
13	OUT5		开关输出 5	/	/
15	OUT6	





NPN 型，高速输出	开关输出 6	/	/
17	OUT7		开关输出 7	/	/
19	OUT8		开关输出 8	DIR3	/
21	OUT9		开关输出 9	PUL3	/
23	OUT10		开关输出 10	DIR2	/
25	OUT11		开关输出 11	PUL2	/
27	OUT12		开关输出 12	DIR1	/
29	OUT12		开关输出 13	PUL1	/
31	OUT14		开关输出 14	DIR0	/
33	OUT15		开关输出 15	PUL0	/
35	E5V	/	5V 电源输出，	/	/


			最大 500mA		
注意：
1.OUT0-1 具有 PWM 和硬件比较输出的功能；
2.OUT8-15 有轴 0-3 的功能，ATYPE=0 时，为普通输出口；
3.E5V 电源输出口用于 PWM 或单端轴扩展时共阳极接线使用，功率较小不建议用于其他用途。



项目	高速输出（OUT0-1、OUT6-15）	低速输出（OUT2-5）
输出方式	NPN 型，输出时为 0V	NPN 型，输出时为 0V
输出频率	＜400kHz	＜8kHz
输出电压等级	DC24V	DC24V
最大输出电流	+500mA	+500mA
关闭时最大漏电流	25μA	25μA
导通响应时间	1μs(阻性负载典型值)	12μs
关闭响应时间	3μs	80μs
过流保护	支持	支持
隔离方式	电容隔离	电容隔离
注意
1.表中的时间都是基于阻性负载的典型值，负载电路有变化时可能会有变化；
2.由于漏型输出，输出的关闭会比较明显受外部负载电路的影响，应用中输出频率不宜设置太高，高速输出建议在 400kHz 以下，低速输出建议 8kHz 以下，如有更高速需求，需联系我们调整参数或定制硬件。




本例以采用 OUT8 和OUT9 连接驱动器来说明，OUT8、OUT9 通过 ATYPE(3)=1 配置后，OUT8 为 DIR3，OUT9
为 PUL3，对应的脉冲驱动器轴号为 3。驱动器根据规格可接 E24V 或E5V。





高速数字输出 OUT（0-1、6-15）和低速数字输出 OUT（2-5）接线原理如上图，外部信号接收端可以是光耦也可以是继电器或电磁阀等，只要输入电流不超过 500mA 均可接入；
公共端的连接请选择 IO 端子上的“EGND”端口与外部输入设备直流电源的负极连接，如果外部设备的直流电源与控制器电源在同一个供电系统中，也可以省略该连接。


1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232 任一种接口连接 RTSys；

3.可通过“OP”指令直接操作端口开启或关闭，也可以通过“RTSys/工具/输出口”界面直接点击进行开启或关闭，详细说明见“Basic 编程手册”；
4.PWM 功能可通过“PWM_FREQ”和“PWM_DUTY”指令分别设定频率和占空比进行使用，详细说明见
“Basic 编程手册”；

5.高速比较输出可通过“HW_PSWITCH2”指令进行设定启用，详细说明见“Basic 编程手册”。


RS232 和RS485 集成在一个标准 DB9 公座中，支持 MODBUS_RTU 协议和自定义通讯。


端子	引脚号	名称	类型	功能

	1、6、8、9	NC	悬空	预留
	2	232RXD	输入	RS232（port0）信号接收
	3	232TXD	输出	RS232（port0）信号发送
	4	485A/+	输入/输出	RS485（port1）信号 A/+
	5	EGND	输出	通讯公共端
	7	485B/-	输入/输出	RS485（port1）信号 B/-





项目	RS232（port0）	RS485（port1）
最大通讯速率（bps）	115200	115200
终端电阻	无	120Ω
拓扑结构	1 对 1 连接	菊花链结构
可扩展节点数	1	127

通讯距离	通讯距离越长通讯速率越低，建议
最大 5m	通讯距离越长通讯速率越低，建
议最大 30m




RS232（port0）的接线如上，收发信号需交叉接线，与电脑连接时建议采用双母头的交叉线；
RS485（port1）的接线如上为菊花链拓扑结构接线，不可采用星型拓扑结构，当使用环境较为理想并且节点较少时也可考虑分支结构；
请务必连接 RS485 总线上各个节点的公共端，以防止 RS485 芯片烧坏；
当从站较多时，建议在 RS485 总线回路的左右两端各接入一个 120 欧姆的电阻阻抗匹配；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地；
现场布线还要注意强电和弱电布线要拉开距离，建议 20cm 以上；
要注意整个线路上的设备接地（机壳）要良好，机壳的接地要接在标准的厂房地桩上。




1.请按照以上接线说明正确接线；

1.上电后请选用 ETHERNET、RS232（默认参数可直接连接）RS485（默认参数可直接连接，硬件需使用转接头）三种任一种接口连接 RTSys；
2.请使用“ADDRESS”和“SETCOM”指令设置和查看协议站号和配置参数，详细说明见“ZBasic 编程手册”；
3.根据各自说明正确设置第三方设备相关参数使各个节点参数匹配；

4.全部设置完成后即可开始通讯；

5.可通过“RTSys/控制器/控制器状态/通讯配置”界面直接查看 RS232/RS485 的通讯数据。


该系列主板上带有 2 个千兆以太网口，详情见选型介绍。采用标准的 RJ45 接口，支持 MODBUS_TCP 协议和自定义通讯。
网口的出厂默认 IP 地址为 LAN1：************；LAN2：************。
网口支持配置为 EtherCAT 总线接口。



网口	
引脚号	信号
		100BASE	1000BASE




	1	TX+	TRD0+
	2	TX-	TRD0-
	3	RX+	TRD1+
	4	NC	TRD2+
	5	NC	TRD2-
	6	RX-	TRD1-
	7	NC	TRD3+
	8	NC	TRD3-
注意：
1.RJ45 上带有 2 个LED 灯，分别表示网络 Link 以及数据传输(Transmit)，当网络正常连接时，
Link 灯显示为绿色常亮
2.为百兆数据传输时，Transmit 灯为绿色闪烁；为千兆数据传输时，Transmit 灯为橙色闪烁。


项目	规格
网络类型	1000BASE-T/100BASE-TX/10BASE-T


传输速度	1000Mbps/100Mbps/10Mbps
最大线缆距离	100m/segment
网卡类型	Intel® Ethernet Controller
注意：当传输速度为 1000Mbps 时则需要至少为 CAT 5e 及以上的网线。


VPLC532M 产品有一个百兆 EtherCAT 通讯接口，支持 EtherCAT 总线协议，用于连接 EtherCAT 总线驱动器或 EtherCAT 总线扩展模块。


ECAT 接口	引脚号	信号

	1	TX+
	2	TX-
	3	RX+
	4	NC
	5	NC
	6	RX-
	7	NC
	8	NC
注意：RJ45 上带有 2 个 LED 灯，分别表示网络 Link 以及数据传输(Transmit)，当网络正常连接
时，Link 灯显示为绿色常亮，当有数据传输时，Transmit 灯为黄色闪烁。


项目	规格
通讯协议	EtherCAT 协议
支持服务	CoE（PDO、SDO）、FoE
同步方式	IO 采用输入输出同步或 DC-分布式时钟
物理层	100BASE-TX
双工方式	全双工
拓扑结构	线性拓扑结构
传输媒介	网线
传输距离	两节点间小于 100m
过程数据	单帧最大 1486 字节
两个从站的同步抖动	<1us
刷新	1000 个开关量输入输出约 30us



Ethernet 通讯接口和 EtherCAT 总线接口采用标准以太网 RJ45 接口。

网线选用超五类屏蔽的网线，水晶头带有金属壳，以减小干扰，防止信息被窃听。如下图所示：


项目	规格
电缆类型	弹性交叉电缆，超五类
导线类型	双绞线
线对	4
隔离	十字骨架
接头	带铁壳水晶头
线缆材质	PVC 材质
线缆长度	不超过 100 米
采用 RJ45 网线接法：

安装时，握住带线的水晶头，插入 RJ45 接口直至发出“喀哒”声；
为确保通讯的稳定性，请将线缆用扎线带等进行固定；
拆卸时，按住水晶头尾部机构将连接器与模块呈水平方向拔出；请使用管型预绝缘端子和合适线径的线缆来进行用户端子的接线。

标准 HDMI 高清多媒体显示接口，用于连接显示屏。


HDMI 接口	引脚号	信号	引脚号	信号



	1	TMDS DATA 2+	11	TMDS CLOCK SHIELD
	2	TMDS DATA 2 SHIELD	12	TMDS CLOCK-
	3	TMDS DATA 2-	13	CEC
	4	TMDS DATA 1+	14	N.C.
	5	TMDS DATA 1 SHIELD	15	DDC CLOCK
	6	TMDS DATA 1-	16	DDC DATA
	7	TMDS DATA 0+	17	GND
	8	TMDS DATA 0 SHIELD	18	+5V PWR
	9	TMDS DATA 0-	19	HOT PLUG DETECT
	10	TMDS CLOCK+	




前面板提供 4 个独立的 USB TYPE-A 接口。为 USB3.0 接口，向下兼容 USB2.0，可用于插 U 盘、鼠标、键盘等设备。


USB3.0 接口	引脚号	信号
	1	VCC5
	2	DATA-
	3	DATA+
	4	GND
	5	SSRX-
	6	SSRX+
	7	GND
	8	SSTX-
	9	SSTX+


项目	USB3.0
最高通讯速率	5.0Gbps
VCC（5V）最大输出电流	500mA
是否隔离	否


该产品提供 1 个本地手轮编码器轴专用接口，接口为双排标准 DB15 母座。


接口	引脚号	信号	说明


	1	H-5V	5V 电源输出正极，仅为手轮供电
	2	H-A	编码器 A 相信号(IN16)
	3	H-B	编码器 B 相信号(IN17)
	4	H-EMGN	紧急停止信号(IN27)
	5	NC	悬空
	6	H-X1	选择 X1 倍率(IN18)
	7	H-X10	选择 X10 倍率(IN19)
	8	H-X100	选择 X100 倍率(IN20)
	9	H-S4	选择轴 3(IN24)
	10	H-S5	选择轴 4(IN25)


	11	EGND	外部电源地
	12	H-S6	选择轴 5(IN26)
	13	H-SZ	选择轴 2(IN23)
	14	H-SY	选择轴 1(IN22)
	15	H-SX	选择轴 0(IN21)



项目	IN（16-27）
输入方式	NPN 型，低电平输入触发
输入频率	推荐＜5kHz
输入阻抗	510Ω
最大输入电压	24V
输入开启电压	<2.8
输入关闭电压	>2.9V
最小输入电流	-1.8mA
最大输入电流	-5.5mA
隔离方式	光电隔离
5V 电源（H-5V，EGND）最大输出电流	100mA






手轮编码器轴接口接线原理如上图所示，手轮设计多种多样，请谨慎连接；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地。


1.参考以下手轮接线示意图正确连接手轮和控制器；

2.上电后请选用 ETHERNET、RS232 任一种接口连接 RTSys；
3.配置轴号，该控制器手轮接口默认 AXIS 轴号为 6，不需要重映射，如下； BASE(6)	'轴选
ATYPE(6)=6   '将轴接口轴类型设为所需要的类型，比如 3 或 6

4.配置 IO：根据需要赋予轴选（H-SX,H-SY,H-SZ,H-S4,H-S5,H-S6）和倍率（HX1,HX10,HX100）以及
紧急停止（HEMGN）功能；这些信号本质为数字输入信号，有固定的编号，但无固定的功能，需要 RTSys 开发；其名称为推荐配置的功能，轴选即为 connect 同步运动的被连接轴，倍率即 connect比率；
5.完成以上配置即可开始使用手轮。


第四章	扩展模块	

控制器可通过 CAN 总线或 EtherCAT 总线扩展资源，支持扩展数字量 IO、模拟量 AD/DA、脉冲轴资
源，可选搭配 ZIO 系列 CAN 总线扩展模块、EIO 系列 EtherCAT 总线扩展模块或 ZMIO310 系列立式总线扩展模块，各扩展模块详情请参考对应产品用户手册。


可选 ZIO 系列扩展模块或 ZMIO310-CAN 耦合器带子模块。


ZIO 扩展模块为双电源供电，除了主电源，需要额外再接一个 IO 电源，给 IO 独立供电，主电源与 IO
电源均采用 24V 直流电源。ZAIO 模拟量扩展模块只需接主电源，无需 IO 电源。为防止干扰，IO 电源和主电源分开。
请根据需求选择扩展模块，根据扩展模块资源选择 IO 映射或轴映射，注意映射的编号需避开已有资源。。
ZIO 扩展模块连接控制器实物接线参考示例和 CAN 总线标准接线如下图所示：





VPLC532M 控制器采用单电源供电，ZIO 扩展模块采用双电源供电，使用时扩展模块的主电源和控制器的主电源可共用一路电源。控制器和 ZIO 扩展模块用不同电源供电时，控制器电源 EGND 要连接扩展模块电源的 GND，否则可能烧坏 CAN。
CAN 总线上连接多个 ZIO 扩展模块时，在 CAN 总线的左右两端各接一个 120 欧的电阻，对于具有
8 位拨码的扩展模块，终端电阻可通过拨码实现。



ZCAN 扩展板一般带 8 位拨码开关，拨 ON 生效，拨码含义如下：

1-4：4 位 CAN ID 用于 ZCAN 扩展模块IO 地址映射，对应值 0-15；

5-6：CAN 通讯速度，对应值 0-3，可选四种不同的速度；

7：预留；

8：120 欧电阻，拨 ON 表示 CANL 和 CANH 间接入一个 120 欧电阻。

整个控制系统的 IO 编号不得重复，映射资源时需避开已有编号。拨码开关必须在上电之前拨好，上电后重新拨码无效，需再次上电才生效。
拨码 1-4 选择 CAN 地址，控制器根据 CAN 拨码地址来设定对应扩展模块的 IO 编号范围，拨码每位 OFF
时对应值 0，ON 时对应值 1，地址组合值=拨码 4×8+拨码 3×4+拨码 2×2+拨码 1；

拨码 5-6 选择 CAN 总线通讯速度，速度组合值=拨码 6×2+拨码 5×1，组合值范围 0-3。对应的速度如下所示：







控制器端通过 CANIO_ADDRESS 指令设置 CAN 通讯速度，同样也是有四种速度参数可供选择，需要与组合值对应的扩展模块的通讯速度一致才可以互相通讯。
出厂默认通讯速度两边都是 500kbps，不需要设置这块，除非要改速度才需要用指令设置通讯速度。

CANIO_ADDRESS 指令为系统参数， 还可以设置 CAN 通讯的主从端，控制器的缺省值 32 ，即
CANIO_ADDRESS=32 做主端，设置为 0-31 之间做从端。

CAN 通讯配置情况可在“控制器状态”窗口查看通讯配置。




CAN 扩展模块 IO 映射使用拨码开关 1-4 位，根据当前已包含 IO 点数（IN 和 OP 中最大编号，需包含轴接口内的 IO 点），使用 1-4 号拨码设置 ID，从而确定扩展 IO 的编号范围。
如控制器本身包含 28 个IN，16 个 OP，那么第一个扩展版设置的起始地址应超过最大值 28，按下图规则应将拨码设置为组合值 1（二进制组合值 0001，从右往左对应拨码 1-4，此时拨码 1 置 ON，其他置 OFF），扩展版上的 IO 编号=扩展版编号值+起始 IO 编号值，其中，29-31 空缺出来的 IO 编号舍去不用。后续的扩展版则依次按 IO 点数继续确认拨码设置。
数字量起始 IO 映射编号从 16 开始，按 16 的倍数递增，不同拨码 ID 对应数字量 IO 编号分配情况如下
表：

拨码 1-4 组合值	起始 IO 编号	结束 IO 编号
0	16	31
1	32	47
2	48	63
3	64	79
4	80	95
5	96	111
6	112	127
7	128	143
8	144	159
9	160	175
10	176	191
11	192	207
12	208	223
13	224	239
14	240	255
15	256	271

模拟量 AD 起始 IO 映射编号从 8 开始，按 8 的倍数递增。模拟量 DA 起始 IO 映射编号从 4 开始，按 4
的倍数递增。不同拨码 ID 对应数字量 IO 编号分配情况如下表：

拨码 1-4 组合值	起始 AD 编号	结束 AD 编号	起始 DA 编号	结束 DA 编号
0	8	15	4	7
1	16	23	8	11
2	24	31	12	15
3	32	39	16	19
4	40	47	20	23
5	48	55	24	27
6	56	63	28	31
7	64	71	32	35


8	72	79	36	39
9	80	87	40	43
10	88	95	44	47
11	96	103	48	51
12	104	111	52	55
13	112	119	56	59
14	120	127	60	63
15	128	135	64	67


CAN 总线扩展方式扩展脉冲轴时，扩展两个脉冲轴，这两个脉冲轴需要映射绑定轴号后访问。扩展轴需要进行轴映射操作，采用 AXIS_ADDRESS 指令映射，映射规则如下：
AXIS_ADDRESS(轴号)=(32*0)+ID	'扩展模块的本地轴接口 AXIS 0
AXIS_ADDRESS(轴号)=(32*1)+ID	'扩展模块的本地轴接口 AXIS 1
ID 为扩展模块 1-4 位地址拨码的组合值，映射完成设置 ATYPE 等轴参数后就可以使用扩展轴。示例：
ATYPE(6)=0	'设为虚拟轴
AXIS_ADDRESS(6)=1+(32*0)	'ZCAN 扩展模块 ID 为 1 的轴号 0 映射到轴 6 ATYPE(6)=8	'ZCAN 扩展轴类型，脉冲方向方式步进或伺服
UNITS(6)=1000	'脉冲当量 1000
SPEED(6)=100	'速度 100units/s
ACCEL(6)=1000	'加速度 1000units/s^2
MOVE(100) AXIS(6)	'扩展轴运动 100units
扩展资源查看：
按 CAN 接线，电源接通后，接线电阻拨码都设置正确，模块上的电源指示灯(POWER)、运行灯(RUN)亮、 IO 电源灯(IO POWER)亮，报警灯(ALM)不亮。同时 RTSys 软件的“控制器”-“控制器状态”-“ZCan 节点”显示扩展模块信息和扩展的 IO 编号范围。
连接多个扩展模块时的拨码 ID 与对应资源编号参考如下：


ALM 指示灯亮请检查接线，电阻以及拨码设置是否正确，以及控制器的 CANIO_ADDRESS 指令是否设置为主端(32)，CAN 通讯速度是否一致。


EIO 系列扩展模块和 ZMIO310-ECAT 是 EtherCAT 总线控制器使用的扩展模块，例如 EIO 系列可扩展数字量 IO 和脉冲轴这两类资源，当控制器本体上资源不够的时候，EtherCAT 总线控制器可连接多个 EIO 扩展模块进行扩展，可查看控制器的最大扩展 IO 点数和最大扩展轴数，支持 IO 的远程扩展。


每个 EIO 扩展模块在扩展接线完成后，不需要进行进行二次开发，只需手动在 EtherCAT 主站控制器配置扩展模块唯一的 IO 地址和轴地址，配置完成即可访问。
IO 地址编号通过总线指令 NODE_IO 来设置，控制器上程序只需通过 IO 编号就可以访问到扩展模块上的资源。轴地址的配置使用 AXIS_ADDRESS 指令映射绑定轴号，绑定完成通过 BASE 或 AXIS 指令指定轴号。
接线时注意 EtherCAT IN 连接上一级模块，EtherCAT OUT 连接下一级模块，IN 和 OUT 口不可混用。

EIO 扩展模块接线参考示例：

上图涉及的编号概念如下：总线相关指令参数会用到如下编号：

1.槽位号(slot)

槽位号是指控制器上总线接口的编号，EtherCAT 总线槽位号为 0。

2.设备号(node)

设备号是指一个槽位上连接的所有设备的编号，从 0 开始，按设备在总线上的连接顺序自动编号，可以通过 NODE_COUNT(slot)指令查看总线上连接的设备总数。
3.驱动器编号


控制器会自动识别出槽位上的驱动器，编号从 0 开始，按驱动器在总线上的连接顺序自动编号。

驱动器编号与设备号不同，只给槽位上的驱动器设备编号，其他设备忽略，映射轴号时将会用到驱动器编号。



控制器上程序只需通过 IO 编号就可以访问到扩展模块上的资源，EtherCAT 总线扩展模块 IO 编号通过总线指令 NODE_IO 来设置，同时配置输入和输出。
IO 映射时先查看控制器自身的最大 IO 编号(包括外部 IO 接口和脉冲轴内的接口)，再使用指令设置。

若扩展的 IO 与控制器自身 IO 编号重合，二者将同时起作用，所以 IO 映射的映射的编号在整个控制系统中均不得重复。
IO 映射语法： NODE_IO(slot,node)=iobase slot：槽位号，0-缺省  node：设备编号，编号从 0 开始
iobase：映射 IO 起始编号，设置结果只会是 8 的倍数示例：
NODE_IO(0,0)= 32	'设置槽位 0 接口设备 0 的 IO 起始编号为 32

若设备 0 为 EIO16084，按如上语法配置后，输入 IN0-15 对应的 IO 编号依次为 32-47，轴接口内的通用输入口编号 48-55，轴 AXIS 0-3 的驱动报警输入分别为 48-51。输出 OUT0-7 应的 IO 编号依次为 32-39，轴接口内的通用输出口编号 40-47，轴AXIS 0-3 的驱动使能输出分别为 40-43。


扩展模块的轴使用前需要使用 AXIS_ADDRESS 指令映射轴号，轴映射也需要注意整个系统的轴号不得重复。EIO 系列扩展轴的映射与总线驱动器的轴映射语法相同。
轴映射语法：

AXIS_ADDRESS(轴号)=(槽位号<<16)+驱动器编号+1

示例：

AXIS_ADDRESS(0)=(0<<16)+0+1	'EtherCAT 总线上的第一个驱动器，驱动器编号 0，绑定为轴 0 AXIS_ADDRESS(1)=(0<<16)+1+1	'EtherCAT 总线上的第二个驱动器，驱动器编号 1，绑定为轴 1
若第一个节点是 EIO16084，EIO16084 上连接了驱动器，那么这里的驱动器 0 是连接在 EIO16084 上的第一个脉冲驱动器，否则便是 EtherCAT 驱动器。


第五章	编程应用	


RTSys 是正运动技术 ZMotion 系列运动控制器的 PC 端程序开发调试与诊断软件，通过它用户能够很容易的对控制器进行程序编辑与配置，快速开发应用程序、实时诊断系统运行参数以及对运动控制器正在运行的程序进行实时调试，支持中英双语环境。
Basic、Plc、Hmi 和 C 语言之间可以多任务运行，其中 Basic 可以多任务号运行，可与 Plc、Hmi 与 C
混合编程。

更新软件版本请前往正运动网站下载，网址：www.zmotion.com.cn。

步骤	操作	显示界面

















1	












打开 RTSys 编程软件，菜单栏“文件”-“新建项目”弹出另存为界面，输入文件名后保存形式后缀为 “.zpj”的项目文件。	

















2	








菜单栏“文件”-
“新建文件”，出现右图弹窗，选择新建的文件类型为basic后确认。支持Basic/ Plc/Hmi混合编程。	


3	
文件视图窗口双击文件右边自动运行的位置，输入任务号“0 ”。	









4	





在程序输入窗口编辑好程序，点击保存文件，新建的basic文件会自动保存到项目 zpj所在的文件下。
保存所有即保存该项目下的所有文件。	
















5	





点击“控制器”-
“连接”-
“控制器”，没有控制器时可选择连接到仿真器仿真运行，点击“连接”-
“仿真器”。	

	
点击“连接”弹出“连接到控制器”窗口
，可选择串口连接或网口连接，选择匹配的串口参数或网口IP地址后，点击连接即可。	









6	点击菜单栏- “控制器”- “RAM/ROM”-
“下载到RAM”/“下载到ROM”，下载成功命令和输出窗口会有提示，同时程序下载到控制器并自动运行。
RAM下载掉电后程序不保存，ROM下载掉电后程序保存。下载到ROM的程序下次连接上控制器之后程序会自动按照任务号运行。	成功下载到RAM：


成功下载到ROM：












7	





点击菜单栏“调试”
-
“启动/停止调试”
调用任务与监视窗口
。因为之前下载过了
，这里选择附加到当前程序即可。	







8	


在菜单栏“工具”- “示波器”打开示波器窗口
示波器使用参见正运动小助手“快速入门
|篇九：如何进行运动控制器示波器的应用”。	

说明：
1.打开工程项目时，需选择打开项目zpj文件，若只打开其中的Bas文件，程序无法下载到控制器。
2.不建立项目的时候，只有Bas文件无法下载到控制器。
3.自动运行的数字0表示任务编号，以任务0运行程序，任务编号不具备优先级。
4.若整个工程项目内的文件都不设置任务编号，下载到控制器时，系统提示如下信息WARN: no program set autorun.




可以使用 RTSys 软件或者 zfirmdown 工具软件下载 zfm 固件包，进行固件升级，接下来我们分别介绍其步骤。
zfm 文件为控制器固件升级包，根据对应的控制器型号选择对应的固件（不同型号的固件包不一样，确保选择正确的固件包，如需固件升级，请联系厂家）。


步骤	操作	显示界面








1	





打开RTSys软件，通过串口/网口连接控制器，在命令与输出窗口看到图示语句表示连接成功。	











2	





点击菜单栏“控制器
”-
“控制器状态”，可以看到当前软件版本
o	








3	


点击菜单栏“控制器
”-
“固件升级”，可以看到：控制器型号以及软件版本。	











4	







点击“浏览”，选择已保存的固件文件，点击“升级”
弹出“控制器需要重启到ZBIOS？”选项
，点击“确定”。	








5	


再次弹出“连接到控制器”界面，选择正确的IP地址，点击“连接”。	











6	





连接成功后，弹出“固件升级”界面，系统进入ZBIOS状态，再次点击“升级”。	




7	

进度条满格后，“固件升级”界面消失，控制器输出框如下，显示固件升级成功。	









8	





重复上述步骤1和步骤2，重新连接控制器、查看控制器状态如下图，可看到软件版本已更新，固件升级完成。	





步骤	操作	显示界面







1	


打开 zfirmdown软件，通过串口/网口连接控制 器。
显示“ 链接成 功”，可以看到当前硬件版本和固件版本。	








2	



点击“Browse 选择”，选择已保存的最新固件文 件，点击“Update升级”。	








3	



连接断开，控制器 需 要 进 入 ZBIOS 状态，需要重新连接，点击 “确定”，并重新连接控制器。	










4	





显示“链接 ZBIOS成功 ”， 点击 “Update 升级”。	





5	

再次弹出“连接到控制器”界面，选择正确的 IP地址，点击“ 连接”。	








6	





固件升级成功，此时连接断开，点击“确定”。	








7	




点击“链接”，显示“链接成功”，固件版本已更 新，控制器的固件升级成功。	





控制器支持 windows，linux，Mac，Android，wince 各种操作系统下的开发，提供 vc，c#，vb.net， labview 等各种环境的 dll 库，如下图。上位机软件编程参考《ZMotion PC 函数库编程手册》。


可使用 PC 上位机软件通过调用动态库连接到控制器，也支持自身嵌入式控制器平台下 QT/Python 等上位机程序的运行。
VS 中的 c++项目开发过程如下：

步骤	操作	显示界面




1	

打开 VS，点击菜单“文件”→“ 新建”→ “ 项目”，启动创建项目向导。	








2	





选择开发 语言为 “Visual C++”和程序类型“MFC 应用程序”。	











3	






下一步，选择类型为 “基于对话框”，下一步或者完成。	





4	

找到厂家提供的光盘资料里面的 C++函数库，路径如下(64 位库为例)。	

5	将上述路径下面的所有 DLL 相关库文件复制到新建的项目里面。












6	








在项目中添加静态库和相关头文件。静态库	：
zauxdll.lib,zmotio n.lib 相关头文件： zauxdll2.h,zmotion
.h	

1)先右击头文件，接着依次选择: “添加”→
“现有项”
o	

		


2)在弹出的窗口中依次添加静态库和相关头文件。	










7	





声明相关的头文件和定义控制器连接句 柄，至此项目新建完成。	



第六章	运行与维护	

设备正确的运行及维护不但可以保证和延长设备本身的生命周期，为防止设备性能劣化或降低设备失效的概率，按事先规定的计划或相应技术条件的规定进行的技术管理措施。


工作环境等对设备有影响，所以，通常以 6 个月~1 年的检查周期为标准对其做定期检查，可以根据周围环境适当调整设备的检查周期，使其工作在规定的标准环境中。
检查项目	检查内容	检查标准
电源	测量电压是否为额定值	DC 24V（-5%~+5%）






周围环境	环境温度是否在规定范围内（柜内安装时，柜
内温度即环境温度）	-10°C~55°C
	环境湿度是否在规定范围内（柜内安装时，柜
内湿度即环境湿度）	10%-95% 非凝结
	是否有阳光直射	应无
	有无水、油、化学品等的飞沫	应无
	有无粉尘、盐分、铁屑、污垢	应无
	有无腐蚀性气体	应无
	有无易燃、易爆性气体或物品	应无
	设备是否受到振动或冲击	应在耐振动、耐冲击的范围内
	散热性是否良好	应保持良好通风及散热


安装和接线状态	基本单元和扩展单元是否安装牢固	安装螺丝应上紧、无松动
	基本单元和扩展单元的联接电缆是否完全插好	联接电缆不能松动
	外部接线的螺丝是否松动	螺丝应上紧、无松动
	外部接线是否损坏	外部接线不能有任何外观异常




常见问题	解决建议






电机不转动	1.轴类型 ATYPE 配置是否正确；
2.确认是否有硬件限位、软件限位、报警信号起作用，轴状态是否正常；
3.电机是否使能成功；
4.确认脉冲当量 UNITS、速度的值是否合适，如果有编码器反馈查看 MPOS 是否变换；
5.确认脉冲模式和驱动器的脉冲模式是否匹配；
6.控制器端或驱动器端是否产生报警；
7.检查接线是否正确；
8.确认控制器是否正常发送脉冲。


限位信号不起作用	1.限位传感器工作是否正常，“输入口”工具是否可以监控到限位传感器的信号变化；
2.限位开关的映射是否正确；
3.限位传感器和控制器的公共端是否相连。

输入口检测不到信号	1.检查是否需要 IO 电源；
2.检查信号电平是否与输入口匹配，排查公共端是否相连；
3.检查输出口编号是否与操作的一致。
输出口操作无响应	1.检查是否需要 IO 电源；
2.检查输出口编号是否与操作的一致。

POWER 灯亮，RUN 灯不亮	1.检查供电电源功率是否充足，此时最好给控制器单独供电，调整好后重启控制器；
2.ALM 灯是否有规律的闪烁（硬件问题）。
RUN 灯亮，ALM 灯也亮	1.程序运行错误，请查验 RTSys 错误代码，检查应用程序。


控制器与 PC 串口连接失败	1.串口参数是否被运行程序修改，可以通过?*SETCOM 查看当前的所有串口配置；
2.查看 PC 的串口参数与控制器是否匹配；
3.打开设备管理器，查看 PC 的串口驱动是否正常。



CAN 扩展模块连接不上	1.检查 CAN 接线和供电回路，120 欧姆电阻是否有安装在两端；
2.检查主从端配置，通讯速度配置等；
3.检查拨码开关，是否有多个扩展模块采用同样的 ID；
4.干扰严重的场合使用双绞线、屏蔽层接地，使用双电源供电（扩展模块主电源和 IO 电源分开供电）。

控制器与 PC 网口连接失败	1.检查 PC 的 IP 地址，需要与控制器 IP 在同一网段；
2.检查控制器 IP 地址，可以用串口连接后查看、获取；
3.网口灯不亮时检查接线是否正常；


	4.控制器的电源灯 POWER 和运行指示灯 RUN 是否正常亮起；
5.网线是否有问题，更换质量好的网线再尝试连接；
6.检查控制器 IP 是否和其他设备冲突；
7.检查控制器的网口通道 ETH 是否全部被其他设备占用，将其他设备断开之后在尝试连接；
8.多网卡的情况下建议禁用其他网卡，或者更换电脑再连接；
9.检查 PC 防火墙设置；
10.Ping 一下控制器 IP，看是否能 Ping 通控制器，若无法 Ping 通，检查物理接口，或者网线；
11.arp -a 查询 IP 地址和 MAC 地址。


第七章	售后服务	


本售后服务条款规定的服务内容适用于在中国市场上通过正运动技术及其授权的合法渠道购买的运动控制器、运动控制卡、扩展模块、人机界面等。


1.保修期：12 个月。

在保修期内，如果产品发生非人为故障，我们为您提供保修服务。请客户联系商务人员并填写《 维修申请表》（主要信息如：产品型号、序列号、故障描述、特殊要求等），寄到我们公司,我们将在维修周期内完成维修并寄还给您。
保修期计算方法，一般按条码管理扫描出库时间作为发货时间（如果客户能提供确切的发货时间证明，也可以按照该时间作为发货时间）。
2.换货：

自产品发货之日起 3 个月内，如果产品发生非人为故障，我们可以为您更换同型号产品。

3.终身维护：

我们将为客户提供终身维护服务。在保修期内但不符合保修条件或超过保修期限的故障产品，我 们提供有偿维修服务，在客户确认接受产品的维修费用后，我们安排进行产品的维修。但对已经停产的产品，或缺乏维修物料，或损坏过于严重无维修价值的返回品则无法提供维修服务。
4.维修费用：

1)保修期内的产品，非人为原因引起的故障，免费维修；

2)超保修期或人为损坏产品收费标准，我们将根据不同型号和损坏程度收取元件的成本费、人工费和运费；具体的费用，由对接的商务人员报价给您；
3)运费：保修范围内产品运费由我司负担单程，非保修范围内的产品运费由客户负担；

5.不享受免费保修的情况：

1)由于火灾、水灾、地震等不可抗力因素造成的产品故障；

2)由于客户安装或者使用不当所导致的损坏；

3)未经正运动技术授权的人员对产品进行了拆卸、维修或者改装造成的产品故障；

4)非正运动技术直销或授权的合法渠道购买的产品；

5)产品的编码撕毁、涂改或者其他原因造成的产品编码无法辨认；

VPLC532M 视觉运动控制一体机用户手册 V1.7.1























































43