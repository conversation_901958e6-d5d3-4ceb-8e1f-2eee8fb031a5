===============================================================================
                        螺丝机控制系统V2 使用说明
===============================================================================

【系统概述】
本系统是基于正运动控制器开发的四轴螺丝机控制程序V2版本，修复了所有编译错误，
支持步进电机开环脉冲控制和电批Modbus通讯，具有圆弧插补运动功能。

【主要特性】
✓ 四轴运动控制（X轴、左Y轴、右Y轴、Z轴）
✓ 电批RS485 Modbus RTU通讯控制
✓ 圆弧插补运动轨迹，避免碰撞
✓ 自动回零功能，按Z-Y-X顺序安全回零
✓ 螺丝位置参数化设置
✓ 左右两侧独立作业控制
✓ 完整的安全保护功能

【文件说明】
1. 螺丝机程序V2.bas     - 主控制程序（推荐使用）⭐
2. 螺丝机测试V2.bas     - 系统测试程序
3. 使用说明V2.txt       - 本说明文件

【硬件配置】
轴配置：
- 轴0 (X轴)：横向移动轴
- 轴1 (Y1轴)：左侧纵向移动轴
- 轴2 (Y2轴)：右侧纵向移动轴  
- 轴3 (Z轴)：垂直升降轴

输入信号：
- IN0：左侧开始按键
- IN1：右侧开始按键
- IN2：系统回零按键
- IN3：急停按键
- IN8-IN11：正向限位开关
- IN12-IN15：负向限位开关
- IN16-IN19：原点开关

输出信号：
- OP0：吸螺丝控制（低电平有效）

通讯接口：
- RS485：电批Modbus RTU通讯（默认地址10，115200波特率）

【快速启动】
1. 系统启动
   RUN "螺丝机程序V2.bas"

2. 系统回零
   按下IN2回零按键，或手动调用：
   CALL HomeWork()

3. 开始作业
   按下IN0开始左侧打螺丝
   按下IN1开始右侧打螺丝

【参数设置】
设置螺丝位置：
CALL SetScrewPos(side, screw_num, pos_x, pos_y, pos_z)
参数说明：
- side: 1-左侧, 2-右侧
- screw_num: 螺丝编号(1开始)
- pos_x, pos_y, pos_z: 位置坐标

示例：
CALL SetScrewPos(1, 1, 100, 80, 0)    ' 设置左侧第1个螺丝位置

显示系统状态：
CALL ShowStatus()

【默认配置】
螺丝位置（可修改）：
- 左侧：2x2阵列，起始位置(100,80)，间距50x40mm
- 右侧：2x2阵列，起始位置(100,220)，间距50x40mm
- 吸螺丝位置：(50,150,5)

运动参数：
- 速度：X/Y轴100mm/s，Z轴50mm/s
- 加速度：X/Y轴1000mm/s²，Z轴500mm/s²
- 脉冲当量：1000脉冲/mm

电批参数：
- 通讯：RS485 Modbus RTU，115200波特率
- 设备地址：10

【作业流程】
每个螺丝的打螺丝流程：
1. 移动到吸螺丝位置
2. 吸取螺丝（OP0输出）
3. 圆弧插补运动到螺丝孔位
4. 执行电批锁紧
5. 圆弧插补运动回到吸螺丝位置
6. 关闭吸螺丝

【安全功能】
✓ 急停保护：硬件急停（IN3）+ 软件急停
✓ 限位保护：各轴配置正负限位开关
✓ 回零保护：系统启动后必须先回零
✓ 状态监控：实时轴状态和电批状态
✓ 错误处理：完整的故障诊断

【测试功能】
运行测试程序：
RUN "螺丝机测试V2.bas"

测试项目包括：
- 硬件连接测试
- 轴参数测试
- 输入信号测试
- 输出信号测试
- Modbus通讯测试
- 运动功能测试

【故障排除】
1. 系统无法启动
   - 检查控制器连接
   - 检查程序下载是否成功

2. 回零失败
   - 检查原点开关连接
   - 检查限位开关状态
   - 检查INVERT_IN设置

3. 电批通讯失败
   - 检查RS485连接
   - 检查电批地址设置（默认10）
   - 检查波特率配置（115200）

4. 运动异常
   - 检查轴参数设置
   - 检查UNITS脉冲当量
   - 检查限位开关

【状态查询】
查看系统状态：
CALL ShowStatus()

查看轴状态：
PRINT "轴0状态：", HEX(AXISSTATUS(0))

查看当前位置：
PRINT "轴0位置：", DPOS(0)

【HMI接口】
系统预留了Modbus寄存器接口：
- MODBUS_REG(0)：系统状态
- MODBUS_REG(1)：左侧螺丝数量
- MODBUS_REG(2)：右侧螺丝数量
- MODBUS_REG(3)：当前螺丝编号
- MODBUS_REG(10-13)：各轴回零状态
- MODBUS_IEEE(20-23)：各轴当前位置

【注意事项】
1. 必须先运行主程序，再运行测试程序
2. 系统启动后必须先回零才能执行作业
3. 急停后需要重新回零
4. 电批通讯地址默认为10，可根据实际调整
5. 圆弧插补需要至少两轴参与
6. 建议先使用测试程序验证各项功能

【技术支持】
如有问题请联系正运动技术支持：
- 官网：www.zmotion.com.cn
- 技术论坛：bbs.zmotion.com.cn

【版本历史】
- v2.0 (2025-07-30)：修复版本
  - 修复所有编译错误
  - 使用全新函数名避免冲突
  - 简化程序结构
  - 完善错误处理

===============================================================================
