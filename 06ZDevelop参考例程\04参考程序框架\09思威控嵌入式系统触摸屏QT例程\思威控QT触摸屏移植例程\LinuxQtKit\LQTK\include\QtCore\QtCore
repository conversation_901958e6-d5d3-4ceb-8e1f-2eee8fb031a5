#ifndef QT_QTCORE_MODULE_H
#define QT_QTCORE_MODULE_H
#include <QtCore/QtCoreDepends>
#include "qabstractanimation.h"
#include "qanimationgroup.h"
#include "qparallelanimationgroup.h"
#include "qpauseanimation.h"
#include "qpropertyanimation.h"
#include "qsequentialanimationgroup.h"
#include "qvariantanimation.h"
#include "qtextcodec.h"
#include "qcompilerdetection.h"
#include "qendian.h"
#include "qflags.h"
#include "qglobal.h"
#include "qglobalstatic.h"
#include "qisenum.h"
#include "qlibraryinfo.h"
#include "qnamespace.h"
#include "qnumeric.h"
#include "qprocessordetection.h"
#include "qsysinfo.h"
#include "qsystemdetection.h"
#include "qtypeinfo.h"
#include "qtypetraits.h"
#include "qbuffer.h"
#include "qdatastream.h"
#include "qdebug.h"
#include "qdir.h"
#include "qdiriterator.h"
#include "qfile.h"
#include "qfiledevice.h"
#include "qfileinfo.h"
#include "qfileselector.h"
#include "qfilesystemwatcher.h"
#include "qiodevice.h"
#include "qlockfile.h"
#include "qloggingcategory.h"
#include "qprocess.h"
#include "qresource.h"
#include "qsavefile.h"
#include "qsettings.h"
#include "qstandardpaths.h"
#include "qstorageinfo.h"
#include "qtemporarydir.h"
#include "qtemporaryfile.h"
#include "qtextstream.h"
#include "qurl.h"
#include "qurlquery.h"
#include "qabstractitemmodel.h"
#include "qabstractproxymodel.h"
#include "qidentityproxymodel.h"
#include "qitemselectionmodel.h"
#include "qsortfilterproxymodel.h"
#include "qstringlistmodel.h"
#include "qjsonarray.h"
#include "qjsondocument.h"
#include "qjsonobject.h"
#include "qjsonvalue.h"
#include "qabstracteventdispatcher.h"
#include "qabstractnativeeventfilter.h"
#include "qbasictimer.h"
#include "qcoreapplication.h"
#include "qcoreevent.h"
#include "qeventloop.h"
#include "qmath.h"
#include "qmetaobject.h"
#include "qmetatype.h"
#include "qmimedata.h"
#include "qobject.h"
#include "qobjectcleanuphandler.h"
#include "qobjectdefs.h"
#include "qpointer.h"
#include "qsharedmemory.h"
#include "qsignalmapper.h"
#include "qsocketnotifier.h"
#include "qsystemsemaphore.h"
#include "qtimer.h"
#include "qtranslator.h"
#include "qvariant.h"
#include "qwineventnotifier.h"
#include "qmimedatabase.h"
#include "qmimetype.h"
#include "qfactoryinterface.h"
#include "qlibrary.h"
#include "qplugin.h"
#include "qpluginloader.h"
#include "quuid.h"
#include "qabstractstate.h"
#include "qabstracttransition.h"
#include "qeventtransition.h"
#include "qfinalstate.h"
#include "qhistorystate.h"
#include "qsignaltransition.h"
#include "qstate.h"
#include "qstatemachine.h"
#include "qatomic.h"
#include "qexception.h"
#include "qfuture.h"
#include "qfutureinterface.h"
#include "qfuturesynchronizer.h"
#include "qfuturewatcher.h"
#include "qgenericatomic.h"
#include "qmutex.h"
#include "qreadwritelock.h"
#include "qresultstore.h"
#include "qrunnable.h"
#include "qsemaphore.h"
#include "qthread.h"
#include "qthreadpool.h"
#include "qthreadstorage.h"
#include "qwaitcondition.h"
#include "qalgorithms.h"
#include "qarraydata.h"
#include "qarraydataops.h"
#include "qarraydatapointer.h"
#include "qbitarray.h"
#include "qbytearray.h"
#include "qbytearraylist.h"
#include "qbytearraymatcher.h"
#include "qcache.h"
#include "qchar.h"
#include "qcollator.h"
#include "qcommandlineoption.h"
#include "qcommandlineparser.h"
#include "qcontainerfwd.h"
#include "qcontiguouscache.h"
#include "qcryptographichash.h"
#include "qdatetime.h"
#include "qeasingcurve.h"
#include "qelapsedtimer.h"
#include "qhash.h"
#include "qiterator.h"
#include "qline.h"
#include "qlinkedlist.h"
#include "qlist.h"
#include "qlocale.h"
#include "qmap.h"
#include "qmargins.h"
#include "qmessageauthenticationcode.h"
#include "qpair.h"
#include "qpoint.h"
#include "qqueue.h"
#include "qrect.h"
#include "qrefcount.h"
#include "qregexp.h"
#include "qregularexpression.h"
#include "qscopedpointer.h"
#include "qscopedvaluerollback.h"
#include "qset.h"
#include "qshareddata.h"
#include "qsharedpointer.h"
#include "qsize.h"
#include "qstack.h"
#include "qstring.h"
#include "qstringbuilder.h"
#include "qstringlist.h"
#include "qstringmatcher.h"
#include "qtextboundaryfinder.h"
#include "qtimeline.h"
#include "qtimezone.h"
#include "qvarlengtharray.h"
#include "qvector.h"
#include "qxmlstream.h"
#include "qtcoreversion.h"
#endif
