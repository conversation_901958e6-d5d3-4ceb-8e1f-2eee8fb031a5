运动控制器提供丰富的接口，具有优良的运动控制性能，可以满足各种项目的扩展需求。本手册介绍了产品的安装、接线、接口定义和操作说明等相关内容。
本手册版权归深圳市正运动技术有限公司所有，在未经本公司书面授权的情况下，任何人不得翻印、翻译和抄袭本手册中的任何内容。前述行为均将构成对本公司手册版权之侵犯，本司将依法追究其法律责任。
涉及 ZMC 控制器软件的详细资料以及每个指令的介绍和例程，请参阅 BASIC 软件手册。
本手册中的信息资料仅供参考。由于改进设计和功能等原因，正运动公司保留对本资料的最终解释权！内容如有更改，恕不另行通知！
调试机器要注意安全！
请务必在机器中设计有效的安全保护装置，并在软件中加入出错处理程序，否则所造成的损失，本公司没有义务或责任对此负责。
为了保证产品安全、正常、有效的使用，请您务必在安装、使用产品前仔细阅读本产品手册。




产品型号：ZMC306X 运动控制器
文件名	版本号	版本（更改）说明	更新日期	更改人





用户手册	




V1.5	1.增加型号信息，工作环境
2.增加各个端子和接口的规格接线和基本使用方法
3.增加网口说明
4.增加扩展模块资源映射
5.增加编程软件使用方法
6.增加版权声明、安全注意事项、运行与维护及售后服务说明内容	




2023/5/11	




xcx

用户手册	
V1.6.0	1.增加铭牌信息
2.更新手册接线图和内容	
2024/11/27	
hj
				
				




本章对正确使用本产品所需关注的安全注意事项进行说明。在使用本产品之前，请先阅读使用说明并正确理解安全注意事项的相关信息。
本产品应在符合设计规格要求的环境下使用，否则可能导致设备损坏，或者人员受伤，因未遵守相关规定引发的功能异常或部件损坏等不在产品质量保证范围之内。
因未遵守本手册的内容、违规操作产品引发的人身安全事故、财产损失等，我司将不承担任何法律责任。

按等级可分为“危险”、“注意”。如果没有按要求操作，可能会导致中度伤害、轻伤及设备损伤的情况。
请妥善保管本指南以备需要时阅读，并请务必将本手册交给最终用户。
安装


危险	控制器拆卸时，系统使用的外部供应电源全部断开后再进行操作，否则可能造成设备误操作或损坏设备；
禁止在以下场合使用：有灰尘、油烟、导电性尘埃、腐蚀性气体、可燃性气体的场所；暴露于高温、结露、风雨的场合；有振动、冲击的场合；电击、火灾、误操作也会导
致产品损坏和恶化。


注意	安装时避免金属屑和电线头掉入硬件电路板内；
安装后保证其硬件电路板上没有异物；
安装时，应使其与安装架紧密牢固；
如果控制器安装不当，可能导致误操作、故障及火灾。
配线


危险	
设备外部配线的规格和安装方式应符合当地配电法规要求；
在配线作业时，应将系统使用的外部供应电源全部断开后再进行操作；
配线作业结束后进行通电、运行时，必须安装产品附带的端子；
线缆端子应做好绝缘，确保线缆安装到端子台后，线缆之间的绝缘距离不会减少。


注意	安装时避免金属屑和电线头掉入硬件电路板内；
电缆连接应在对所连接的接口的类型进行确认的基础上正确地进行；
应确认压入端子的线缆接触良好；
请勿把控制线及通信电缆与主电路或动力电源线等捆扎在一起，走线应相距 100 ㎜以上，否则噪声可能导致误动作。
如果控制器安装不当，可能会导致触电或设备故障、误动作；


目录

第一章	产品信息	1
1.1产品简介	1
1.2功能特点	1
1.3系统框图	2
1.4硬件安装	2
第二章	产品规格	4
2.1基本规格	4
2.2铭牌及型号	4
2.3接口定义	5
2.4工作环境	6
第三章	接线、通讯设定及组网	7
3.1电源输入	7
3.1.1电源规格	7
3.2RS485、CAN、RS422 通讯接口	7
3.2.1RS485、CAN、RS422 通讯规格和接线	8
3.2.2基本使用方法	9
3.3RS232 串口	10
3.3.1RS232 通讯接口规格及接线	10
3.3.2基本使用方法	11
3.4IN 数字量输入、高速锁存端口	12
3.4.1数字输入规格及接线	13
3.4.2基本使用方法	14
3.5OUT 数字量输出、PWM 端口	14
3.5.1数字输出规格及接线	15
3.5.2基本使用方法	16
3.6AD/DA 模拟量输出	16
3.6.1模拟量输入/输出规格及接线	17
3.6.2基本使用方法	17
3.7U 盘接口	18
3.8ETHERNET 网口	18
3.9AXIS 差分脉冲轴接口	20
3.9.1AXIS 接口信号规格及接线	21
3.9.2基本使用方法	23
第四章	扩展模块	25
4.1CAN 总线扩展	25
4.2CAN 总线扩展接线	25
4.3CAN 总线扩展资源映射	26
第五章	编程与应用	29
5.1ZDevelop 软件使用	29
5.2PC 上位机编程应用	33
第六章	运行与维护	35
6.1定期检查与维护	35
6.2常见问题	35
第七章	售后服务	37

第一章	产品信息	


ZMC 是正运动技术推出的网络运动控制器型号简称。
ZMC304X/ZMC306X 缺省支持正运动 XPLC 的功能，可以通过网络来做组态显示。
ZMC306X 高性能多轴运动控制器是一款脉冲型的独立式运动控制器，控制器最多支持 12 轴的复杂连续轨迹控制需求。
ZMC3 系列高性能多轴运动控制器可用于机器人(SCARA、Delta、6 关节)、电子半导体设备(检测类设备、组装类设备、锁附类设备、焊锡机)、点胶设备、非标设备、印刷包装设备、纺织服装设备、舞台娱乐设备、医疗设备、流水线等应用场合。


最多 12 轴运动控制。
脉冲输出模式：脉冲/方向或双脉冲。
每轴最大输出脉冲频率 10MHz。
支持 CAN 扩展 IO，最多可扩展到 512 个隔离输入口和 512 个隔离输出口。
轴正负限位信号口/原点信号口可以随意配置为任何输入口。
通用数字输出口最大输出电流可达 300mA，可直接驱动部分电磁阀。
支持 RS232 接口、RS485 接口、U 盘接口、以太网接口。
支持最多 12 轴直线插补、任意圆弧插补、螺旋插补、样条插补。
支持电子凸轮、电子齿轮、位置锁存、同步跟随、虚拟轴等功能。
支持脉冲闭环，螺距补偿等功能。
支持 Basic 多文件多任务编程。
多种程序加密手段，保护客户的知识产权。
掉电检测，掉电存储。




系统框图如下图所示：



ZMC306X 运动控制器采用螺钉固定的水平安装方式，每个控制器应安装 4 个螺钉进行紧固。
(单位：mm、安装孔直径：4.5mm)



ZMC304X 运动控制器基本轴数为 4 轴，安装方式和 ZMC306X 相同。











安装注意：	只有受过电气设备相关培训、具有电气知识的专业人员才能操作，严禁非专业人员操作！
安装前请务必仔细阅读产品使用说明书和安全注意事项！
安装前，请确保产品处于断电状态；
请勿拆解模块，否则可能损坏机器；
避免阳光直射安装；
为了利于通风以及控制器的更换，控制器上下部分与安装环境及周边部件之间应留出 2-3cm；
考虑到对控制器的方便操作及维护，请勿将控制器安装在以下场所：
a)周边环境温度超出-10℃-55℃范围的场所
b)周边环境湿度超出 10%-95% （非凝结）范围的场所
c)有腐蚀性气体、可燃性气体的场所
d)灰尘、铁粉等导电性的粉末、油雾、盐分、有机溶剂较多的场所


第二章	产品规格	


项目	描述
型号	ZMC306X	ZMC304X
基本轴数	6	4
最多扩展轴数	12	12
基本轴类型	脉冲轴
通用 IO 数	24 路输入，12 路输出
轴接口 IO 数	6 路输入，6 路输出
最多扩展 IO 数	512 路输入，512 路输出
PWM 数	2
AD/DA	通用 AD 2 路、DA 2 路，0-10V
最多扩展 AD/DA	128 路 AD，64 路 DA
脉冲位数	32
编码器位数	32
速度加速度位数	32
脉冲最高频率	10MHz
每轴运动缓冲数	128
数组空间	300000
程序空间	2000kByte
Flash 空间	128MByte
电源输入	24V 直流输入
通讯接口	RS232，RS485，以太网，U 盘，CAN
外形尺寸	205mm*134mm


铭牌信息：



型号	规格描述
ZMC304X	4 轴，点位，电子凸轮，直线，圆弧，连续轨迹运动，机械手指令。
ZMC304X-1	4 轴，点位，电子凸轮。
ZMC304X-2	4 轴，点位，电子凸轮，直线。
ZMC304XR	ZMC304X 加上蜘蛛手机械手指令支持。
ZMC306X	6 轴，点位，电子凸轮，直线，圆弧，连续轨迹运动，机械手指令。
ZMC306XR	ZMC306X 加上蜘蛛手和 6 关节机械手指令支持。

ZMC304X 有 HW 版本（ZMC304X-HW），支持硬件比较输出功能，见 HW_PSWITCH2 指令。型号多样，详情请咨询。





接口说明如下表：
标识	接口	个数	说明
POW	

状态指示灯	1 个	电源指示灯：电源接通时亮灯
RUN		1 个	运行指示灯：正常运行时亮灯
ALM		1 个	错误指示灯：运行错误时亮灯
RS232	RS232 串口（port0）	1 个	采用 MODBUS_RTU 协议
RS485	RS485 串口（port1）	1 个	采用 MODBUS_RTU 协议
RS422	RS422 串口（port2）	1 个	采用 MODBUS_RTU 协议
ETHERNET	网口	1 个	采用 MODBUS_TCP 协议，通过交换机扩展网口个数，?*port 查询网口通道数，默认 IP 地址 ************
UDISK	U 盘接口	1 个	插入 U 盘设备
E+24V	主电源	1 个	24V 直流电源给控制器供电
CAN	CAN 总线接口	1 个	连接 CAN 扩展模块和其他标准 CAN 设备
IN	数字 IO 输入口	24 个	NPN 型，内部 24V 供电，2 路高速输入，IN0-1 具有锁存功能
OUT	数字 IO 输出口	12 个	NPN 型，内部 24V 供电，2 路高速输出，OUT0-1 具有 PWM
功能
AD	模拟量输入口	2 个	分辨率 12 位，0-10V
DA	模拟量输出口	2 个	分辨率 12 位，0-10V
AXIS	脉冲轴接口	6 个	包含差分脉冲输出和差分编码器输入


项目	参数
工作温度	-10℃-55℃
工作相对湿度	10%-95%非凝结
储存温度	-40℃～80℃(不冻结)
储存湿度	90%RH 以下(不结露)


振动	频率	5-150Hz
	位移	3.5mm(直接安装) (<9Hz)
	加速度	1g(直接安装) (>9Hz)
	方向	3 轴向
冲击(碰撞)	15g，11ms，半正弦波，3 轴向
防护等级	IP20


第三章	接线、通讯设定及组网	


电源输入采用 3Pin 间距为 3.81mm 的螺钉式可插拔接线端子，该端子为控制器电源。


端子	名称	类型	功能

	E+24V	输入	直流输入正端
	EGND	输入	直流输入负端
	FG	接地	机壳保护地



项目	说明
输入电压	DC24V(-5%~5%)
启动电流	≤0.5A
工作电流	≤0.4A
防反接	有
过流保护	有


通讯接口采用 10Pin 间距为 3.81mm 的螺钉式可插拔接线端子，RS485、RS422 通讯和 CAN 通讯均可通过该端子对应接口连接使用，支持 MODBUS_RTU 协议和自定义通讯。


端子	名称	功能

	485B	485-
	485A	485+
	EGND	通讯公共端
	CANL	CAN 差分数据-
	CANH	CAN 差分数据+
	EGND	通讯公共端

	422TX-	RS422 发送-
	422TX+	RS422 发送+
	422RX-	RS422 接收-
	422RX+	RS422 接收+




RS485 串口支持 MODBUS_RTU 协议和自定义通讯，主要包含 485A、485B 和公共端。
控制器的 CAN 接口采用标准 CAN 通讯协议，主要包含三个端口，CANL、CANH 和公共端。支持连接 CAN
扩展模块和其他标准 CAN 设备。
RS422 串口支持 MODBUS_RTU 协议和自定义通讯，主要包含 422TX-、422TX+、422RX-、422RX+和公共端。


项目	RS485（port1）	CAN	RS422（port2）
最大通讯速率(bps)	115200	1M	115200
终端电阻	120Ω	120Ω	无
拓扑结构	菊花链连接结构	菊花链连接结构	点对多连接
可扩展节点数	最大 127 个	最大 16 个	最大 10 个

通讯距离	通讯距离越长通讯速率
越低，建议最大 30m	通讯距离越长通讯速率越
低，建议最大 30m	通讯距离越长通讯速率
越低，建议最大 30m


将 RS485 的 485A 和 485B 对应连接控制器的 485A 和 485B，RS485 通讯双方的公共端连接在一起。

将标准 CAN 模块的 CANL 和 CANH 分别连接对方的 CANL 和 CANH，CAN 总线通讯双方的公共端连接在一起，在 CAN 总线的左右两端各接一个 120 欧的电阻。


将 RS422 的 422TX 和 422RX 对应连接控制器的 422RX 和 422TX，RS422 通讯双方的公共端连接在一起。





如上为菊花链拓扑结构接线，不可采用星型拓扑结构，当使用环境较为理想并且节点较少时也可考虑分支结构；
请在 CAN 总线/RS485 最两端接口各并接一个 120Ω 的终端电阻，匹配电路阻抗，保证通讯稳定性；
请务必连接 CAN 总线/RS485 上各个节点的公共端，以防止 CAN/RS485 芯片烧坏；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地；
现场布线还要注意强电和弱电布线要拉开距离，建议 20cm 以上；
要注意整个线路上的设备接地（机壳）要良好，机壳的接地要接在标准的厂房地桩上。

双绞屏蔽线，屏蔽电缆接地。



1.请按照以上接线说明正确接线；
2.上电后请选用 ETHERNET、RS232、RS485 三种任一种接口连接 ZDevelop；
3.请使用“ADDRESS”和“SETCOM”指令设置和查看协议站号和配置参数，详细说明见“Basic 编程手册”；
4.请使用“CANIO_ADDRESS”指令根据需要设置主端“地址”和“速率”，“CANIO_ENABLE”指令设置使能或禁止内部 CAN 主端功能，也可以通过“ZDevelop/控制器/控制器状态/通讯配置”界面直观查看 CAN 状态，详细说明见“Basic 编程手册”；




5.根据各自说明正确设置第三方设备相关参数使各个节点参数匹配；
6.根据从站手册说明正确设置从站扩展模块的“地址”和“速率”；
7.全部设置完成后重启所有站点电源即可建立通讯；
8.注意 CAN 总线上每个节点的“速率”设置必须一致，“地址”设置不能够产生冲突，否则“ALM”告警灯会亮起，通讯建立失败或者通讯错乱。


RS232 在一个标准 DB9 公座中，支持 MODBUS_RTU 协议和自定义通讯。


端子	引脚号	名称	类型	功能

	1、4、6、7、8	NC	悬空	预留
	2	RXD	输入	RS232 信号接收
	3	TXD	输出	RS232 信号发送
	5	EGND	输出	E5V 电源输出负极和该通讯公共端
	9	E5V	输出	E5V 电源输出正极，最大 300mA



项目	RS232（port0）
最大通讯速率（bps）	115200
终端电阻	无
拓扑结构	1 对 1 连接
可扩展节点数	1
通讯距离	通讯距离越长通讯速率越低，建议最大 5m






RS232 的接线如上，收发信号需交叉接线，与电脑连接时建议采用双母头的交叉线；
请务必连接各个通讯节点的公共端，以防止通讯芯片烧坏；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地。

双绞屏蔽线，屏蔽电缆接地。



1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232（默认参数可直接连接）、RS485（默认参数可直接连接，硬件需使用转接头）三种任一种接口连接 ZDevelop；
3.请使用“ADDRESS”和“SETCOM”指令设置和查看协议站号和配置参数，详细说明见“Basic 编程手册”；
4.根据各自说明正确设置第三方设备相关参数使各个节点参数匹配；

5.全部设置完成后即可开始通讯；

6.可通过“ZDevelop/控制器/控制器状态/通讯配置”界面直接查看 RS232/RS485 的通讯数据。





数字量输入采用 3 组 10Pin 间距为 3.81mm 的螺钉式可插拔接线端子，数字输入信号中集成有高速锁存功能。


端子	名称	类型	功能 1	功能 2

	EGND	/	IO 公共端	/
	EGND	/	IO 公共端	/
	IN0	
NPN 型，高速输入	开关输入 0	高速锁存
	IN1		开关输入 1	高速锁存
	IN2	



NPN 型，低速输入	开关输入 2	/
	IN3		开关输入 3	/
	IN4		开关输入 4	/
	IN5		开关输入 5	/
	IN6		开关输入 6	/
	IN7		开关输入 7	/

	EGND	/	IO 公共端	/
	EGND	/	IO 公共端	/
	IN8	




NPN 型，低速输入	开关输入 8	/
	IN9		开关输入 9	/
	IN10		开关输入 10	/
	IN11		开关输入 11	/
	IN12		开关输入 12	/
	IN13		开关输入 13	/
	IN14		开关输入 14	/
	IN15		开关输入 15	/



	EGND	/	IO 公共端	/
	EGND	/	IO 公共端	/
	IN16	




NPN 型，低速输入	开关输入 16	/
	IN17		开关输入 17	/
	IN18		开关输入 18	/
	IN19		开关输入 19	/
	IN20		开关输入 20	/
	IN21		开关输入 21	/
	IN22		开关输入 22	/
	IN23		开关输入 23	/



项目	高速输入（IN0-1）	低速输入（IN2-23）
输入方式	NPN 型，低电平输入触发	NPN 型，低电平输入触发
输入频率	＜100kHz	＜5kHz
输入阻抗	3.3KΩ	4.7KΩ
输入电压等级	DC24V	DC24V
输入开启电压	<15V	<14.5V
输入关闭电压	>15.1V	>14.7V
最小输入电流	-2.3mA	-1.8mA
最大输入电流	-7.5mA	-6mA
隔离方式	光电隔离	光电隔离
注意：以上参数是当控制器电源电压（E+24V 端口）为 24V 时的典型值。





高速数字输入 IN（0-1）和低速数字输入 IN（2-23）接线原理如上图，外部信号源可以是光耦也可以是按键开关或传感器等，只要输出电平满足要求均可接入；
公共端请选择 IO 端子上的“EGND”端口与外部输入设备的“COM”端连接，如果外部设备该信号区域电源与控制器电源在同一个供电系统中，也可以省略该连接。


1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232、RS485 三种任一种接口连接 ZDevelop；

3.可通过“IN”指令直接读取相应输入口的状态值，也可以通过“ZDevelop/视图/输入口”界面直观查看输入口状态，详细说明见“Basic 编程手册”；


4.锁存功能可通过“REGIST”指令进行设定启用，软件里面使用 REG_INPUTS 配置。详细说明见
“Basic 编程手册”。


数字量输出采用 2 组间距为 3.81mm 的螺钉式可插拔接线端子，数字输出信号中集成有 PWM 功能。


端子	名称	类型	功能 1	功能 2

	EGND	/	E5V 电源地/IO 公共端	/
	E5V	/	E5V 电源输出，最大 300mA	/
	OUT0	NPN 型
高速输出	开关输出 0	PWM 输出 0
	OUT1		开关输出 1	PWM 输出 1
	OUT2	


NPN 型
低速输出	开关输出 2	/
	OUT3		开关输出 3	/
	OUT4		开关输出 4	/
	OUT5		开关输出 5	/
	OUT6		开关输出 6	/
	OUT7		开关输出 7	/



	EGND	/	IO 公共端	/
	OUT8	
NPN 型
低速输出	开关输出 8	/
	OUT9		开关输出 9	/
	OUT10		开关输出 10	/
	OUT11		开关输出 11	/
注意：
1.E5V 电源输出口用于 PWM 或者接线使用，功率较小不建议用于其他用途；
2.OUT0-1 具有 PWM 的功能，当 PWM 关闭时为通用输出。



项目	高速输出（OUT0-1）	低速输出（OUT2-11）
输出方式	NPN 型，输出时为 0V	NPN 型，输出时为 0V
输出频率	＜400kHz	＜8kHz
输出电压等级	DC24V	DC24V
最大输出电流	+300mA	+300mA
关闭时最大漏电流	25μA	25μA
导通响应时间	1μs(阻性负载典型值)	12μs
关闭响应时间	3μs	80μs
过流保护	支持	支持
隔离方式	光电隔离	光电隔离
注意：
1.表中的时间都是基于阻性负载的典型值，负载电路有变化时可能会有变化；
2.由于漏型输出，输出的关闭会比较明显受外部负载电路的影响，应用中输出频率不宜设置太高。






高速数字输出 OUT（0-1）和低速数字输出 OUT（2-11）接线原理如上图，外部信号接收端可以是光耦也可以是继电器或电磁阀等，只要输入电流不超过 300mA 均可接入；
公共端的连接请选择 IO 端子上的“EGND”端口与外部输入设备直流电源的负极连接，如果外部设备的直流电源与控制器电源在同一个供电系统中，也可以省略该连接；
E5V 端口为 5V 电源输出端口，当面对一些需要提供外部 5V 电源输入的负载时可以采用该电源端，最大电流 300mA。


1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232、RS485 三种任一种接口连接 ZDevelop；

3.可通过“OP”指令直接操作端口开启或关闭，也可以通过“ZDevelop/视图/输出口”界面直接点击进行开启或关闭，详细说明见“Basic 编程手册”；

4.PWM 功能可通过“PWM_FREQ”和“PWM_DUTY”指令分别设定频率和占空比进行使用，详细说明见
“Basic 编程手册”；


模拟量端口采用 1 组 5Pin 间距为 3.81mm 的螺钉式可插拔接线端子。


端子	名称	类型	功能

	DA0	输出	模拟量输出端口 AOUT(0)
	DA1	输出	模拟量输出端口 AOUT(1)
	AGND	公共端	模拟量公共端
	AD0	输入	模拟量输出端口 AIN(0)
	AD1	输入	模拟量输出端口 AIN(1)





项目	AD（0-1）	DA（0-1）
分辨率	12 位	12 位
数据范围	0-4095	0-4095
信号范围	0-10V 输入	0-10V 输出
数据刷新率	1kHz	1kHz
电压输入阻抗/输出负载	>300KΩ(电压输入阻抗)	>10KΩ(电压输出负载)




模拟量输入/输出接线方法如上图，外部负载信号范围需与之匹配；
请使用屏蔽线接线，尤其是环境恶劣的场合，务必使屏蔽层充分接地。

双绞屏蔽线，屏蔽电缆接地。





1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232、RS485 三种任一种接口连接 ZDevelop；


3.可通过“AIN”和“AOUT”指令读取模拟量输入电压和使模拟量输出相应电压，也可以通过
“ZDevelop/视图/AD/DA”界面直接查看各通道数据，详细说明见“Basic 编程手册”。



ZMC306X 运动控制器上提供一个 USB 通讯接口来插入 U 盘设备，用于 ZAR 程序升级、控制器数据导入导出、3 次文件执行。其示意图如下图所示：



项目	USB2.0
最高通讯速率	12Mbps
5V 最大输出电流	500mA
是否隔离	否


ZMC306X 运动控制器具有一个百兆网口，支持MODBUS_TCP 协议和自定义通讯，默认IP 地址************。针脚定义图如下：



控制器以太网口可以通过一根以太网电缆与计算机，HMI 等进行点对点连接，示意图如下：

控制器也可以通过以太网电缆连接到交换机上，通过交换机与其他设备相连，实现多点连接。示意图如下：


ETHERNET 通讯接口采用标准以太网 RJ45 接口。

网线选用超五类屏蔽双绞线，水晶头带有金属壳，以减少干扰，防止信息被窃听。如下图所示：


项目	规格
电缆类型	弹性交叉电缆，超五类
导线类型	双绞线
线对	4
隔离	十字骨架
接头	带铁壳水晶头
线缆材质	PVC 材质
线缆长度	不超过 100 米


采用 RJ45 网线接法：

安装时，握住带线的水晶头，插入 RJ45 接口直至发出“喀哒”声；
为确保通讯的稳定性，请将线缆用扎线带等进行固定；
拆卸时，按住水晶头尾部机构将连接器与模块呈水平方向拔出；请使用管型预绝缘端子和合适线径的线缆来进行用户端子的接线。



该产品提供 6 个本地差分脉冲轴接口，每个接口为标准 DB26 母座。每个端子提供了 0V 和+5V 输出，可以为编码器提供 5V 电源。
轴使用前，要通过 ATYPE 参数来配置轴的使用方式。


接口	引脚号	信号	说明











	1	EGND	数字 IO 电源 24V 负极
	2	IN24-29/ALM	数字输入，建议做驱动报警
	3	OUT12-17ENABLE	数字输出，建议做驱动使能
	4	EA-	编码器差分输入信号 A-
	5	EB-	编码器差分输入信号 B-
	6	EZ-	编码器差分输入信号 Z-
	7	+5V	脉冲/编码器信号 5V 电源正极
	8	备用	悬空
	9	DIR+	伺服或步进方向输出差分信号+
	10	GND	脉冲/编码器信号 5V 电源负极
	11	PUL-	伺服或步进脉冲输出差分信号-
	12	备用	悬空
	13	GND	脉冲/编码器信号 5V 电源负极
	14	OVCC	数字 IO 电源 24V 正极
	15	备用	悬空
	16	备用	悬空
	17	EA+	编码器差分输入信号 A+
	18	EB+	编码器差分输入信号 B+
	19	EZ+	编码器差分输入信号 Z+
	20	GND	脉冲/编码器信号 5V 电源负极
	21	GND	脉冲/编码器信号 5V 电源负极
	22	DIR-	伺服或步进方向输出差分信号-
	23	PUL+	伺服或步进脉冲输出差分信号+
	24	GND	脉冲/编码器信号 5V 电源负极
	25	备用	悬空


	26	备用	悬空
注意：
1.ALM，ENABLE 由于驱动能力较小，建议做轴 IO 使用；
2.OVCC，+5V 仅供控制器和伺服驱动器通讯使用，请勿用作其他地方供电。
脉冲轴引脚号与 IO 的对应关系：
脉冲轴号	对应 IN 口（2 号引脚）	对应 OUT 口（3 号引脚）
AXIS0	IN24	OUT12
AXIS1	IN25	OUT13
AXIS2	IN26	OUT14
AXIS3	IN27	OUT15
AXIS4	IN28	OUT16
AXIS5	IN29	OUT17



信号	项目	说明

PUL/DIR	信号类型	差分输出信号
	信号电压范围	0-5V
	信号最大频率	10MHz

EA/EB/EZ	信号类型	差分输入信号
	信号电压范围	0-5V
	信号最大频率	5MHz





IN24-29	输入方式	NPN 型，低电平输入触发
	输入频率	＜5kHz
	输入阻抗	6.8KΩ
	输入电压等级	DC24V
	输入开启电压	<10.5V
	输入关闭电压	>10.7V
	最小输入电流	-1.8mA
	最大输入电流	-4mA
	隔离方式	光电隔离



OUT12-17	输出方式	NPN 型，输出时为 0V
	输出频率	＜8kHz
	输出电压等级	DC24V
	最大输出电流	+50mA
	过流保护	无
	隔离方式	光电隔离
+5V，GND	5V 电源最大输出电流	50mA
OVCC，EGND	24V 电源最大输出电流	50mA



与松下 A5/A6 伺服驱动器接线参考示例：

单端脉冲轴接线：



单端编码器接线：



差分脉冲轴接口接线原理如上图所示，不同型号驱动器接线方法存在差异，请谨慎连接；
请使用双绞屏蔽线，尤其是环境恶劣的场合，务必使屏蔽层充分接地。


1.请按照以上接线说明正确接线；

2.上电后请选用 ETHERNET、RS232（默认参数可直接连接）、RS485（默认参数可直接连接，硬件需使用转接头）三种任一种接口连接 ZDevelop；
3.设置基本运动参数 ATYPE、UNITS、SPEED、ACCEL、FWD_IN、REV_IN 等轴参数。

4.脉冲轴的相关参数比较多，需通过相关指令进行设定和查看，详细说明见“Basic 编程手册”中
“轴参数与轴状态指令”部分说明；也可以通过“ZDevelop/视图/轴参数”界面直观查看。


5.通过 ZDevelop 视图栏中手动运动窗口操作控制相应运动即可。



参考 BASIC 例程

BASE(0,1)	'选择轴
ATYPE = 1,1	'设置轴 0,1 为脉冲轴类型
UNITS = 1000,1000	'设置轴 0,1 脉冲当量为 1000 个脉冲为单位
SPEED = 10,10		'设置轴速度为 10*1000 脉冲/秒 ACCEL = 1000,1000	'	设置轴加速度 1000*1000 脉冲/秒/秒 FWD_IN = -1,-1	'禁用轴正向硬限位
REV_IN = -1,-1	'禁用轴负向硬限位
MOVE(10) AXIS(0)	'轴 0 正向运动 10*1000 个脉冲的距离
MOVE(-20) AXIS(0)	'轴 0 负向运动 20*1000 个脉冲的距离


第四章	扩展模块	

控制器可通过 CAN 总线扩展资源，支持扩展数字量 IO、模拟量 AD/DA、脉冲轴资源，可选搭配 ZIO 系列 CAN 总线扩展模块或 ZMIO310 系列立式总线扩展模块，各扩展模块详情请参考对应产品用户手册。


可选 ZIO 系列扩展模块或 ZMIO310-CAN 耦合器带子模块。
控制器连接 CAN 总线扩展模块，扩展模块的拨码开关第八位拨 ON 表示接入了一个 120 欧电阻，还需外接一个 120 欧电阻。连接多个 CAN 扩展模块时，只需将最后一个扩展模块的第八位拨 ON，其他模块第八位拨码不用拨。


IO 扩展模块为双电源供电，除了主电源，需要额外再接一个 IO 电源，给 IO 独立供电，主电源与 IO 电源均采用 24V 直流电源。ZAIO 模拟量扩展模块只需接主电源，无需 IO 电源。
为防止干扰，IO 电源和主电源分开。
请根据需求选择扩展模块，根据扩展模块资源选择 IO 映射或轴映射，注意映射的编号需避开已有资源。
ZIO 扩展模块连接控制器实物接线参考示例和 CAN 总线标准接线如下图所示：






ZMC306X 控制器采用单电源供电，ZIO 扩展卡采用双电源供电，使用时扩展模块的主电源和控制器的主电源可共用一路电源。ZMC306X 控制器和 ZIO 扩展模块用不同电源供电时，控制器电源 EGND要连接扩展模块电源的 GND，否则可能烧坏 CAN。
CAN 总线上连接多个 ZIO 扩展模块时，在 CAN 总线的左右两端各接一个 120 欧的电阻，对于具有 8
位拨码的扩展模块，终端电阻可通过拨码实现。


ZCAN 扩展模块一般带 8 位拨码开关，拨 ON 生效，拨码含义如下：
1-4：4 位 CAN ID 用于 ZCAN 扩展模块 IO 地址映射，对应值 0-15；
5-6：CAN 通讯速度，对应值 0-3，可选四种不同的速度；
7：预留；
8：120 欧电阻，拨 ON 表示 CANL 和 CANH 间接入一个 120 欧电阻。
整个控制系统的 IO 编号不得重复，映射资源时需避开已有编号。拨码开关必须在上电之前拨好，上电后重新拨码无效，需再次上电才生效。
拨码 1-4 选择 CAN 地址，控制器根据 CAN 拨码地址来设定对应扩展模块的 IO 编号范围，拨码每位 OFF
时对应值 0，ON 时对应值 1，地址组合值=拨码 4×8+拨码 3×4+拨码 2×2+拨码 1；
拨码 5-6 选择 CAN 总线通讯速度，速度组合值=拨码 6×2+拨码 5×1，组合值范围 0-3。对应的速度如下所示：







控制器端通过 CANIO_ADDRESS 指令设置 CAN 通讯速度，同样也是有四种速度参数可供选择，通讯速度需要与组合值对应的扩展模块的通讯速度一致才可以互相通讯。
出厂默认通讯速度两边都是 500kbps，不需要设置这块，除非要改速度才需要用指令设置通讯速度。


CANIO_ADDRESS 指令为系统参数，还可以设置 CAN 通讯的主从端，控制器的缺省值 32 ，即
CANIO_ADDRESS=32 做主端，设置为 0-31 之间做从端。
CAN 通讯配置情况可在“控制器状态”窗口查看通讯配置。

CAN 扩展模块 IO 映射使用拨码开关 1-4 位，根据当前已包含 IO 点数（IN 和 OP 中最大编号，需包含轴接口内的 IO 点），使用 1-4 号拨码设置 ID，从而确定扩展 IO 的编号范围。
如控制器本身包含 28 个 IN，16 个 OP，那么第一个扩展版设置的起始地址应超过最大值 28，按下图规则应将拨码设置为组合值 1（二进制组合值 0001，从右往左对应拨码 1-4，此时拨码 1 置 ON，其他置 OFF），扩展版上的 IO 编号=扩展版编号值+起始 IO 编号值，其中，29-31 空缺出来的 IO 编号舍去不用。后续的扩展版则依次按 IO 点数继续确认拨码设置。
数字量起始 IO 映射编号从 16 开始，按 16 的倍数递增，不同拨码 ID 对应数字量 IO 编号分配情况如下表：























模拟量 AD 起始 IO 映射编号从 8 开始，按 8 的倍数递增。模拟量 DA 起始 IO 映射编号从 4 开始，按 4
的倍数递增。不同拨码 ID 对应数字量 IO 编号分配情况如下表：
拨码 1-4 组合值	起始 AD 编号	结束 AD 编号	起始 DA 编号	结束 DA 编号
0	8	15	4	7
1	16	23	8	11
2	24	31	12	15
3	32	39	16	19
4	40	47	20	23
5	48	55	24	27


6	56	63	28	31
7	64	71	32	35
8	72	79	36	39
9	80	87	40	43
10	88	95	44	47
11	96	103	48	51
12	104	111	52	55
13	112	119	56	59
14	120	127	60	63
15	128	135	64	67


CAN 总线扩展方式扩展脉冲轴时，可选 ZIO16082M，扩展两个脉冲轴，这两个脉冲轴需要映射绑定轴号后访问。
扩展轴需要进行轴映射操作，采用 AXIS_ADDRESS 指令映射，映射规则如下：
AXIS_ADDRESS(轴号)=(32*0)+ID	'扩展模块的本地轴接口 AXIS 0
AXIS_ADDRESS(轴号)=(32*1)+ID	'扩展模块的本地轴接口 AXIS 1
ID 为扩展模块 1-4 位地址拨码的组合值，映射完成设置 ATYPE 等轴参数后就可以使用扩展轴。示例：
ATYPE(6)=0	'设为虚拟轴
AXIS_ADDRESS(6)=1+(32*0)	'ZCAN 扩展模块 ID 为 1 的轴号 0 映射到轴 6 ATYPE(6)=8	'ZCAN 扩展轴类型，脉冲方向方式步进或伺服
UNITS(6)=1000	'脉冲当量 1000
SPEED(6)=100	'速度 100units/s
ACCEL(6)=1000	'加速度 1000units/s^2
MOVE(100) AXIS(6)	'扩展轴运动 100units
扩展资源查看：
按 CAN 接线，电源接通后，接线电阻拨码都设置正确，模块上的电源指示灯(POWER)、运行灯(RUN)亮、 IO 电源灯(IO POWER)亮，报警灯(ALM)不亮。同时 ZDevelop 软件的“控制器”-“控制器状态”-“ZCan 节点”显示扩展模块信息和扩展的 IO 编号范围。
连接多个扩展模块时的拨码 ID 与对应资源编号参考如下：

ALMRM 指示灯亮请检查接线，电阻以及拨码设置是否正确，以及控制器的 CANIO_ADDRESS 指令是否设置为主端(32)，CAN 通讯速度是否一致。


第五章	编程与应用	


ZDevelop 是正运动技术 ZMoiton 系列运动控制器的 PC 端程序开发调试与诊断软件，通过它用户能够很容易的对控制器进行程序编辑与配置，快速开发应用程序、实时诊断系统运行参数以及对运动控制器正在运行的程序进行实时调试，支持中英双语环境。
Basic、Plc 和 Hmi 之间可以多任务运行，其中 Basic 可以多任务号运行，可与 Plc 与 Hmi 混合编程。更新软件版本请前往正运动网站下载，网址：www.zmotion.com.cn。
步骤	操作	显示界面
















1	










打开 ZDevelop 编程软件，菜单栏“文件 ”-“ 新 建 项目”弹出另存为界面，输入文件名后保存形式后缀为 “.zpj” 的项目文件。	

















2	









菜单栏“ 文件”- “新建文件”，出现右图弹窗，选择新建的文件类型为 basic 后确认。支持 Basic/Plc/Hmi 混合编程。	




3	文件视图窗口双击文件右边自动运行的位置，输入任务
号“0”。	








4	

在程序输入窗口编辑好程序，点击保存文件，新建的 basic 文件会自动保存到项目 zpj 所在的文件下。
保存所有即保存该项目下的所有文件。	

		














5	



点击“ 控制器”- “连接”，没有控制器是可选择连接到仿真器仿真运行，点击“连接”- “ 连 接 到 仿 真器”。	

	
点击“连接”弹出 “连接到控制器”窗口，可选择串口连接或网口连接，选择匹配的串口参数或网口 IP 地址后，点击连接即可。	










6	点击菜单栏按钮
“RAM/ROM”-“ 下
载到 RAM/ROM”，下载成功命令和输出窗口会有提示，同时程序下载到控制器并自动运行。 RAM 下载掉电后程序不保存，ROM 下载掉电后程序保存。下载到 ROM 的程序下次连接上控制器之后程序会自动按
照任务号运行。	
成功下载到 RAM：

成功下载到 ROM：













7	



点击菜单栏“ 调试”-“启动/停止调试”调用任务与监视窗口。因为之前下载过了，这里选择附加到当前程序即可。	








8	

在 菜 单 栏 “ 视图”-“ 示波器”打开示波器窗口 示波器使用参见正运动小助手“快速入门|篇九：如何进行运动控制器示波器的应用”。	

注意：
1.打开工程项目时，选择打开项目 zpj 文件，若只打开其中的 Bas 文件，程序无法下载到控制器。
2.不建立项目的时候，只有 Bas 文件无法下载到控制器。
3.自动运行的数字 0 表示任务编号，以任务 0 运行程序，任务编号不具备优先级。
4.若整个工程项目内的文件都不设置任务编号，下载到控制器时，系统提示如下信息 WARN: no program set autorun。




控制器支持 windows，linux，Mac，Android，wince 各种操作系统下的开发，提供 vc，c#，vb.net， labview 等各种环境的 dll 库，如下图。上位机软件编程参考《ZMotion PC 函数库编程手册》。





使用 PC 上位机开发程序需要将 dll 动态链接到控制器，开发时需将 dll 库添加到头文件并声明，然后将程序下载到控制器。
VS 中的 c++项目开发过程如下：
步骤	操作	显示界面



1	

打开 VS，点击菜单“文件”→“新建”→ “项目”，启动创建项目向导。	







2	




选择开发语言为“Visual C++” 和程序类型“MFC应用程序”。	








3	





下一步，选择类型为“基于对话框”，下一步或者完成。	






4	

找到厂家提供的光盘资料里面的 C++函数库，路径如下(64 位库为例)。	

5	将上述路径下面的所有 DLL 相关库文件复制到新建的项目里面。












6	








在项目中添加静态库和相关头文件。 静态库 ： zauxdll.lib,zmotion.li b 相 关 头 文 件 ： zauxdll2.h,zmotion.h。	
1) 先右击头文件， 接着依次选
择 :“ 添
加 ”→“现	有
项”。	

		

2) 在弹出的窗口中依次添加静态库和相关头文件。	







7	




声明相关的头文件和定义控制器连接句柄，至此项目新建完成。	



第六章	运行与维护	

设备正确的运行及维护不但可以保证和延长设备本身的生命周期，为防止设备性能劣化或降低设备失效的概率，按事先规定的计划或相应技术条件的规定进行的技术管理措施。


工作环境等对设备有影响，所以，通常以 6 个月~1 年的检查周期为标准对其做定期检查，可以根据周围环境适当调整设备的检查周期，使其工作在规定的标准环境中。























常见问题	解决建议






电机不转动	1.轴类型 ATYPE 配置是否正确；
2.确认是否有硬件限位、软件限位、报警信号起作用，轴状态是否正常；
3.电机是否使能成功；
4.确认脉冲当量 UNITS、速度的值是否合适，如果有编码器反馈查看 MPOS 是否变换；
5.确认脉冲模式和驱动器的脉冲模式是否匹配；
6.控制器端或驱动器端是否产生报警；
7.检查接线是否正确；
8.确认控制器是否正常发送脉冲。




限位信号不起作用	1.限位传感器工作是否正常，“输入口”视图是否可以监控到限位传感器的信号变化；
2.限位开关的映射是否正确；
3.限位传感器和控制器的公共端是否相连。

输入口检测不到信号	1.检查是否需要 IO 电源；
2.检查信号电平是否与输入口匹配，排查公共端是否相连；
3.检查输出口编号是否与操作的一致。

输出口操作无响应	1.检查是否需要 IO 电源；
2.检查输出口编号是否与操作的一致。

POWER 灯亮，RUN 灯不亮	1.检查供电电源功率是否充足，此时最好给控制器单独供电，调整好后重启控制器；
2.ALM 灯是否有规律的闪烁（硬件问题）。
RUN 灯亮，ALM 灯也亮	1.程序运行错误，请查验 ZDevelop 错误代码，检查应用程序。


控制器与 PC 串口连接失败	1.串口参数是否被运行程序修改，可以通过?*SETCOM 查看当前的所有串口配置；
2.查看 PC 的串口参数与控制器是否匹配；
3.打开设备管理器，查看 PC 的串口驱动是否正常。


CAN 扩展模块连接不上	1.检查 CAN 接线和供电回路，120 欧姆电阻是否有安装在两端；
2.检查主从端配置，通讯速度配置等；
3.检查拨码开关，是否有多个扩展模块采用同样的 ID。
4.干扰严重的场合使用双绞线、屏蔽层接地，使用双电源供电（扩展模块主电源和 IO 电源分开供电）







控制器与 PC 网口连接失败	1.检查 PC 的 IP 地址，需要与控制器 IP 在同一网段；
2.检查控制器 IP 地址，可以用串口连接后查看、获取；
3.网口灯不亮时检查接线是否正常；
4.控制器的电源灯 POWER 和运行指示灯 RUN 是否正常亮起；
5.网线是否有问题，更换质量好的网线再尝试连接；
6.检查控制器 IP 是否和其他设备冲突；
7.检查控制器的网口通道 ETH 是否全部被其他设备占用，将其他设备断开之后在尝试连接；
8.多网卡的情况下建议禁用其他网卡，或者更换电脑再连接；
9.检查 PC 防火墙设置；
10.Ping 一下控制器 IP，看是否能 Ping 通控制器，若无法 Ping 通，检查物理接口，或者网线；
11.arp -a 查询 IP 地址和 MAC 地址.


第七章	售后服务	


本售后服务条款规定的服务内容适用于在中国市场上通过正运动技术及其授权的合法渠道购买的运动控制器、运动控制卡、扩展模块、人机界面等。


1.保修期：12 个月。
在保修期内，如果产品发生非人为故障，我们为您提供保修服务。请客户联系商务人员并填写《维修申请表》（主要信息如：产品型号、序列号、故障描述、特殊要求等），寄到我们公司,我们将在维修周期内完成维修并寄还给您。
保修期计算方法，一般按条码管理扫描出库时间作为发货时间（如果客户能提供确切的发货时间证明，也可以按照该时间作为发货时间）。
2.换货：
自产品发货之日起 3 个月内，如果产品发生非人为故障，我们可以为您更换同型号产品。

3.终身维护：
我们将为客户提供终身维护服务。在保修期内但不符合保修条件或超过保修期限的故障产品，我们提供有偿维修服务，在客户确认接受产品的维修费用后，我们安排进行产品的维修。但对已经停产的产品，或缺乏维修物料，或损坏过于严重无维修价值的返回品则无法提供维修服务。
4.维修费用：
1)保修期内的产品，非人为原因引起的故障，免费维修；
2)超保修期或人为损坏产品收费标准，我们将根据不同型号和损坏程度收取元件的成本费、人工费和运费；具体的费用，由对接的商务人员报价给您；
3)运费：保修范围内产品运费由我司负担单程，非保修范围内的产品运费由客户负担；
5.不享受免费保修的请况：
1)由于火灾、水灾、地震等不可抗力因素造成的产品故障；
2)由于客户安装或者使用不当所导致的损坏；
3)未经正运动技术授权的人员对产品进行了拆卸、维修或者改装造成的产品故障；
4)非正运动技术直销或授权的合法渠道购买的产品；
5)产品的编码撕毁、涂改或者其他原因造成的产品编码无法辨认；

