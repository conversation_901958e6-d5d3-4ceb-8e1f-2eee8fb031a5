/*
 * All feature dependencies.
 *
 * This list is generated by qmake from <qtbase>/src/corelib/global/qfeatures.txt
 */
#if !defined(QT_NO_DBUS) && (defined(QT_NO_PROPERTIES) || defined(QT_NO_XMLSTREAMREADER))
#  define QT_NO_DBUS
#endif
#if !defined(QT_NO_ACCESSIBILITY) && (defined(QT_NO_PROPERTIES) || defined(QT_NO_MENUBAR))
#  define QT_NO_ACCESSIBILITY
#endif
#if !defined(QT_NO_UNDOVIEW) && (defined(QT_NO_UNDOSTACK) || defined(QT_NO_LISTVIEW))
#  define QT_NO_UNDOVIEW
#endif
#if !defined(QT_NO_UNDOSTACK) && (defined(QT_NO_UNDOCOMMAND))
#  define QT_NO_UNDOSTACK
#endif
#if !defined(QT_NO_UNDOGROUP) && (defined(QT_NO_UNDOSTACK))
#  define QT_NO_UNDOGROUP
#endif
#if !defined(QT_NO_FSCOMPLETER) && (defined(QT_NO_FILESYSTEMMODEL) || defined(QT_NO_COMPLETER))
#  define QT_NO_FSCOMPLETER
#endif
#if !defined(QT_NO_BEARERMANAGEMENT) && (defined(QT_NO_LIBRARY) || defined(QT_NO_NETWORKINTERFACE) || defined(QT_NO_PROPERTIES))
#  define QT_NO_BEARERMANAGEMENT
#endif
#if !defined(QT_NO_SOCKS5) && (defined(QT_NO_NETWORKPROXY))
#  define QT_NO_SOCKS5
#endif
#if !defined(QT_NO_ICONV) && (defined(QT_NO_TEXTCODEC))
#  define QT_NO_ICONV
#endif
#if !defined(QT_NO_BIG_CODECS) && (defined(QT_NO_TEXTCODEC))
#  define QT_NO_BIG_CODECS
#endif
#if !defined(QT_NO_CODECS) && (defined(QT_NO_TEXTCODEC))
#  define QT_NO_CODECS
#endif
#if !defined(QT_NO_CUPS) && (defined(QT_NO_PRINTER) || defined(QT_NO_LIBRARY))
#  define QT_NO_CUPS
#endif
#if !defined(QT_NO_PRINTER) && (defined(QT_NO_PICTURE) || defined(QT_NO_TEMPORARYFILE) || defined(QT_NO_PDF))
#  define QT_NO_PRINTER
#endif
#if !defined(QT_NO_STYLE_STYLESHEET) && (defined(QT_NO_STYLE_WINDOWS) || defined(QT_NO_PROPERTIES) || defined(QT_NO_CSSPARSER))
#  define QT_NO_STYLE_STYLESHEET
#endif
#if !defined(QT_NO_STYLE_WINDOWSMOBILE) && (defined(QT_NO_STYLE_WINDOWS) || defined(QT_NO_IMAGEFORMAT_XPM))
#  define QT_NO_STYLE_WINDOWSMOBILE
#endif
#if !defined(QT_NO_STYLE_WINDOWSCE) && (defined(QT_NO_STYLE_WINDOWS) || defined(QT_NO_IMAGEFORMAT_XPM))
#  define QT_NO_STYLE_WINDOWSCE
#endif
#if !defined(QT_NO_STYLE_WINDOWSXP) && (defined(QT_NO_STYLE_WINDOWS))
#  define QT_NO_STYLE_WINDOWSXP
#endif
#if !defined(QT_NO_STYLE_WINDOWSVISTA) && (defined(QT_NO_STYLE_WINDOWSXP))
#  define QT_NO_STYLE_WINDOWSVISTA
#endif
#if !defined(QT_NO_DATAWIDGETMAPPER) && (defined(QT_NO_ITEMVIEWS) || defined(QT_NO_PROPERTIES))
#  define QT_NO_DATAWIDGETMAPPER
#endif
#if !defined(QT_NO_DIRMODEL) && (defined(QT_NO_ITEMVIEWS) || defined(QT_NO_FILESYSTEMMODEL))
#  define QT_NO_DIRMODEL
#endif
#if !defined(QT_NO_INPUTDIALOG) && (defined(QT_NO_COMBOBOX) || defined(QT_NO_SPINBOX) || defined(QT_NO_STACKEDWIDGET))
#  define QT_NO_INPUTDIALOG
#endif
#if !defined(QT_NO_PRINTPREVIEWDIALOG) && (defined(QT_NO_PRINTPREVIEWWIDGET) || defined(QT_NO_PRINTDIALOG) || defined(QT_NO_TOOLBAR))
#  define QT_NO_PRINTPREVIEWDIALOG
#endif
#if !defined(QT_NO_PRINTDIALOG) && (defined(QT_NO_PRINTER) || defined(QT_NO_COMBOBOX) || defined(QT_NO_BUTTONGROUP) || defined(QT_NO_SPINBOX) || defined(QT_NO_TREEVIEW) || defined(QT_NO_TABWIDGET))
#  define QT_NO_PRINTDIALOG
#endif
#if !defined(QT_NO_FONTDIALOG) && (defined(QT_NO_STRINGLISTMODEL) || defined(QT_NO_COMBOBOX) || defined(QT_NO_VALIDATOR) || defined(QT_NO_GROUPBOX))
#  define QT_NO_FONTDIALOG
#endif
#if !defined(QT_NO_FILEDIALOG) && (defined(QT_NO_DIRMODEL) || defined(QT_NO_TREEVIEW) || defined(QT_NO_COMBOBOX) || defined(QT_NO_TOOLBUTTON) || defined(QT_NO_BUTTONGROUP) || defined(QT_NO_TOOLTIP) || defined(QT_NO_SPLITTER) || defined(QT_NO_STACKEDWIDGET) || defined(QT_NO_PROXYMODEL))
#  define QT_NO_FILEDIALOG
#endif
#if !defined(QT_NO_KEYSEQUENCEEDIT) && (defined(QT_NO_LINEEDIT) || defined(QT_NO_SHORTCUT))
#  define QT_NO_KEYSEQUENCEEDIT
#endif
#if !defined(QT_NO_PRINTPREVIEWWIDGET) && (defined(QT_NO_GRAPHICSVIEW) || defined(QT_NO_PRINTER) || defined(QT_NO_MAINWINDOW))
#  define QT_NO_PRINTPREVIEWWIDGET
#endif
#if !defined(QT_NO_CALENDARWIDGET) && (defined(QT_NO_TABLEVIEW) || defined(QT_NO_MENU) || defined(QT_NO_TEXTDATE) || defined(QT_NO_SPINBOX) || defined(QT_NO_TOOLBUTTON))
#  define QT_NO_CALENDARWIDGET
#endif
#if !defined(QT_NO_PROGRESSDIALOG) && (defined(QT_NO_PROGRESSBAR))
#  define QT_NO_PROGRESSDIALOG
#endif
#if !defined(QT_NO_MENUBAR) && (defined(QT_NO_MENU) || defined(QT_NO_TOOLBUTTON))
#  define QT_NO_MENUBAR
#endif
#if !defined(QT_NO_DIAL) && (defined(QT_NO_SLIDER))
#  define QT_NO_DIAL
#endif
#if !defined(QT_NO_SCROLLBAR) && (defined(QT_NO_SLIDER))
#  define QT_NO_SCROLLBAR
#endif
#if !defined(QT_NO_SCROLLAREA) && (defined(QT_NO_SCROLLBAR))
#  define QT_NO_SCROLLAREA
#endif
#if !defined(QT_NO_GRAPHICSVIEW) && (defined(QT_NO_SCROLLAREA))
#  define QT_NO_GRAPHICSVIEW
#endif
#if !defined(QT_NO_GRAPHICSEFFECT) && (defined(QT_NO_GRAPHICSVIEW))
#  define QT_NO_GRAPHICSEFFECT
#endif
#if !defined(QT_NO_MDIAREA) && (defined(QT_NO_SCROLLAREA))
#  define QT_NO_MDIAREA
#endif
#if !defined(QT_NO_DOCKWIDGET) && (defined(QT_NO_RUBBERBAND) || defined(QT_NO_MAINWINDOW))
#  define QT_NO_DOCKWIDGET
#endif
#if !defined(QT_NO_BUTTONGROUP) && (defined(QT_NO_GROUPBOX))
#  define QT_NO_BUTTONGROUP
#endif
#if !defined(QT_NO_TOOLBOX) && (defined(QT_NO_TOOLBUTTON) || defined(QT_NO_SCROLLAREA))
#  define QT_NO_TOOLBOX
#endif
#if !defined(QT_NO_MAINWINDOW) && (defined(QT_NO_MENU) || defined(QT_NO_RESIZEHANDLER) || defined(QT_NO_TOOLBUTTON))
#  define QT_NO_MAINWINDOW
#endif
#if !defined(QT_NO_TOOLBAR) && (defined(QT_NO_MAINWINDOW))
#  define QT_NO_TOOLBAR
#endif
#if !defined(QT_NO_FONTCOMBOBOX) && (defined(QT_NO_COMBOBOX) || defined(QT_NO_STRINGLISTMODEL))
#  define QT_NO_FONTCOMBOBOX
#endif
#if !defined(QT_NO_COMBOBOX) && (defined(QT_NO_LINEEDIT) || defined(QT_NO_STANDARDITEMMODEL) || defined(QT_NO_LISTVIEW))
#  define QT_NO_COMBOBOX
#endif
#if !defined(QT_NO_TABWIDGET) && (defined(QT_NO_TABBAR) || defined(QT_NO_STACKEDWIDGET))
#  define QT_NO_TABWIDGET
#endif
#if !defined(QT_NO_SPINBOX) && (defined(QT_NO_SPINWIDGET) || defined(QT_NO_LINEEDIT) || defined(QT_NO_VALIDATOR))
#  define QT_NO_SPINBOX
#endif
#if !defined(QT_NO_COLORDIALOG) && (defined(QT_NO_SPINBOX))
#  define QT_NO_COLORDIALOG
#endif
#if !defined(QT_NO_SPLITTER) && (defined(QT_NO_RUBBERBAND))
#  define QT_NO_SPLITTER
#endif
#if !defined(QT_NO_TEXTEDIT) && (defined(QT_NO_SCROLLAREA) || defined(QT_NO_PROPERTIES))
#  define QT_NO_TEXTEDIT
#endif
#if !defined(QT_NO_ERRORMESSAGE) && (defined(QT_NO_TEXTEDIT))
#  define QT_NO_ERRORMESSAGE
#endif
#if !defined(QT_NO_SYNTAXHIGHLIGHTER) && (defined(QT_NO_TEXTEDIT))
#  define QT_NO_SYNTAXHIGHLIGHTER
#endif
#if !defined(QT_NO_TEXTBROWSER) && (defined(QT_NO_TEXTEDIT))
#  define QT_NO_TEXTBROWSER
#endif
#if !defined(QT_NO_DATETIMEEDIT) && (defined(QT_NO_CALENDARWIDGET) || defined(QT_NO_DATESTRING))
#  define QT_NO_DATETIMEEDIT
#endif
#if !defined(QT_NO_ITEMVIEWS) && (defined(QT_NO_RUBBERBAND) || defined(QT_NO_SCROLLAREA))
#  define QT_NO_ITEMVIEWS
#endif
#if !defined(QT_NO_STRINGLISTMODEL) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_STRINGLISTMODEL
#endif
#if !defined(QT_NO_PROXYMODEL) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_PROXYMODEL
#endif
#if !defined(QT_NO_COMPLETER) && (defined(QT_NO_PROXYMODEL))
#  define QT_NO_COMPLETER
#endif
#if !defined(QT_NO_IDENTITYPROXYMODEL) && (defined(QT_NO_PROXYMODEL))
#  define QT_NO_IDENTITYPROXYMODEL
#endif
#if !defined(QT_NO_SORTFILTERPROXYMODEL) && (defined(QT_NO_PROXYMODEL))
#  define QT_NO_SORTFILTERPROXYMODEL
#endif
#if !defined(QT_NO_STANDARDITEMMODEL) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_STANDARDITEMMODEL
#endif
#if !defined(QT_NO_TABLEVIEW) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_TABLEVIEW
#endif
#if !defined(QT_NO_TABLEWIDGET) && (defined(QT_NO_TABLEVIEW))
#  define QT_NO_TABLEWIDGET
#endif
#if !defined(QT_NO_LISTVIEW) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_LISTVIEW
#endif
#if !defined(QT_NO_COLUMNVIEW) && (defined(QT_NO_LISTVIEW))
#  define QT_NO_COLUMNVIEW
#endif
#if !defined(QT_NO_LISTWIDGET) && (defined(QT_NO_LISTVIEW))
#  define QT_NO_LISTWIDGET
#endif
#if !defined(QT_NO_TREEVIEW) && (defined(QT_NO_ITEMVIEWS))
#  define QT_NO_TREEVIEW
#endif
#if !defined(QT_NO_TREEWIDGET) && (defined(QT_NO_TREEVIEW))
#  define QT_NO_TREEWIDGET
#endif
#if !defined(QT_NO_LOCALSERVER) && (defined(QT_NO_TEMPORARYFILE))
#  define QT_NO_LOCALSERVER
#endif
#if !defined(QT_NO_NETWORKDISKCACHE) && (defined(QT_NO_TEMPORARYFILE))
#  define QT_NO_NETWORKDISKCACHE
#endif
#if !defined(QT_NO_PDF) && (defined(QT_NO_TEMPORARYFILE))
#  define QT_NO_PDF
#endif
#if !defined(QT_NO_FTP) && (defined(QT_NO_TEXTDATE))
#  define QT_NO_FTP
#endif
#if !defined(QT_NO_DATESTRING) && (defined(QT_NO_TEXTDATE))
#  define QT_NO_DATESTRING
#endif
#if !defined(QT_NO_IMAGEFORMATPLUGIN) && (defined(QT_NO_LIBRARY))
#  define QT_NO_IMAGEFORMATPLUGIN
#endif
#if !defined(QT_NO_IM) && (defined(QT_NO_LIBRARY))
#  define QT_NO_IM
#endif
#if !defined(QT_NO_TOOLBUTTON) && (defined(QT_NO_ACTION))
#  define QT_NO_TOOLBUTTON
#endif
#if !defined(QT_NO_WHATSTHIS) && (defined(QT_NO_TOOLBUTTON))
#  define QT_NO_WHATSTHIS
#endif
#if !defined(QT_NO_TABBAR) && (defined(QT_NO_TOOLBUTTON))
#  define QT_NO_TABBAR
#endif
#if !defined(QT_NO_MENU) && (defined(QT_NO_ACTION))
#  define QT_NO_MENU
#endif
#if !defined(QT_NO_CONTEXTMENU) && (defined(QT_NO_MENU))
#  define QT_NO_CONTEXTMENU
#endif
#if !defined(QT_NO_STYLE_FUSION) && (defined(QT_NO_IMAGEFORMAT_XPM))
#  define QT_NO_STYLE_FUSION
#endif
#if !defined(QT_NO_DRAGANDDROP) && (defined(QT_NO_IMAGEFORMAT_XPM))
#  define QT_NO_DRAGANDDROP
#endif
#if !defined(QT_NO_XMLSTREAMREADER) && (defined(QT_NO_XMLSTREAM))
#  define QT_NO_XMLSTREAMREADER
#endif
#if !defined(QT_NO_XMLSTREAMWRITER) && (defined(QT_NO_XMLSTREAM))
#  define QT_NO_XMLSTREAMWRITER
#endif
#if !defined(QT_NO_TEXTODFWRITER) && (defined(QT_NO_XMLSTREAMWRITER))
#  define QT_NO_TEXTODFWRITER
#endif
#if !defined(QT_NO_STATEMACHINE) && (defined(QT_NO_PROPERTIES))
#  define QT_NO_STATEMACHINE
#endif
#if !defined(QT_NO_ANIMATION) && (defined(QT_NO_PROPERTIES))
#  define QT_NO_ANIMATION
#endif
#if !defined(QT_NO_WIZARD) && (defined(QT_NO_PROPERTIES))
#  define QT_NO_WIZARD
#endif
