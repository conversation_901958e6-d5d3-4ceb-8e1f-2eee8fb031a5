#############################################################################
# Makefile for building: SetVariable
# Generated by qmake (3.0) (Qt 5.5.0)
# Project:  ..\easyhmi_test\SetVariable.pro
# Template: app
# Command: D:\zz\LinuxQtKit\LQTK\bin\qmake.exe -spec easyhmi-g++ "CONFIG+=release c++11" -o Makefile ..\easyhmi_test\SetVariable.pro
#############################################################################

MAKEFILE      = Makefile

####### Compiler, tools and options

CC            = D:/zz/LinuxQtKit/LNDK/bin/arm-none-linux-gnueabi-gcc
CXX           = D:/zz/LinuxQtKit/LNDK/bin/arm-none-linux-gnueabi-g++
DEFINES       = -DQT_NO_DEBUG -DQT_WIDGETS_LIB -DQT_GUI_LIB -DQT_CORE_LIB
CFLAGS        = -pipe -DLINUX=1 -O2 -DLINUX=1 -Wall -W -D_REENTRANT -fPIC $(DEFINES)
CXXFLAGS      = -pipe -DLINUX=1 -O2 -DLINUX=1 -std=c++0x -Wall -W -D_REENTRANT -fPIC $(DEFINES)
INCPATH       = -I..\easyhmi_test -I. -I..\easyhmi_test -I..\easyhmi_test -I..\LinuxQtKit\LQTK\include -I..\LinuxQtKit\LQTK\include\QtWidgets -I..\LinuxQtKit\LQTK\include\QtGui -I..\LinuxQtKit\LQTK\include\QtCore -I. -I. -I..\LinuxQtKit\LQTK\mkspecs\easyhmi-g++
QMAKE         = D:\zz\LinuxQtKit\LQTK\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
TAR           = 
COMPRESS      = 
DISTNAME      = SetVariable1.0.0
DISTDIR = D:\zz\runrelease\.tmp\SetVariable1.0.0
LINK          = D:/zz/LinuxQtKit/LNDK/bin/arm-none-linux-gnueabi-g++
LFLAGS        = -Wl,-O1 -Wl,-rpath,D:/home/<USER>/SWIKOON/SWK.EASYHMI/N32926.frame/3.GUIKIT/qt550/prefix_qt550/lib
LIBS          = $(SUBLIBS) -lrt -LD:/zz/easyhmi_test/ -lzmotion -LD:/zz/LinuxQtKit/LQTK/lib -lQt5Widgets -lQt5Gui -lQt5Core -lpthread 
AR            = D:/zz/LinuxQtKit/LNDK/bin/arm-none-linux-gnueabi-ar cqs
RANLIB        = 
SED           = $(QMAKE) -install sed
STRIP         = D:/zz/LinuxQtKit/LNDK/bin/arm-none-linux-gnueabi-strip

####### Output directory

OBJECTS_DIR   = ./

####### Files

SOURCES       = ..\easyhmi_test\main.cpp \
		..\easyhmi_test\mainwindow.cpp \
		..\easyhmi_test\zaux.cpp moc_mainwindow.cpp
OBJECTS       = main.obj \
		mainwindow.obj \
		zaux.obj \
		moc_mainwindow.obj
DIST          =  ..\easyhmi_test\mainwindow.h \
		..\easyhmi_test\zaux.h ..\easyhmi_test\main.cpp \
		..\easyhmi_test\mainwindow.cpp \
		..\easyhmi_test\zaux.cpp
QMAKE_TARGET  = SetVariable
DESTDIR       = #avoid trailing-slash linebreak
TARGET        = SetVariable


first: all
####### Implicit rules

.SUFFIXES: .obj .c .cpp .cc .cxx

.cpp.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o "$@" "$<"

.cc.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o "$@" "$<"

.cxx.obj:
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o "$@" "$<"

.c.obj:
	$(CC) -c $(CFLAGS) $(INCPATH) -o "$@" "$<"

####### Build rules

$(TARGET): ui_mainwindow.h $(OBJECTS)  
	$(LINK) $(LFLAGS) -o $(TARGET) $(OBJECTS) $(OBJCOMP) $(LIBS)

Makefile: ..\easyhmi_test\SetVariable.pro ..\LinuxQtKit\LQTK\mkspecs\easyhmi-g++\qmake.conf ..\LinuxQtKit\LQTK\mkspecs\features\spec_pre.prf \
		..\LinuxQtKit\LQTK\mkspecs\common\unix.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\linux.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\sanitize.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\gcc-base.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\gcc-base-unix.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\g++-base.conf \
		..\LinuxQtKit\LQTK\mkspecs\common\g++-unix.conf \
		..\LinuxQtKit\LQTK\mkspecs\qconfig.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_bootstrap_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_concurrent.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_concurrent_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_core.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_core_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_dbus.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_dbus_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_gui.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_gui_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_network.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_network_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_platformsupport_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_printsupport.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_printsupport_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qml.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qml_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qmldevtools_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_serialport.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_serialport_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_sql.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_sql_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_testlib.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_testlib_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_widgets.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_widgets_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_xml.pri \
		..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_xml_private.pri \
		..\LinuxQtKit\LQTK\mkspecs\features\qt_functions.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\qt_config.prf \
		..\LinuxQtKit\LQTK\mkspecs\easyhmi-g++\qmake.conf \
		..\LinuxQtKit\LQTK\mkspecs\features\spec_post.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\exclusive_builds.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\default_pre.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\resolve_config.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\default_post.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\c++11.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\warn_on.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\qt.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\resources.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\moc.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\uic.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\unix\thread.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\testcase_targets.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\exceptions.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\yacc.prf \
		..\LinuxQtKit\LQTK\mkspecs\features\lex.prf \
		..\easyhmi_test\SetVariable.pro
	$(QMAKE) -spec easyhmi-g++ "CONFIG+=release c++11" -o Makefile ..\easyhmi_test\SetVariable.pro
..\LinuxQtKit\LQTK\mkspecs\features\spec_pre.prf:
..\LinuxQtKit\LQTK\mkspecs\common\unix.conf:
..\LinuxQtKit\LQTK\mkspecs\common\linux.conf:
..\LinuxQtKit\LQTK\mkspecs\common\sanitize.conf:
..\LinuxQtKit\LQTK\mkspecs\common\gcc-base.conf:
..\LinuxQtKit\LQTK\mkspecs\common\gcc-base-unix.conf:
..\LinuxQtKit\LQTK\mkspecs\common\g++-base.conf:
..\LinuxQtKit\LQTK\mkspecs\common\g++-unix.conf:
..\LinuxQtKit\LQTK\mkspecs\qconfig.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_bootstrap_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_concurrent.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_concurrent_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_core.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_core_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_dbus.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_dbus_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_gui.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_gui_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_network.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_network_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_platformsupport_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_printsupport.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_printsupport_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qml.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qml_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_qmldevtools_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_serialport.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_serialport_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_sql.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_sql_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_testlib.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_testlib_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_widgets.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_widgets_private.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_xml.pri:
..\LinuxQtKit\LQTK\mkspecs\modules\qt_lib_xml_private.pri:
..\LinuxQtKit\LQTK\mkspecs\features\qt_functions.prf:
..\LinuxQtKit\LQTK\mkspecs\features\qt_config.prf:
..\LinuxQtKit\LQTK\mkspecs\easyhmi-g++\qmake.conf:
..\LinuxQtKit\LQTK\mkspecs\features\spec_post.prf:
..\LinuxQtKit\LQTK\mkspecs\features\exclusive_builds.prf:
..\LinuxQtKit\LQTK\mkspecs\features\default_pre.prf:
..\LinuxQtKit\LQTK\mkspecs\features\resolve_config.prf:
..\LinuxQtKit\LQTK\mkspecs\features\default_post.prf:
..\LinuxQtKit\LQTK\mkspecs\features\c++11.prf:
..\LinuxQtKit\LQTK\mkspecs\features\warn_on.prf:
..\LinuxQtKit\LQTK\mkspecs\features\qt.prf:
..\LinuxQtKit\LQTK\mkspecs\features\resources.prf:
..\LinuxQtKit\LQTK\mkspecs\features\moc.prf:
..\LinuxQtKit\LQTK\mkspecs\features\uic.prf:
..\LinuxQtKit\LQTK\mkspecs\features\unix\thread.prf:
..\LinuxQtKit\LQTK\mkspecs\features\testcase_targets.prf:
..\LinuxQtKit\LQTK\mkspecs\features\exceptions.prf:
..\LinuxQtKit\LQTK\mkspecs\features\yacc.prf:
..\LinuxQtKit\LQTK\mkspecs\features\lex.prf:
..\easyhmi_test\SetVariable.pro:
qmake: FORCE
	@$(QMAKE) -spec easyhmi-g++ "CONFIG+=release c++11" -o Makefile ..\easyhmi_test\SetVariable.pro

qmake_all: FORCE


all: Makefile $(TARGET)

dist: distdir FORCE
	(cd `dirname $(DISTDIR)` && $(TAR) $(DISTNAME).tar $(DISTNAME) && $(COMPRESS) $(DISTNAME).tar) && $(MOVE) `dirname $(DISTDIR)`\$(DISTNAME).tar.gz . && $(DEL_FILE) -r $(DISTDIR)

distdir: FORCE
	@if not exist $(DISTDIR) mkdir $(DISTDIR) & if not exist $(DISTDIR) exit 1
	$(COPY_FILE) --parents $(DIST) $(DISTDIR)\
	$(COPY_FILE) --parents ..\easyhmi_test\mainwindow.h ..\easyhmi_test\zaux.h $(DISTDIR)\
	$(COPY_FILE) --parents ..\easyhmi_test\main.cpp ..\easyhmi_test\mainwindow.cpp ..\easyhmi_test\zaux.cpp $(DISTDIR)\
	$(COPY_FILE) --parents ..\easyhmi_test\mainwindow.ui $(DISTDIR)\


clean: compiler_clean 
	-$(DEL_FILE) $(OBJECTS)
	-$(DEL_FILE) *~ core *.core


distclean: clean 
	-$(DEL_FILE) $(TARGET) 
	-$(DEL_FILE) Makefile


####### Sub-libraries

mocclean: compiler_moc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_source_make_all

check: first

compiler_rcc_make_all:
compiler_rcc_clean:
compiler_moc_header_make_all: moc_mainwindow.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) moc_mainwindow.cpp
moc_mainwindow.cpp: ..\LinuxQtKit\LQTK\include\QtWidgets\QMainWindow \
		..\LinuxQtKit\LQTK\include\QtWidgets\qmainwindow.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qwidget.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobal.h \
		..\LinuxQtKit\LQTK\include\QtCore\qconfig.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfeatures.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsystemdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qprocessordetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcompilerdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypeinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypetraits.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsysinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlogging.h \
		..\LinuxQtKit\LQTK\include\QtCore\qflags.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbasicatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_bootstrap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qgenericatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_cxx11.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_gcc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_msvc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv7.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv6.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv5.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_ia64.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_mips.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_x86.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_unix.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobalstatic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmutex.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnumeric.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnamespace.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs_impl.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs_win.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstring.h \
		..\LinuxQtKit\LQTK\include\QtCore\qchar.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrefcount.h \
		..\LinuxQtKit\LQTK\include\QtCore\qarraydata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringbuilder.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qalgorithms.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiterator.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearraylist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qregexp.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringmatcher.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcoreevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qscopedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmetatype.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvarlengtharray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontainerfwd.h \
		..\LinuxQtKit\LQTK\include\QtCore\qisenum.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmargins.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpaintdevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrect.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsize.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpoint.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpalette.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcolor.h \
		..\LinuxQtKit\LQTK\include\QtGui\qrgb.h \
		..\LinuxQtKit\LQTK\include\QtGui\qbrush.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpair.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvector.h \
		..\LinuxQtKit\LQTK\include\QtGui\qmatrix.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpolygon.h \
		..\LinuxQtKit\LQTK\include\QtGui\qregion.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdatastream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiodevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qline.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtransform.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpainterpath.h \
		..\LinuxQtKit\LQTK\include\QtGui\qimage.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixelformat.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qshareddata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qhash.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer_impl.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfont.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontmetrics.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontinfo.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qsizepolicy.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcursor.h \
		..\LinuxQtKit\LQTK\include\QtGui\qkeysequence.h \
		..\LinuxQtKit\LQTK\include\QtGui\qevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvariant.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdebug.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtextstream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlocale.h \
		..\LinuxQtKit\LQTK\include\QtCore\qset.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontiguouscache.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurlquery.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfile.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfiledevice.h \
		..\LinuxQtKit\LQTK\include\QtGui\qvector2d.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtouchdevice.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qtabwidget.h \
		..\LinuxQtKit\LQTK\include\QtGui\qicon.h \
		..\LinuxQtKit\LQTK\include\QtCore\QDebug \
		..\easyhmi_test\zaux.h \
		..\easyhmi_test\zmotion.h \
		..\easyhmi_test\mainwindow.h
	D:\zz\LinuxQtKit\LQTK\bin\moc.exe $(DEFINES) -ID:/zz/LinuxQtKit/LQTK/mkspecs/easyhmi-g++ -ID:/zz/easyhmi_test -ID:/zz/easyhmi_test -ID:/zz/easyhmi_test -ID:/zz/LinuxQtKit/LQTK/include -ID:/zz/LinuxQtKit/LQTK/include/QtWidgets -ID:/zz/LinuxQtKit/LQTK/include/QtGui -ID:/zz/LinuxQtKit/LQTK/include/QtCore -I. -I/home/<USER>/Compile/arm-2014.05/arm-none-linux-gnueabi/include/c++/4.8.3 -I/home/<USER>/Compile/arm-2014.05/arm-none-linux-gnueabi/include/c++/4.8.3/arm-none-linux-gnueabi -I/home/<USER>/Compile/arm-2014.05/arm-none-linux-gnueabi/include/c++/4.8.3/backward -I/home/<USER>/Compile/arm-2014.05/lib/gcc/arm-none-linux-gnueabi/4.8.3/include -I/home/<USER>/Compile/arm-2014.05/lib/gcc/arm-none-linux-gnueabi/4.8.3/include-fixed -I/home/<USER>/Compile/arm-2014.05/arm-none-linux-gnueabi/include -I/home/<USER>/Compile/arm-2014.05/arm-none-linux-gnueabi/libc/usr/include ..\easyhmi_test\mainwindow.h -o moc_mainwindow.cpp

compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_mainwindow.h
compiler_uic_clean:
	-$(DEL_FILE) ui_mainwindow.h
ui_mainwindow.h: ..\easyhmi_test\mainwindow.ui
	D:\zz\LinuxQtKit\LQTK\bin\uic.exe ..\easyhmi_test\mainwindow.ui -o ui_mainwindow.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_moc_header_clean compiler_uic_clean 

####### Compile

main.obj: ..\easyhmi_test\main.cpp ..\LinuxQtKit\LQTK\include\QtWidgets\QApplication \
		..\LinuxQtKit\LQTK\include\QtWidgets\qapplication.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcoreapplication.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobal.h \
		..\LinuxQtKit\LQTK\include\QtCore\qconfig.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfeatures.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsystemdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qprocessordetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcompilerdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypeinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypetraits.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsysinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlogging.h \
		..\LinuxQtKit\LQTK\include\QtCore\qflags.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbasicatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_bootstrap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qgenericatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_cxx11.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_gcc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_msvc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv7.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv6.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv5.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_ia64.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_mips.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_x86.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_unix.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobalstatic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmutex.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnumeric.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstring.h \
		..\LinuxQtKit\LQTK\include\QtCore\qchar.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrefcount.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnamespace.h \
		..\LinuxQtKit\LQTK\include\QtCore\qarraydata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringbuilder.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qalgorithms.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiterator.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearraylist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qregexp.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringmatcher.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcoreevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qscopedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmetatype.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvarlengtharray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontainerfwd.h \
		..\LinuxQtKit\LQTK\include\QtCore\qisenum.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qeventloop.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs_win.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpoint.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsize.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcursor.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qdesktopwidget.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qwidget.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmargins.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpaintdevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrect.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpalette.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcolor.h \
		..\LinuxQtKit\LQTK\include\QtGui\qrgb.h \
		..\LinuxQtKit\LQTK\include\QtGui\qbrush.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpair.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvector.h \
		..\LinuxQtKit\LQTK\include\QtGui\qmatrix.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpolygon.h \
		..\LinuxQtKit\LQTK\include\QtGui\qregion.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdatastream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiodevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qline.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtransform.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpainterpath.h \
		..\LinuxQtKit\LQTK\include\QtGui\qimage.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixelformat.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qshareddata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qhash.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer_impl.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfont.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontmetrics.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontinfo.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qsizepolicy.h \
		..\LinuxQtKit\LQTK\include\QtGui\qkeysequence.h \
		..\LinuxQtKit\LQTK\include\QtGui\qevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvariant.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdebug.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtextstream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlocale.h \
		..\LinuxQtKit\LQTK\include\QtCore\qset.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontiguouscache.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurlquery.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfile.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfiledevice.h \
		..\LinuxQtKit\LQTK\include\QtGui\qvector2d.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtouchdevice.h \
		..\LinuxQtKit\LQTK\include\QtGui\qguiapplication.h \
		..\LinuxQtKit\LQTK\include\QtGui\qinputmethod.h \
		../easyhmi_test/mainwindow.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\QMainWindow \
		..\LinuxQtKit\LQTK\include\QtWidgets\qmainwindow.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qtabwidget.h \
		..\LinuxQtKit\LQTK\include\QtGui\qicon.h \
		..\LinuxQtKit\LQTK\include\QtCore\QDebug \
		../easyhmi_test/zaux.h \
		..\easyhmi_test\zmotion.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o main.obj ..\easyhmi_test\main.cpp

mainwindow.obj: ..\easyhmi_test\mainwindow.cpp ../easyhmi_test/mainwindow.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\QMainWindow \
		..\LinuxQtKit\LQTK\include\QtWidgets\qmainwindow.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qwidget.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobal.h \
		..\LinuxQtKit\LQTK\include\QtCore\qconfig.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfeatures.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsystemdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qprocessordetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcompilerdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypeinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypetraits.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsysinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlogging.h \
		..\LinuxQtKit\LQTK\include\QtCore\qflags.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbasicatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_bootstrap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qgenericatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_cxx11.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_gcc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_msvc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv7.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv6.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv5.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_ia64.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_mips.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_x86.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_unix.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobalstatic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmutex.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnumeric.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnamespace.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs_impl.h \
		..\LinuxQtKit\LQTK\include\QtGui\qwindowdefs_win.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstring.h \
		..\LinuxQtKit\LQTK\include\QtCore\qchar.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrefcount.h \
		..\LinuxQtKit\LQTK\include\QtCore\qarraydata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringbuilder.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qalgorithms.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiterator.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearraylist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qregexp.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringmatcher.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcoreevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qscopedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmetatype.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvarlengtharray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontainerfwd.h \
		..\LinuxQtKit\LQTK\include\QtCore\qisenum.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmargins.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpaintdevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrect.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsize.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpoint.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpalette.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcolor.h \
		..\LinuxQtKit\LQTK\include\QtGui\qrgb.h \
		..\LinuxQtKit\LQTK\include\QtGui\qbrush.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpair.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvector.h \
		..\LinuxQtKit\LQTK\include\QtGui\qmatrix.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpolygon.h \
		..\LinuxQtKit\LQTK\include\QtGui\qregion.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdatastream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiodevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qline.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtransform.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpainterpath.h \
		..\LinuxQtKit\LQTK\include\QtGui\qimage.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixelformat.h \
		..\LinuxQtKit\LQTK\include\QtGui\qpixmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qshareddata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qhash.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsharedpointer_impl.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfont.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontmetrics.h \
		..\LinuxQtKit\LQTK\include\QtGui\qfontinfo.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qsizepolicy.h \
		..\LinuxQtKit\LQTK\include\QtGui\qcursor.h \
		..\LinuxQtKit\LQTK\include\QtGui\qkeysequence.h \
		..\LinuxQtKit\LQTK\include\QtGui\qevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvariant.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qdebug.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtextstream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlocale.h \
		..\LinuxQtKit\LQTK\include\QtCore\qset.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontiguouscache.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qurlquery.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfile.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfiledevice.h \
		..\LinuxQtKit\LQTK\include\QtGui\qvector2d.h \
		..\LinuxQtKit\LQTK\include\QtGui\qtouchdevice.h \
		..\LinuxQtKit\LQTK\include\QtWidgets\qtabwidget.h \
		..\LinuxQtKit\LQTK\include\QtGui\qicon.h \
		..\LinuxQtKit\LQTK\include\QtCore\QDebug \
		../easyhmi_test/zaux.h \
		..\easyhmi_test\zmotion.h \
		ui_mainwindow.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o mainwindow.obj ..\easyhmi_test\mainwindow.cpp

zaux.obj: ..\easyhmi_test\zaux.cpp ../easyhmi_test/zaux.h \
		..\easyhmi_test\zmotion.h \
		..\LinuxQtKit\LQTK\include\QtCore\QString \
		..\LinuxQtKit\LQTK\include\QtCore\qstring.h \
		..\LinuxQtKit\LQTK\include\QtCore\qchar.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobal.h \
		..\LinuxQtKit\LQTK\include\QtCore\qconfig.h \
		..\LinuxQtKit\LQTK\include\QtCore\qfeatures.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsystemdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qprocessordetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcompilerdetection.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypeinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtypetraits.h \
		..\LinuxQtKit\LQTK\include\QtCore\qsysinfo.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlogging.h \
		..\LinuxQtKit\LQTK\include\QtCore\qflags.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbasicatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_bootstrap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qgenericatomic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_cxx11.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_gcc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_msvc.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv7.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv6.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_armv5.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_ia64.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_mips.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_x86.h \
		..\LinuxQtKit\LQTK\include\QtCore\qatomic_unix.h \
		..\LinuxQtKit\LQTK\include\QtCore\qglobalstatic.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmutex.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnumeric.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qrefcount.h \
		..\LinuxQtKit\LQTK\include\QtCore\qnamespace.h \
		..\LinuxQtKit\LQTK\include\QtCore\qarraydata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringbuilder.h \
		..\LinuxQtKit\LQTK\include\QtCore\QDebug \
		..\LinuxQtKit\LQTK\include\QtCore\qdebug.h \
		..\LinuxQtKit\LQTK\include\QtCore\qalgorithms.h \
		..\LinuxQtKit\LQTK\include\QtCore\qhash.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiterator.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qbytearraylist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringlist.h \
		..\LinuxQtKit\LQTK\include\QtCore\qregexp.h \
		..\LinuxQtKit\LQTK\include\QtCore\qstringmatcher.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpair.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmap.h \
		..\LinuxQtKit\LQTK\include\QtCore\qtextstream.h \
		..\LinuxQtKit\LQTK\include\QtCore\qiodevice.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobjectdefs_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcoreevent.h \
		..\LinuxQtKit\LQTK\include\QtCore\qscopedpointer.h \
		..\LinuxQtKit\LQTK\include\QtCore\qmetatype.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvarlengtharray.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontainerfwd.h \
		..\LinuxQtKit\LQTK\include\QtCore\qisenum.h \
		..\LinuxQtKit\LQTK\include\QtCore\qobject_impl.h \
		..\LinuxQtKit\LQTK\include\QtCore\qlocale.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvariant.h \
		..\LinuxQtKit\LQTK\include\QtCore\qshareddata.h \
		..\LinuxQtKit\LQTK\include\QtCore\qvector.h \
		..\LinuxQtKit\LQTK\include\QtCore\qpoint.h \
		..\LinuxQtKit\LQTK\include\QtCore\qset.h \
		..\LinuxQtKit\LQTK\include\QtCore\qcontiguouscache.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o zaux.obj ..\easyhmi_test\zaux.cpp

moc_mainwindow.obj: moc_mainwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o moc_mainwindow.obj moc_mainwindow.cpp

####### Install

install:  FORCE

uninstall:  FORCE

FORCE:

